'use client';

import React, { useRef, useEffect, useMemo } from 'react';
import type { MagneticField } from '../hooks/useMagneticFieldSystem';

interface MagneticFieldRendererProps {
  fields: Array<MagneticField & {
    visualRadius: number;
    visualOpacity: number;
    gradient: string;
    ariaLabel?: string;
  }>;
  interactions: Array<{
    x1: number;
    y1: number;
    x2: number;
    y2: number;
    length: number;
    angle: number;
    opacity: number;
    thickness: number;
    source: string;
    target: string;
    strength: number;
    type: 'particle-field' | 'field-field';
    color: string;
    visible: boolean;
    ariaLabel?: string;
  }>;
  width: number;
  height: number;
  isMobile?: boolean;
  accessibilityMode?: boolean;
  performanceMode?: boolean;
  className?: string;
  onFieldClick?: (field: MagneticField) => void;
  onFieldHover?: (field: MagneticField | null) => void;
}

export const MagneticFieldRenderer: React.FC<MagneticFieldRendererProps> = ({
  fields,
  interactions,
  width,
  height,
  isMobile = false,
  accessibilityMode = false,
  performanceMode = false,
  className = '',
  onFieldClick,
  onFieldHover
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const animationFrameRef = useRef<number | null>(null);

  // Optimize rendering based on device capabilities
  const renderingConfig = useMemo(() => {
    const baseConfig = {
      enableShadows: true,
      enableGradients: true,
      enableAnimations: true,
      maxFieldsToRender: fields.length,
      maxInteractionsToRender: interactions.length,
      renderQuality: 1
    };

    if (performanceMode || isMobile) {
      return {
        ...baseConfig,
        enableShadows: false,
        enableGradients: !isMobile,
        enableAnimations: !performanceMode,
        maxFieldsToRender: Math.min(fields.length, isMobile ? 15 : 30),
        maxInteractionsToRender: Math.min(interactions.length, isMobile ? 5 : 15),
        renderQuality: isMobile ? 0.75 : 0.9
      };
    }

    if (accessibilityMode) {
      return {
        ...baseConfig,
        enableShadows: false,
        enableAnimations: false,
        maxFieldsToRender: Math.min(fields.length, 20),
        maxInteractionsToRender: Math.min(interactions.length, 5)
      };
    }

    return baseConfig;
  }, [fields.length, interactions.length, isMobile, accessibilityMode, performanceMode]);

  // Canvas rendering function
  const renderFrame = useMemo(() => {
    return () => {
      const canvas = canvasRef.current;
      if (!canvas) return;

      const ctx = canvas.getContext('2d');
      if (!ctx) return;

      // Set canvas size with device pixel ratio for crisp rendering
      const devicePixelRatio = window.devicePixelRatio || 1;
      const scaledWidth = width * devicePixelRatio * renderingConfig.renderQuality;
      const scaledHeight = height * devicePixelRatio * renderingConfig.renderQuality;

      canvas.width = scaledWidth;
      canvas.height = scaledHeight;
      canvas.style.width = `${width}px`;
      canvas.style.height = `${height}px`;

      ctx.scale(devicePixelRatio * renderingConfig.renderQuality, devicePixelRatio * renderingConfig.renderQuality);

      // Clear canvas
      ctx.clearRect(0, 0, width, height);

      // Render interactions first (behind fields)
      const visibleInteractions = interactions
        .filter(interaction => interaction.visible)
        .slice(0, renderingConfig.maxInteractionsToRender);

      visibleInteractions.forEach(interaction => {
        if (interaction.opacity <= 0) return;

        ctx.save();
        ctx.globalAlpha = interaction.opacity;
        ctx.strokeStyle = interaction.color;
        ctx.lineWidth = interaction.thickness;

        if (renderingConfig.enableShadows && !accessibilityMode) {
          ctx.shadowColor = interaction.color;
          ctx.shadowBlur = 5;
        }

        ctx.beginPath();
        ctx.moveTo(interaction.x1, interaction.y1);
        ctx.lineTo(interaction.x2, interaction.y2);
        ctx.stroke();

        ctx.restore();
      });

      // Render magnetic fields
      const visibleFields = fields
        .filter(field => field.active && field.visualOpacity > 0)
        .slice(0, renderingConfig.maxFieldsToRender);

      visibleFields.forEach(field => {
        if (field.visualRadius <= 0 || field.visualOpacity <= 0) return;

        ctx.save();
        ctx.globalAlpha = field.visualOpacity;

        // Create gradient for field visualization
        if (renderingConfig.enableGradients) {
          const gradient = ctx.createRadialGradient(
            field.x, field.y, 0,
            field.x, field.y, field.visualRadius
          );

          if (accessibilityMode) {
            // High contrast colors for accessibility
            const centerColor = field.type === 'attract' ? '#0040FF' : '#FF0000';
            gradient.addColorStop(0, centerColor + '80');
            gradient.addColorStop(0.7, centerColor + '40');
            gradient.addColorStop(1, centerColor + '00');
          } else {
            gradient.addColorStop(0, field.color + '80');
            gradient.addColorStop(0.5, field.color + '40');
            gradient.addColorStop(1, field.color + '00');
          }

          ctx.fillStyle = gradient;
        } else {
          // Simple solid color for performance mode
          ctx.fillStyle = field.color + '40';
        }

        // Add shadow effect
        if (renderingConfig.enableShadows && !accessibilityMode) {
          ctx.shadowColor = field.color;
          ctx.shadowBlur = 10;
        }

        // Draw field circle
        ctx.beginPath();
        ctx.arc(field.x, field.y, field.visualRadius, 0, Math.PI * 2);
        ctx.fill();

        // Draw field center point for better visibility
        if (accessibilityMode || field.visualRadius < 30) {
          ctx.shadowBlur = 0;
          ctx.fillStyle = field.color;
          ctx.beginPath();
          ctx.arc(field.x, field.y, 3, 0, Math.PI * 2);
          ctx.fill();
        }

        // Draw field type indicator
        if (!performanceMode && field.visualRadius > 40) {
          ctx.shadowBlur = 0;
          ctx.fillStyle = accessibilityMode ? '#FFFFFF' : field.color;
          ctx.font = `${Math.min(16, field.visualRadius / 3)}px Arial`;
          ctx.textAlign = 'center';
          ctx.textBaseline = 'middle';

          const symbol = field.type === 'attract' ? '+' : '−';
          ctx.fillText(symbol, field.x, field.y);
        }

        ctx.restore();
      });

      // Performance indicator
      if (renderingConfig.maxFieldsToRender < fields.length ||
        renderingConfig.maxInteractionsToRender < interactions.length) {
        ctx.save();
        ctx.fillStyle = 'rgba(255, 255, 0, 0.8)';
        ctx.font = '12px Arial';
        ctx.textAlign = 'left';
        ctx.fillText(
          `Performance Mode: ${visibleFields.length}/${fields.length} fields, ${visibleInteractions.length}/${interactions.length} interactions`,
          10, height - 10
        );
        ctx.restore();
      }
    };
  }, [fields, interactions, width, height, renderingConfig, accessibilityMode, performanceMode]);

  // Animation loop
  useEffect(() => {
    if (!renderingConfig.enableAnimations) {
      renderFrame();
      return;
    }

    const animate = () => {
      renderFrame();
      animationFrameRef.current = requestAnimationFrame(animate);
    };

    animationFrameRef.current = requestAnimationFrame(animate);

    return () => {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }
    };
  }, [renderFrame, renderingConfig.enableAnimations]);

  // Handle canvas interactions
  const handleCanvasClick = (event: React.MouseEvent<HTMLCanvasElement>) => {
    if (!onFieldClick) return;

    const canvas = canvasRef.current;
    if (!canvas) return;

    const rect = canvas.getBoundingClientRect();
    const x = event.clientX - rect.left;
    const y = event.clientY - rect.top;

    // Find clicked field
    const clickedField = fields.find(field => {
      const distance = Math.sqrt((x - field.x) ** 2 + (y - field.y) ** 2);
      return distance <= field.visualRadius;
    });

    if (clickedField) {
      onFieldClick(clickedField);
    }
  };

  const handleCanvasMouseMove = (event: React.MouseEvent<HTMLCanvasElement>) => {
    if (!onFieldHover) return;

    const canvas = canvasRef.current;
    if (!canvas) return;

    const rect = canvas.getBoundingClientRect();
    const x = event.clientX - rect.left;
    const y = event.clientY - rect.top;

    // Find hovered field
    const hoveredField = fields.find(field => {
      const distance = Math.sqrt((x - field.x) ** 2 + (y - field.y) ** 2);
      return distance <= field.visualRadius;
    });

    onFieldHover(hoveredField || null);
  };

  return (
    <div className={`relative ${className}`}>
      <canvas
        ref={canvasRef}
        onClick={handleCanvasClick}
        onMouseMove={handleCanvasMouseMove}
        className="absolute inset-0 pointer-events-auto"
        style={{
          width: `${width}px`,
          height: `${height}px`,
          cursor: onFieldClick ? 'pointer' : 'default'
        }}
        aria-label="Magnetic field visualization"
        role="img"
      />

      {/* Accessibility information */}
      {accessibilityMode && (
        <div className="sr-only" aria-live="polite">
          {fields.length > 0 && (
            <p>
              Magnetic field system active with {fields.length} fields and {interactions.length} interactions.
              {fields.map(field => (
                <span key={field.id}>
                  {field.ariaLabel || `${field.type} field at position ${Math.round(field.x)}, ${Math.round(field.y)}`}
                </span>
              ))}
            </p>
          )}
        </div>
      )}
    </div>
  );
};
