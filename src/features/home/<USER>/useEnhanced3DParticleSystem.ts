// Enhanced 3D Particle System with Shader Effects
import { useState, useEffect, useRef, useMemo, useCallback } from 'react';

export interface Enhanced3DParticle {
        id: string;
        x: number;
        y: number;
        z: number;
        vx: number;
        vy: number;
        vz: number;
        size: number;
        color: string;
        opacity: number;
        rotation: number;
        rotationSpeed: number;
        life: number;
        maxLife: number;
        type: 'spark' | 'energy' | 'magnetic' | 'goal' | 'celebration';
        shader: {
                glow: number;
                shimmer: number;
                distortion: number;
                rainbow: boolean;
        };
        physics: {
                mass: number;
                friction: number;
                gravity: number;
                magnetism: number;
        };
        trail: Array<{ x: number; y: number; z: number; opacity: number }>;
}

interface MagneticField {
        x: number;
        y: number;
        z: number;
        strength: number;
        range: number;
        type: 'attract' | 'repel';
}

interface ParticleSystemConfig {
        maxParticles: number;
        emissionRate: number;
        particleLife: number;
        enablePhysics: boolean;
        enableMagneticFields: boolean;
        enableTrails: boolean;
        shaderQuality: 'low' | 'medium' | 'high' | 'ultra';
        performanceMode: boolean;
}

export const useEnhanced3DParticleSystem = (
        config: ParticleSystemConfig,
        energy: number,
        isActive: boolean = true
) => {
        const [particles, setParticles] = useState<Enhanced3DParticle[]>([]);
        const [magneticFields, setMagneticFields] = useState<MagneticField[]>([]);
        const animationFrameRef = useRef<number | null>(null);
        const lastEmissionRef = useRef<number>(0);
        const performanceMetricsRef = useRef({
                frameCount: 0,
                averageFPS: 0,
                particleCount: 0,
                renderTime: 0
        });

        // Memoized particle generation function
        const createParticle = useCallback((
                x: number,
                y: number,
                type: Enhanced3DParticle['type'] = 'energy'
        ): Enhanced3DParticle => {
                const id = `particle_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

                const baseConfig = {
                        spark: {
                                size: 2 + Math.random() * 3,
                                color: `hsl(${45 + Math.random() * 60}, 100%, ${60 + Math.random() * 40}%)`,
                                life: 500 + Math.random() * 1000,
                                velocity: 0.5 + Math.random() * 2,
                                shader: { glow: 0.8, shimmer: 1, distortion: 0.2, rainbow: false }
                        },
                        energy: {
                                size: 3 + Math.random() * 4,
                                color: `hsl(${220 + Math.random() * 40}, 80%, ${50 + Math.random() * 30}%)`,
                                life: 1000 + Math.random() * 2000,
                                velocity: 0.3 + Math.random() * 1.5,
                                shader: { glow: 1, shimmer: 0.6, distortion: 0.4, rainbow: false }
                        },
                        magnetic: {
                                size: 1 + Math.random() * 2,
                                color: `hsl(${280 + Math.random() * 80}, 90%, ${40 + Math.random() * 40}%)`,
                                life: 2000 + Math.random() * 3000,
                                velocity: 0.1 + Math.random() * 0.8,
                                shader: { glow: 1.2, shimmer: 0.4, distortion: 0.8, rainbow: false }
                        },
                        goal: {
                                size: 5 + Math.random() * 8,
                                color: `hsl(${120 + Math.random() * 60}, 100%, ${60 + Math.random() * 30}%)`,
                                life: 3000 + Math.random() * 2000,
                                velocity: 1 + Math.random() * 3,
                                shader: { glow: 1.5, shimmer: 1.2, distortion: 0.1, rainbow: true }
                        },
                        celebration: {
                                size: 4 + Math.random() * 6,
                                color: `hsl(${Math.random() * 360}, 100%, ${70 + Math.random() * 20}%)`,
                                life: 2000 + Math.random() * 3000,
                                velocity: 0.8 + Math.random() * 2.5,
                                shader: { glow: 1.3, shimmer: 1, distortion: 0.3, rainbow: true }
                        }
                };

                const typeConfig = baseConfig[type];
                const angle = Math.random() * Math.PI * 2;
                const velocity = typeConfig.velocity;

                return {
                        id,
                        x,
                        y,
                        z: Math.random() * 100 - 50,
                        vx: Math.cos(angle) * velocity * (0.5 + Math.random()),
                        vy: Math.sin(angle) * velocity * (0.5 + Math.random()),
                        vz: (Math.random() - 0.5) * velocity * 0.5,
                        size: typeConfig.size,
                        color: typeConfig.color,
                        opacity: 0.8 + Math.random() * 0.2,
                        rotation: Math.random() * 360,
                        rotationSpeed: (Math.random() - 0.5) * 5,
                        life: typeConfig.life,
                        maxLife: typeConfig.life,
                        type,
                        shader: typeConfig.shader,
                        physics: {
                                mass: 0.1 + Math.random() * 0.9,
                                friction: 0.98 + Math.random() * 0.02,
                                gravity: type === 'goal' ? -0.05 : 0.02 + Math.random() * 0.03,
                                magnetism: type === 'magnetic' ? 1 : 0.1 + Math.random() * 0.3
                        },
                        trail: []
                };
        }, []);

        // Advanced particle physics update
        const updateParticle = useCallback((particle: Enhanced3DParticle, deltaTime: number): Enhanced3DParticle => {
                const newParticle = { ...particle };

                // Apply physics
                if (config.enablePhysics) {
                        // Gravity
                        newParticle.vy += newParticle.physics.gravity * deltaTime;

                        // Friction
                        newParticle.vx *= newParticle.physics.friction;
                        newParticle.vy *= newParticle.physics.friction;
                        newParticle.vz *= newParticle.physics.friction;

                        // Magnetic field interactions
                        if (config.enableMagneticFields) {
                                magneticFields.forEach(field => {
                                        const dx = field.x - newParticle.x;
                                        const dy = field.y - newParticle.y;
                                        const dz = field.z - newParticle.z;
                                        const distance = Math.sqrt(dx * dx + dy * dy + dz * dz);

                                        if (distance < field.range && distance > 0) {
                                                const force = field.strength / (distance * distance) * newParticle.physics.magnetism;
                                                const multiplier = field.type === 'attract' ? 1 : -1;

                                                newParticle.vx += (dx / distance) * force * multiplier * deltaTime;
                                                newParticle.vy += (dy / distance) * force * multiplier * deltaTime;
                                                newParticle.vz += (dz / distance) * force * multiplier * deltaTime;
                                        }
                                });
                        }
                }

                // Update position
                newParticle.x += newParticle.vx * deltaTime;
                newParticle.y += newParticle.vy * deltaTime;
                newParticle.z += newParticle.vz * deltaTime;

                // Update rotation
                newParticle.rotation += newParticle.rotationSpeed * deltaTime;

                // Update life and opacity
                newParticle.life -= deltaTime;
                newParticle.opacity = Math.max(0, newParticle.life / newParticle.maxLife);

                // Update trail
                if (config.enableTrails && newParticle.trail.length < 10) {
                        newParticle.trail.push({
                                x: newParticle.x,
                                y: newParticle.y,
                                z: newParticle.z,
                                opacity: newParticle.opacity * 0.5
                        });

                        // Remove old trail points
                        if (newParticle.trail.length > 5) {
                                newParticle.trail.shift();
                        }
                }

                // Enhanced shader effects based on energy
                newParticle.shader.glow *= (1 + energy * 0.2);
                newParticle.shader.shimmer = Math.sin(Date.now() * 0.01 + newParticle.x * 0.1) * 0.5 + 0.5;

                return newParticle;
        }, [config, magneticFields, energy]);

        // Particle emission system
        const emitParticles = useCallback((count: number, centerX: number, centerY: number, type: Enhanced3DParticle['type'] = 'energy') => {
                const newParticles: Enhanced3DParticle[] = [];

                for (let i = 0; i < count; i++) {
                        const particle = createParticle(
                                centerX + (Math.random() - 0.5) * 20,
                                centerY + (Math.random() - 0.5) * 20,
                                type
                        );
                        newParticles.push(particle);
                }

                setParticles(current => {
                        const combined = [...current, ...newParticles];
                        // Limit particles for performance
                        return combined.slice(-config.maxParticles);
                });
        }, [createParticle, config.maxParticles]);

        // Goal celebration effect
        const triggerGoalCelebration = useCallback((centerX: number, centerY: number) => {
                // Create burst of celebration particles
                for (let i = 0; i < 50; i++) {
                        setTimeout(() => {
                                emitParticles(5, centerX, centerY, 'celebration');
                        }, i * 50);
                }

                // Add temporary magnetic fields for dramatic effect
                const celebrationFields: MagneticField[] = [
                        { x: centerX - 50, y: centerY, z: 0, strength: 200, range: 100, type: 'attract' },
                        { x: centerX + 50, y: centerY, z: 0, strength: 200, range: 100, type: 'attract' },
                        { x: centerX, y: centerY - 50, z: 0, strength: 150, range: 80, type: 'repel' },
                        { x: centerX, y: centerY + 50, z: 0, strength: 150, range: 80, type: 'repel' }
                ];

                setMagneticFields(current => [...current, ...celebrationFields]);

                // Remove celebration fields after effect
                setTimeout(() => {
                        setMagneticFields(current =>
                                current.filter(field => !celebrationFields.includes(field))
                        );
                }, 3000);
        }, [emitParticles]);

        // Animation loop
        useEffect(() => {
                if (!isActive) return;

                let lastTime = Date.now();

                const animate = () => {
                        const currentTime = Date.now();
                        const deltaTime = Math.min(currentTime - lastTime, 50); // Cap delta time
                        lastTime = currentTime;

                        const renderStart = performance.now();

                        // Update particles
                        setParticles(currentParticles => {
                                const updatedParticles = currentParticles
                                        .map(particle => updateParticle(particle, deltaTime))
                                        .filter(particle => particle.life > 0);

                                // Emit new particles based on energy and emission rate
                                if (currentTime - lastEmissionRef.current > 1000 / config.emissionRate) {
                                        const emissionCount = Math.floor(1 + energy * 3);
                                        const newParticles: Enhanced3DParticle[] = [];

                                        for (let i = 0; i < emissionCount; i++) {
                                                const width = typeof window !== 'undefined' ? window.innerWidth : 800;
                                                const height = typeof window !== 'undefined' ? window.innerHeight : 600;
                                                newParticles.push(createParticle(
                                                        50 + Math.random() * width - 100,
                                                        50 + Math.random() * height - 100,
                                                        Math.random() > 0.7 ? 'spark' : 'energy'
                                                ));
                                        }

                                        lastEmissionRef.current = currentTime;
                                        return [...updatedParticles, ...newParticles].slice(-config.maxParticles);
                                }

                                return updatedParticles;
                        });

                        // Update performance metrics
                        const renderTime = performance.now() - renderStart;
                        performanceMetricsRef.current.frameCount++;
                        performanceMetricsRef.current.renderTime = renderTime;
                        performanceMetricsRef.current.particleCount = particles.length;

                        animationFrameRef.current = requestAnimationFrame(animate);
                };

                animationFrameRef.current = requestAnimationFrame(animate);

                return () => {
                        if (animationFrameRef.current) {
                                cancelAnimationFrame(animationFrameRef.current);
                        }
                };
        }, [isActive, config, updateParticle, createParticle, energy, particles.length]);

        // Memoized particle rendering data
        const particleRenderData = useMemo(() => {
                return particles.map(particle => ({
                        ...particle,
                        transform: `translate3d(${particle.x}px, ${particle.y}px, ${particle.z}px) rotate(${particle.rotation}deg)`,
                        boxShadow: `0 0 ${particle.shader.glow * 10}px ${particle.color}`,
                        filter: `brightness(${1 + particle.shader.shimmer}) blur(${particle.shader.distortion}px)`,
                        background: particle.shader.rainbow
                                ? `conic-gradient(from ${particle.rotation}deg, #ff0000, #ff8000, #ffff00, #80ff00, #00ff00, #00ff80, #00ffff, #0080ff, #0000ff, #8000ff, #ff00ff, #ff0080, #ff0000)`
                                : particle.color
                }));
        }, [particles]);

        return {
                particles: particleRenderData,
                magneticFields,
                emitParticles,
                triggerGoalCelebration,
                setMagneticFields,
                performanceMetrics: performanceMetricsRef.current
        };
};
