'use client';

import React from 'react';
import { DatePicker } from './DatePicker';
import { LeagueSelector } from './LeagueSelector';
import { SeasonFilter } from './SeasonFilter';

interface League {
  id: string;
  name: string;
  logo?: string;
  country?: string;
}

interface Season {
  id: string;
  name: string;
  year: string;
}

interface FilterBarProps {
  selectedDate: string | null;
  selectedLeague: string | null;
  selectedSeason: string | null;
  leagues: League[];
  seasons: Season[];
  onDateChange: (date: string | null) => void;
  onLeagueChange: (league: string | null) => void;
  onSeasonChange: (season: string | null) => void;
  onReset: () => void;
  isLoading?: boolean;
}

export const FilterBar: React.FC<FilterBarProps> = ({
  selectedDate,
  selectedLeague,
  selectedSeason,
  leagues,
  seasons,
  onDateChange,
  onLeagueChange,
  onSeasonChange,
  onReset,
  isLoading = false
}) => {
  // Check if any filters are active
  const hasActiveFilters = Boolean(
    selectedLeague ||
    selectedSeason ||
    (selectedDate && selectedDate !== new Date().toISOString().split('T')[0])
  );

  return (
    <div className="bg-white rounded-lg border border-gray-200 p-4">
      {/* Simple Header */}
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-sm font-medium text-gray-700">Filter Fixtures</h3>
        {hasActiveFilters && (
          <button
            onClick={onReset}
            disabled={isLoading}
            className="text-xs text-gray-500 hover:text-gray-700 disabled:opacity-50"
          >
            Reset
          </button>
        )}
      </div>

      {/* Simple Filter Controls */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {/* Date Filter */}
        <div>
          <label className="block text-xs font-medium text-gray-600 mb-2">Date</label>
          <DatePicker
            selectedDate={selectedDate}
            onDateChange={onDateChange}
            disabled={isLoading}
          />
        </div>

        {/* League Filter */}
        <div>
          <label className="block text-xs font-medium text-gray-600 mb-2">League</label>
          <LeagueSelector
            selectedLeague={selectedLeague}
            leagues={leagues}
            onLeagueChange={onLeagueChange}
            disabled={isLoading}
          />
        </div>

        {/* Season Filter */}
        <div>
          <label className="block text-xs font-medium text-gray-600 mb-2">Season</label>
          <SeasonFilter
            selectedSeason={selectedSeason}
            seasons={seasons}
            onSeasonChange={onSeasonChange}
            disabled={isLoading}
          />
        </div>
      </div>

      {/* Loading Indicator */}
      {isLoading && (
        <div className="mt-3 text-center">
          <div className="text-xs text-gray-500">Loading...</div>
        </div>
      )}
    </div>
  );
};
