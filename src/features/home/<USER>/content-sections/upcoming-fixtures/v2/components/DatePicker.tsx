'use client';

import React, { useState } from 'react';

interface DatePickerProps {
  selectedDate: string | null;
  onDateChange: (date: string | null) => void;
  disabled?: boolean;
}

export const DatePicker: React.FC<DatePickerProps> = ({
  selectedDate,
  onDateChange,
  disabled = false
}) => {
  const [showCustomPicker, setShowCustomPicker] = useState(false);

  // Get today and tomorrow dates
  const today = new Date().toISOString().split('T')[0];
  const tomorrow = new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString().split('T')[0];

  // Quick date options
  const quickOptions = [
    {
      id: 'today',
      label: 'Today',
      value: today,
      icon: '📅',
      description: new Date().toLocaleDateString('en-US', { weekday: 'short', month: 'short', day: 'numeric' })
    },
    {
      id: 'tomorrow',
      label: 'Tomorrow',
      value: tomorrow,
      icon: '📆',
      description: new Date(tomorrow).toLocaleDateString('en-US', { weekday: 'short', month: 'short', day: 'numeric' })
    },
    {
      id: 'custom',
      label: 'Custom',
      value: null,
      icon: '🗓️',
      description: 'Pick any date'
    }
  ];

  const handleQuickOptionClick = (option: typeof quickOptions[0]) => {
    if (option.id === 'custom') {
      setShowCustomPicker(true);
    } else {
      onDateChange(option.value);
      setShowCustomPicker(false);
    }
  };

  const handleCustomDateChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const newDate = event.target.value;
    onDateChange(newDate);
    setShowCustomPicker(false);
  };

  // Determine which option is currently selected
  const getSelectedOption = () => {
    if (selectedDate === today) return 'today';
    if (selectedDate === tomorrow) return 'tomorrow';
    if (selectedDate) return 'custom';
    return null;
  };

  const selectedOption = getSelectedOption();

  return (
    <div>
      {/* Simple Quick Options */}
      <div className="grid grid-cols-3 gap-2">
        {quickOptions.map((option) => {
          const isSelected = selectedOption === option.id ||
            (option.id === 'custom' && selectedDate && selectedDate !== today && selectedDate !== tomorrow);

          return (
            <button
              key={option.id}
              onClick={() => handleQuickOptionClick(option)}
              disabled={disabled}
              className={`
                p-2 rounded border text-center text-xs transition-colors
                ${isSelected
                  ? 'border-blue-500 bg-blue-50 text-blue-700'
                  : 'border-gray-200 bg-white text-gray-600 hover:border-blue-300'
                }
                ${disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
              `}
            >
              <div className="font-medium">{option.label}</div>
            </button>
          );
        })}
      </div>

      {/* Custom Date Picker */}
      {showCustomPicker && (
        <div className="mt-2">
          <input
            type="date"
            value={selectedDate || ''}
            onChange={handleCustomDateChange}
            disabled={disabled}
            className="w-full px-2 py-1 text-xs border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-blue-500 disabled:opacity-50"
          />
        </div>
      )}
    </div>
  );
};
