'use client';

import React, { useState, useRef, useEffect } from 'react';

interface League {
  id: string;
  name: string;
  logo?: string;
  country?: string;
}

interface LeagueSelectorProps {
  selectedLeague: string | null;
  leagues: League[];
  onLeagueChange: (league: string | null) => void;
  disabled?: boolean;
}

export const LeagueSelector: React.FC<LeagueSelectorProps> = ({
  selectedLeague,
  leagues,
  onLeagueChange,
  disabled = false
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Get selected league object
  const selectedLeagueObj = leagues.find(league => league.id === selectedLeague);

  const handleLeagueSelect = (league: League | null) => {
    onLeagueChange(league?.id || null);
    setIsOpen(false);
  };

  const toggleDropdown = () => {
    if (!disabled) {
      setIsOpen(!isOpen);
    }
  };

  return (
    <div className="relative" ref={dropdownRef}>
      {/* Simple Trigger Button */}
      <button
        onClick={toggleDropdown}
        disabled={disabled}
        className={`
          w-full flex items-center justify-between px-3 py-2 bg-white border rounded text-xs transition-colors
          ${isOpen ? 'border-blue-500' : 'border-gray-200 hover:border-blue-300'}
          ${disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
        `}
      >
        <span className="text-gray-700 truncate">
          {selectedLeagueObj ? selectedLeagueObj.name : 'All leagues'}
        </span>
        <span className={`text-gray-400 ml-2 ${isOpen ? 'rotate-180' : ''}`}>▼</span>
      </button>

      {/* Simple Dropdown */}
      {isOpen && (
        <div className="absolute top-full left-0 right-0 mt-1 bg-white border border-gray-200 rounded shadow-lg z-50 max-h-48 overflow-y-auto">
          {/* Clear Option */}
          <button
            onClick={() => handleLeagueSelect(null)}
            className="w-full px-3 py-2 text-left text-xs hover:bg-gray-50 border-b border-gray-100"
          >
            All leagues
          </button>

          {/* League Options */}
          {leagues.map((league) => (
            <button
              key={league.id}
              onClick={() => handleLeagueSelect(league)}
              className={`
                w-full px-3 py-2 text-left text-xs hover:bg-blue-50 transition-colors
                ${selectedLeague === league.id ? 'bg-blue-50 text-blue-700' : 'text-gray-700'}
              `}
            >
              {league.name}
            </button>
          ))}
        </div>
      )}
    </div>
  );
};
