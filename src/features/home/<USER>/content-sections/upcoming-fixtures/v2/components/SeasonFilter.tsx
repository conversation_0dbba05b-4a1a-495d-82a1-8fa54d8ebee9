'use client';

import React, { useState, useRef, useEffect } from 'react';

interface Season {
  id: string;
  name: string;
  year: string;
}

interface SeasonFilterProps {
  selectedSeason: string | null;
  seasons: Season[];
  onSeasonChange: (season: string | null) => void;
  disabled?: boolean;
}

export const SeasonFilter: React.FC<SeasonFilterProps> = ({
  selectedSeason,
  seasons,
  onSeasonChange,
  disabled = false
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Get selected season object
  const selectedSeasonObj = seasons.find(season => season.id === selectedSeason);

  const handleSeasonSelect = (season: Season | null) => {
    onSeasonChange(season?.id || null);
    setIsOpen(false);
  };

  const toggleDropdown = () => {
    if (!disabled) {
      setIsOpen(!isOpen);
    }
  };

  return (
    <div className="relative" ref={dropdownRef}>
      {/* Simple Trigger Button */}
      <button
        onClick={toggleDropdown}
        disabled={disabled}
        className={`
          w-full flex items-center justify-between px-3 py-2 bg-white border rounded text-xs transition-colors
          ${isOpen ? 'border-blue-500' : 'border-gray-200 hover:border-blue-300'}
          ${disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
        `}
      >
        <span className="text-gray-700 truncate">
          {selectedSeasonObj ? selectedSeasonObj.name : 'All seasons'}
        </span>
        <span className={`text-gray-400 ml-2 ${isOpen ? 'rotate-180' : ''}`}>▼</span>
      </button>

      {/* Simple Dropdown */}
      {isOpen && (
        <div className="absolute top-full left-0 right-0 mt-1 bg-white border border-gray-200 rounded shadow-lg z-50 max-h-48 overflow-y-auto">
          {/* Clear Option */}
          <button
            onClick={() => handleSeasonSelect(null)}
            className="w-full px-3 py-2 text-left text-xs hover:bg-gray-50 border-b border-gray-100"
          >
            All seasons
          </button>

          {/* Season Options */}
          {seasons.map((season) => (
            <button
              key={season.id}
              onClick={() => handleSeasonSelect(season)}
              className={`
                w-full px-3 py-2 text-left text-xs hover:bg-blue-50 transition-colors
                ${selectedSeason === season.id ? 'bg-blue-50 text-blue-700' : 'text-gray-700'}
              `}
            >
              {season.name}
            </button>
          ))}
        </div>
      )}
    </div>
  );
};
