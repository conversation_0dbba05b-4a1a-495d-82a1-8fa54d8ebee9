'use client';

import React, { useState } from 'react';
import { UpcomingFixturesProps } from '../types';
import { FilterBar } from './components/FilterBar';
import { FixturesGrid } from './components/FixturesGrid';
import { Pagination } from './components/Pagination';
import { useFixturesV2 } from './hooks/useFixturesV2';
import { useFilters } from './hooks/useFilters';

const UpcomingFixturesV2: React.FC<UpcomingFixturesProps> = ({
  className = ''
}) => {
  // Filter state management
  const {
    selectedDate,
    selectedLeague,
    selectedSeason,
    setSelectedDate,
    setSelectedLeague,
    setSelectedSeason,
    resetFilters
  } = useFilters();

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize] = useState(20);

  // Data fetching with V2 optimizations
  const {
    fixtures,
    leagues,
    seasons,
    isLoading,
    error,
    totalItems,
    totalPages,
    refetch
  } = useFixturesV2({
    date: selectedDate,
    league: selectedLeague,
    season: selectedSeason,
    page: currentPage,
    limit: pageSize
  });

  // Handle page change
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    // Smooth scroll to top
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  // Handle filter changes
  const handleFilterChange = () => {
    setCurrentPage(1); // Reset to first page when filters change
  };

  return (
    <section className={`py-8 ${className}`}>
      <div className="container mx-auto px-4 max-w-7xl">
        {/* Simple Header */}
        <div className="mb-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-2">
            Fixtures Schedule
          </h2>
        </div>

        {/* Filter Bar */}
        <div className="mb-8">
          <FilterBar
            selectedDate={selectedDate}
            selectedLeague={selectedLeague}
            selectedSeason={selectedSeason}
            leagues={leagues}
            seasons={seasons}
            onDateChange={(date) => {
              setSelectedDate(date);
              handleFilterChange();
            }}
            onLeagueChange={(league) => {
              setSelectedLeague(league);
              handleFilterChange();
            }}
            onSeasonChange={(season) => {
              setSelectedSeason(season);
              handleFilterChange();
            }}
            onReset={() => {
              resetFilters();
              handleFilterChange();
            }}
            isLoading={isLoading}
          />
        </div>

        {/* Error State */}
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-xl p-6 mb-8">
            <div className="flex items-center space-x-3">
              <div className="text-red-500 text-xl">⚠️</div>
              <div>
                <h3 className="text-red-800 font-semibold">Error Loading Fixtures</h3>
                <p className="text-red-600 text-sm mt-1">{error}</p>
                <button
                  onClick={refetch}
                  className="mt-3 px-4 py-2 bg-red-100 hover:bg-red-200 text-red-700 rounded-lg text-sm font-medium transition-colors"
                >
                  Try Again
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Fixtures Grid */}
        <div className="mb-8">
          <FixturesGrid
            fixtures={fixtures}
            isLoading={isLoading}
            emptyMessage={
              selectedDate || selectedLeague || selectedSeason
                ? "No fixtures found for the selected filters"
                : "No fixtures available"
            }
          />
        </div>

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="flex justify-center">
            <Pagination
              currentPage={currentPage}
              totalPages={totalPages}
              totalItems={totalItems}
              pageSize={pageSize}
              onPageChange={handlePageChange}
              isLoading={isLoading}
            />
          </div>
        )}

        {/* Stats Footer */}
        {fixtures.length > 0 && (
          <div className="mt-12 text-center">
            <div className="inline-flex items-center space-x-6 bg-white/80 backdrop-blur-sm rounded-full px-6 py-3 border border-gray-200">
              <div className="flex items-center space-x-2">
                <span className="text-blue-500">📊</span>
                <span className="text-sm text-gray-600">
                  Showing {fixtures.length} of {totalItems} fixtures
                </span>
              </div>
              {selectedDate && (
                <div className="flex items-center space-x-2">
                  <span className="text-purple-500">📅</span>
                  <span className="text-sm text-gray-600">
                    {new Date(selectedDate).toLocaleDateString('en-US', {
                      weekday: 'long',
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric'
                    })}
                  </span>
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </section>
  );
};

export default UpcomingFixturesV2;
