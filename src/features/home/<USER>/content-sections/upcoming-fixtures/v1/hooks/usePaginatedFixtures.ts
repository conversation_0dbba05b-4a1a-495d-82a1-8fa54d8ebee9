import { useState, useEffect, useCallback, useRef } from 'react';
import { ProcessedFixture } from '../../types';

interface UsePaginatedFixturesProps {
  selectedDate?: string;
  refreshInterval?: number;
  enableRealTime?: boolean;
  initialPageSize?: number;
}

interface PaginationMeta {
  totalItems: number;
  totalPages: number;
  currentPage: number;
  limit: number;
}

interface UsePaginatedFixturesReturn {
  fixtures: ProcessedFixture[];
  isLoading: boolean;
  error: string | null;
  refetch: () => void;
  lastUpdated: Date | null;
  isRealTimeActive: boolean;
  toggleRealTime: () => void;
  // Pagination
  currentPage: number;
  totalPages: number;
  totalItems: number;
  pageSize: number;
  setPage: (page: number) => void;
  setPageSize: (size: number) => void;
  hasNextPage: boolean;
  hasPrevPage: boolean;
}

export const usePaginatedFixtures = ({
  selectedDate,
  refreshInterval = 30000,
  enableRealTime = true,
  initialPageSize = 20
}: UsePaginatedFixturesProps): UsePaginatedFixturesReturn => {
  const [fixtures, setFixtures] = useState<ProcessedFixture[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);
  const [isRealTimeActive, setIsRealTimeActive] = useState(enableRealTime);
  
  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(initialPageSize);
  const [paginationMeta, setPaginationMeta] = useState<PaginationMeta>({
    totalItems: 0,
    totalPages: 0,
    currentPage: 1,
    limit: initialPageSize
  });
  
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const abortControllerRef = useRef<AbortController | null>(null);

  // Process API fixture to our format
  const processApiFixture = (apiFixture: any): ProcessedFixture => {
    const extractFlag = (flag: any): string => {
      if (typeof flag === 'string') return flag;
      if (flag && typeof flag === 'object' && flag.flag) return flag.flag;
      return '🏳️';
    };

    const extractVenue = (venue: any): string => {
      if (typeof venue === 'string') return venue;
      if (venue && typeof venue === 'object') {
        return venue.name || venue.city || 'Unknown Venue';
      }
      return 'Unknown Venue';
    };

    const extractRound = (round: any): string => {
      if (typeof round === 'string') return round;
      if (round && typeof round === 'object' && round.name) return round.name;
      return 'Regular Season';
    };

    let status = apiFixture.status || 'NS';
    if (apiFixture.computedStatus) {
      status = apiFixture.computedStatus;
    }

    return {
      id: apiFixture.id,
      externalId: apiFixture.externalId,
      slug: apiFixture.slug,
      homeTeam: apiFixture.homeTeamName,
      awayTeam: apiFixture.awayTeamName,
      homeScore: apiFixture.goalsHome,
      awayScore: apiFixture.goalsAway,
      status,
      minute: apiFixture.elapsed ? `${apiFixture.elapsed}'` : undefined,
      date: apiFixture.date,
      competition: apiFixture.leagueName,
      homeFlag: extractFlag(apiFixture.homeTeamFlag),
      awayFlag: extractFlag(apiFixture.awayTeamFlag),
      homeLogo: apiFixture.homeTeamLogo,
      awayLogo: apiFixture.awayTeamLogo,
      venue: extractVenue(apiFixture.venue),
      round: extractRound(apiFixture.round),
      isHot: apiFixture.isHot || false,
      isTrending: apiFixture.isTrending || false,
      isVip: apiFixture.isVip || false,
    };
  };

  const fetchFixtures = useCallback(async (showLoading = true) => {
    try {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }

      abortControllerRef.current = new AbortController();

      if (showLoading) {
        setIsLoading(true);
      }
      setError(null);

      // Build query parameters with pagination
      const params = new URLSearchParams();
      params.append('page', currentPage.toString());
      params.append('limit', pageSize.toString());

      if (selectedDate) {
        params.append('date', selectedDate);
        console.log('🗓️ Backend filtering by date:', selectedDate);
      }

      const url = `/api/football/fixtures?${params.toString()}`;
      console.log('🔄 Fetching paginated fixtures:', url, `(Page ${currentPage}/${paginationMeta.totalPages || '?'})`);

      const response = await fetch(url, {
        signal: abortControllerRef.current.signal
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      if (data.status !== 200) {
        throw new Error(`API error: Status ${data.status}`);
      }

      if (!data.data || !Array.isArray(data.data)) {
        throw new Error('Invalid API response: data.data is not an array');
      }

      const processedFixtures = data.data.map(processApiFixture);

      // Update pagination meta
      if (data.meta) {
        setPaginationMeta({
          totalItems: data.meta.totalItems || 0,
          totalPages: data.meta.totalPages || 0,
          currentPage: data.meta.currentPage || currentPage,
          limit: data.meta.limit || pageSize
        });
      }

      setFixtures(processedFixtures);
      setLastUpdated(new Date());
      
      console.log('✅ Paginated fixtures updated:', processedFixtures.length, 
        `(Page ${currentPage}/${data.meta?.totalPages || '?'}, Total: ${data.meta?.totalItems || '?'})`);

    } catch (err) {
      if (err instanceof Error && err.name === 'AbortError') {
        console.log('🚫 Request aborted');
        return;
      }
      
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch fixtures';
      console.error('❌ Error fetching fixtures:', errorMessage);
      setError(errorMessage);
    } finally {
      if (showLoading) {
        setIsLoading(false);
      }
    }
  }, [selectedDate, currentPage, pageSize, paginationMeta.totalPages]);

  // Page navigation functions
  const setPage = useCallback((page: number) => {
    if (page >= 1 && page <= paginationMeta.totalPages) {
      setCurrentPage(page);
    }
  }, [paginationMeta.totalPages]);

  const handlePageSizeChange = useCallback((size: number) => {
    setPageSize(size);
    setCurrentPage(1); // Reset to first page when changing page size
  }, []);

  // Manual refetch
  const refetch = useCallback(() => {
    fetchFixtures(true);
  }, [fetchFixtures]);

  // Toggle real-time updates
  const toggleRealTime = useCallback(() => {
    setIsRealTimeActive(prev => !prev);
  }, []);

  // Setup real-time polling
  useEffect(() => {
    // Initial fetch
    fetchFixtures(true);

    // Setup interval for real-time updates
    if (isRealTimeActive && refreshInterval > 0) {
      intervalRef.current = setInterval(() => {
        fetchFixtures(false); // Don't show loading for background updates
      }, refreshInterval);

      console.log(`🔄 Real-time updates enabled (${refreshInterval}ms interval)`);
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, [fetchFixtures, isRealTimeActive, refreshInterval]);

  // Reset page when date changes
  useEffect(() => {
    setCurrentPage(1);
  }, [selectedDate]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, []);

  return {
    fixtures,
    isLoading,
    error,
    refetch,
    lastUpdated,
    isRealTimeActive,
    toggleRealTime,
    // Pagination
    currentPage,
    totalPages: paginationMeta.totalPages,
    totalItems: paginationMeta.totalItems,
    pageSize,
    setPage,
    setPageSize: handlePageSizeChange,
    hasNextPage: currentPage < paginationMeta.totalPages,
    hasPrevPage: currentPage > 1
  };
};
