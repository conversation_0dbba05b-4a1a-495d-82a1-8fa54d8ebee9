import { useState, useEffect, useCallback, useRef } from 'react';
import { ProcessedFixture } from '../../types';

interface UseRealTimeFixturesProps {
  selectedDate?: string;
  selectedLeague?: string;
  selectedLeagueId?: number;
  refreshInterval?: number; // in milliseconds
  enableRealTime?: boolean;
}

interface UseRealTimeFixturesReturn {
  fixtures: ProcessedFixture[];
  isLoading: boolean;
  error: string | null;
  refetch: () => void;
  lastUpdated: Date | null;
  isRealTimeActive: boolean;
  toggleRealTime: () => void;
}

export const useRealTimeFixtures = ({
  selectedDate,
  selectedLeague,
  selectedLeagueId,
  refreshInterval = 30000, // 30 seconds default
  enableRealTime = true
}: UseRealTimeFixturesProps): UseRealTimeFixturesReturn => {
  const [fixtures, setFixtures] = useState<ProcessedFixture[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);
  const [isRealTimeActive, setIsRealTimeActive] = useState(enableRealTime);

  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const abortControllerRef = useRef<AbortController | null>(null);

  // Process API fixture to our format
  const processApiFixture = (apiFixture: any): ProcessedFixture => {
    const extractFlag = (flag: any): string => {
      if (typeof flag === 'string') return flag;
      if (flag && typeof flag === 'object' && flag.flag) return flag.flag;
      return '🏳️';
    };

    const extractVenue = (venue: any): string => {
      if (typeof venue === 'string') return venue;
      if (venue && typeof venue === 'object') {
        return venue.name || venue.city || 'Unknown Venue';
      }
      return 'Unknown Venue';
    };

    const extractRound = (round: any): string => {
      if (typeof round === 'string') return round;
      if (round && typeof round === 'object' && round.name) return round.name;
      return 'Regular Season';
    };

    // Enhanced status logic
    let status = apiFixture.status || 'NS';
    if (apiFixture.computedStatus) {
      status = apiFixture.computedStatus;
    }

    return {
      id: apiFixture.id,
      externalId: apiFixture.externalId,
      slug: apiFixture.slug,
      homeTeam: apiFixture.homeTeamName,
      awayTeam: apiFixture.awayTeamName,
      homeScore: apiFixture.goalsHome,
      awayScore: apiFixture.goalsAway,
      status,
      minute: apiFixture.elapsed ? `${apiFixture.elapsed}'` : undefined,
      date: apiFixture.date,
      competition: apiFixture.leagueName,
      homeFlag: extractFlag(apiFixture.homeTeamFlag),
      awayFlag: extractFlag(apiFixture.awayTeamFlag),
      homeLogo: apiFixture.homeTeamLogo,
      awayLogo: apiFixture.awayTeamLogo,
      venue: extractVenue(apiFixture.venue),
      round: extractRound(apiFixture.round),
      isHot: apiFixture.isHot || false,
      isTrending: apiFixture.isTrending || false,
      isVip: apiFixture.isVip || false,
    };
  };

  const fetchFixtures = useCallback(async (showLoading = true) => {
    try {
      // Cancel previous request
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }

      // Create new abort controller
      abortControllerRef.current = new AbortController();

      if (showLoading) {
        setIsLoading(true);
      }
      setError(null);

      // Build query parameters with enhanced backend filtering
      const params = new URLSearchParams();
      params.append('page', '1');
      params.append('limit', '50');

      // Use backend filtering when possible
      if (selectedDate) {
        params.append('date', selectedDate);
        console.log('🗓️ Backend filtering by date:', selectedDate);
      }

      if (selectedLeagueId) {
        params.append('leagueId', selectedLeagueId.toString());
        console.log('🏆 Backend filtering by leagueId:', selectedLeagueId);
      } else if (selectedLeague) {
        // Fallback to legacy league name filtering
        params.append('league', selectedLeague);
        console.log('⚠️ Legacy filtering by league name:', selectedLeague);
      }

      const url = `/api/football/fixtures?${params.toString()}`;
      console.log('🔄 Real-time fetching fixtures:', url);

      const response = await fetch(url, {
        signal: abortControllerRef.current.signal
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      if (data.status !== 200) {
        throw new Error(`API error: Status ${data.status}`);
      }

      if (!data.data || !Array.isArray(data.data)) {
        throw new Error('Invalid API response: data.data is not an array');
      }

      let processedFixtures = data.data.map(processApiFixture);

      // Note: Backend filtering is now handled by the API proxy
      // Only minimal client-side filtering needed for edge cases
      console.log('✅ Backend-filtered fixtures received:', processedFixtures.length);

      setFixtures(processedFixtures);
      setLastUpdated(new Date());

      console.log('✅ Real-time fixtures updated:', processedFixtures.length);

    } catch (err) {
      if (err instanceof Error && err.name === 'AbortError') {
        console.log('🚫 Request aborted');
        return;
      }

      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch fixtures';
      console.error('❌ Error fetching fixtures:', errorMessage);
      setError(errorMessage);
    } finally {
      if (showLoading) {
        setIsLoading(false);
      }
    }
  }, [selectedDate, selectedLeague, selectedLeagueId]);

  // Manual refetch
  const refetch = useCallback(() => {
    fetchFixtures(true);
  }, [fetchFixtures]);

  // Toggle real-time updates
  const toggleRealTime = useCallback(() => {
    setIsRealTimeActive(prev => !prev);
  }, []);

  // Setup real-time polling
  useEffect(() => {
    // Initial fetch
    fetchFixtures(true);

    // Setup interval for real-time updates
    if (isRealTimeActive && refreshInterval > 0) {
      intervalRef.current = setInterval(() => {
        fetchFixtures(false); // Don't show loading for background updates
      }, refreshInterval);

      console.log(`🔄 Real-time updates enabled (${refreshInterval}ms interval)`);
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, [fetchFixtures, isRealTimeActive, refreshInterval]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, []);

  return {
    fixtures,
    isLoading,
    error,
    refetch,
    lastUpdated,
    isRealTimeActive,
    toggleRealTime
  };
};
