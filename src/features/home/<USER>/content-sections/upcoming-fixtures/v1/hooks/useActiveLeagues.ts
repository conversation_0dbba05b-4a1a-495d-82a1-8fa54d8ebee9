import { useState, useEffect, useCallback } from 'react';

export interface League {
  id: number;
  name: string;
  logo?: string;
  country: string;
  season: string;
  isActive: boolean;
  externalId?: number;
}

export interface EnhancedLeague extends League {
  fixtureCount?: number;
  lastUpdated?: string;
  priority?: number;
}

interface ApiResponse {
  status: number;
  data: League[];
  meta?: {
    totalItems: number;
    currentPage: number;
    totalPages: number;
  };
}

// Helper function to build CDN image URL for leagues
const buildLeagueCDNUrl = (path?: string): string | undefined => {
  if (!path) return undefined;

  const cdnDomain = process.env.NEXT_PUBLIC_CDN_DOMAIN_PICTURE;
  if (!cdnDomain) {
    console.warn('⚠️ NEXT_PUBLIC_CDN_DOMAIN_PICTURE not configured');
    return path;
  }

  if (path.startsWith('http')) return path;

  const cdnUrl = `${cdnDomain}/${path.startsWith('/') ? path.slice(1) : path}`;
  console.log('🏆 Built League CDN URL:', path, '→', cdnUrl);
  return cdnUrl;
};

// Helper function to generate premium league logo fallback
const generatePremiumLeagueLogo = (leagueName: string, country: string): string => {
  const initials = leagueName.split(' ').map(word => word.charAt(0)).join('').substring(0, 2).toUpperCase();

  // Premium color schemes based on league prestige
  const premiumColors = {
    'Premier League': { bg: '#37003c', accent: '#00ff87' },
    'La Liga': { bg: '#ff6900', accent: '#ffffff' },
    'Serie A': { bg: '#0066cc', accent: '#ffffff' },
    'Bundesliga': { bg: '#d20515', accent: '#ffffff' },
    'Ligue 1': { bg: '#1e3a8a', accent: '#fbbf24' },
    'Champions League': { bg: '#0c1e3e', accent: '#1e40af' },
    'Europa League': { bg: '#ff6900', accent: '#ffffff' }
  };

  const colorScheme = premiumColors[leagueName as keyof typeof premiumColors] ||
    { bg: '#1f2937', accent: '#60a5fa' };

  return `data:image/svg+xml,${encodeURIComponent(`
    <svg width="48" height="48" xmlns="http://www.w3.org/2000/svg">
      <defs>
        <linearGradient id="grad" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" style="stop-color:${colorScheme.bg};stop-opacity:1" />
          <stop offset="100%" style="stop-color:${colorScheme.bg}dd;stop-opacity:1" />
        </linearGradient>
        <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
          <feDropShadow dx="0" dy="2" stdDeviation="3" flood-color="#00000020"/>
        </filter>
      </defs>
      <circle cx="24" cy="24" r="22" fill="url(#grad)" stroke="${colorScheme.accent}" stroke-width="2" filter="url(#shadow)"/>
      <text x="24" y="30" text-anchor="middle" fill="${colorScheme.accent}" font-family="Arial" font-size="12" font-weight="bold">
        ${initials}
      </text>
      <circle cx="24" cy="24" r="18" fill="none" stroke="${colorScheme.accent}40" stroke-width="1"/>
    </svg>
  `)}`;
};

export const useActiveLeagues = () => {
  const [leagues, setLeagues] = useState<League[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchLeagues = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);

      console.log('🏆 Fetching active leagues...');

      const response = await fetch('/api/football/leagues?active=true');

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data: ApiResponse = await response.json();

      if (data.status !== 200) {
        throw new Error(`API error: Status ${data.status}`);
      }

      if (!data.data || !Array.isArray(data.data)) {
        throw new Error('Invalid API response: data.data is not an array');
      }

      // Process leagues with CDN URLs and fallbacks
      const processedLeagues = data.data.map(league => ({
        ...league,
        logo: buildLeagueCDNUrl(league.logo) || generatePremiumLeagueLogo(league.name, league.country)
      }));

      console.log('✅ Active leagues fetched successfully:', processedLeagues.length);
      setLeagues(processedLeagues);

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch leagues';
      console.error('❌ Error fetching leagues:', errorMessage);
      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchLeagues();
  }, [fetchLeagues]);

  const refetch = useCallback(() => {
    fetchLeagues();
  }, [fetchLeagues]);

  return {
    leagues,
    isLoading,
    error,
    refetch
  };
};
