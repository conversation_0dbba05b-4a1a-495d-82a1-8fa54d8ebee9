import { useState, useEffect, useCallback } from 'react';
import { ProcessedFixture } from '../../types';

interface ApiFixture {
  id: number;
  externalId: number;
  slug: string;
  homeTeamName: string;
  awayTeamName: string;
  goalsHome?: number;
  goalsAway?: number;
  status: string;
  elapsed?: number;
  date: string;
  leagueName: string;
  homeTeamFlag: any; // Can be string or object
  awayTeamFlag: any; // Can be string or object
  homeTeamLogo?: string;
  awayTeamLogo?: string;
  venue?: any; // Can be string or object
  round?: any; // Can be string or object
  isHot?: boolean;
  isTrending?: boolean;
  isVip?: boolean;
}

interface ApiResponse {
  status: number;
  data: ApiFixture[];
  meta?: {
    totalItems: number;
    currentPage: number;
    totalPages: number;
  };
}

// Helper function to get country flag emoji
const getCountryFlag = (country: string): string => {
  const flagMap: Record<string, string> = {
    'England': '🏴󠁧󠁢󠁥󠁮󠁧󠁿',
    'Spain': '🇪🇸',
    'Germany': '🇩🇪',
    'Italy': '🇮🇹',
    'France': '🇫🇷',
    'Portugal': '🇵🇹',
    'Netherlands': '🇳🇱',
    'Belgium': '🇧🇪',
    'Brazil': '🇧🇷',
    'Argentina': '🇦🇷',
    'Japan': '🇯🇵',
    'South Korea': '🇰🇷',
    'United States': '🇺🇸',
    'Mexico': '🇲🇽',
    'Canada': '🇨🇦'
  };
  return flagMap[country] || '🏳️';
};

// Helper function to extract flag from API response
const extractFlag = (flagData: any): string => {
  if (typeof flagData === 'string') {
    return flagData;
  }
  if (flagData && typeof flagData === 'object') {
    // If it's an object, try to extract country name or flag
    if (flagData.name) {
      return getCountryFlag(flagData.name);
    }
    if (flagData.country) {
      return getCountryFlag(flagData.country);
    }
    if (flagData.flag) {
      return flagData.flag;
    }
  }
  return '🏳️'; // Default flag
};

// Helper function to extract string from venue data
const extractVenue = (venueData: any): string | undefined => {
  if (typeof venueData === 'string') {
    return venueData;
  }
  if (venueData && typeof venueData === 'object') {
    if (venueData.name) {
      return venueData.name;
    }
    if (venueData.city) {
      return venueData.city;
    }
    if (venueData.stadium) {
      return venueData.stadium;
    }
  }
  return undefined;
};

// Helper function to extract string from round data
const extractRound = (roundData: any): string | undefined => {
  if (typeof roundData === 'string') {
    return roundData;
  }
  if (roundData && typeof roundData === 'object') {
    if (roundData.name) {
      return roundData.name;
    }
    if (roundData.round) {
      return roundData.round;
    }
    if (roundData.matchday) {
      return `Matchday ${roundData.matchday}`;
    }
  }
  return undefined;
};

export const useFixturesByDate = (selectedDate?: string, selectedLeague?: string) => {
  const [fixtures, setFixtures] = useState<ProcessedFixture[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const processApiFixture = (apiFixture: ApiFixture): ProcessedFixture => {
    // Determine status
    let status = 'UPCOMING';
    if (['1H', '2H', 'HT', 'LIVE'].includes(apiFixture.status)) {
      status = 'LIVE';
    } else if (['FT', 'AET', 'PEN', 'FINISHED'].includes(apiFixture.status)) {
      status = 'FINISHED';
    }

    return {
      id: apiFixture.id,
      externalId: apiFixture.externalId,
      slug: apiFixture.slug,
      homeTeam: apiFixture.homeTeamName,
      awayTeam: apiFixture.awayTeamName,
      homeScore: apiFixture.goalsHome,
      awayScore: apiFixture.goalsAway,
      status,
      minute: apiFixture.elapsed ? `${apiFixture.elapsed}'` : undefined,
      date: apiFixture.date,
      competition: apiFixture.leagueName,
      homeFlag: extractFlag(apiFixture.homeTeamFlag),
      awayFlag: extractFlag(apiFixture.awayTeamFlag),
      homeLogo: apiFixture.homeTeamLogo,
      awayLogo: apiFixture.awayTeamLogo,
      venue: extractVenue(apiFixture.venue),
      round: extractRound(apiFixture.round),
      isHot: apiFixture.isHot || false,
      isTrending: apiFixture.isTrending || false,
      isVip: apiFixture.isVip || false,
      temperature: apiFixture.isVip ? 'VIP' : apiFixture.isHot ? 'HOT' : apiFixture.isTrending ? 'TRENDING' : undefined
    };
  };

  const fetchFixtures = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);

      // Build query parameters
      const params = new URLSearchParams();
      params.append('page', '1');
      params.append('limit', '50');

      if (selectedDate) {
        params.append('date', selectedDate);
      }

      if (selectedLeague) {
        params.append('league', selectedLeague.toString());
      }

      const url = `/api/football/fixtures?${params.toString()}`;
      console.log('🔌 Fetching fixtures:', url);

      const response = await fetch(url);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data: ApiResponse = await response.json();

      if (data.status !== 200) {
        throw new Error(`API error: Status ${data.status}`);
      }

      if (!data.data || !Array.isArray(data.data)) {
        throw new Error('Invalid API response: data.data is not an array');
      }

      let processedFixtures = data.data.map(processApiFixture);

      // Client-side filtering since backend doesn't support it yet
      if (selectedDate) {
        const targetDate = new Date(selectedDate).toISOString().split('T')[0];
        processedFixtures = processedFixtures.filter(fixture => {
          if (!fixture.date) return false;
          const fixtureDate = new Date(fixture.date).toISOString().split('T')[0];
          return fixtureDate === targetDate;
        });
      }

      if (selectedLeague) {
        // Filter by exact league name match
        processedFixtures = processedFixtures.filter(fixture => {
          return fixture.competition.toLowerCase() === selectedLeague.toLowerCase();
        });
      }

      console.log('✅ Fixtures fetched successfully:', processedFixtures.length,
        selectedDate ? `(filtered by date: ${selectedDate})` : '',
        selectedLeague ? `(filtered by league: ${selectedLeague})` : '');
      setFixtures(processedFixtures);

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch fixtures';
      console.error('❌ Error fetching fixtures:', errorMessage);
      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  }, [selectedDate, selectedLeague]);

  useEffect(() => {
    fetchFixtures();
  }, [fetchFixtures]);

  const refetch = useCallback(() => {
    fetchFixtures();
  }, [fetchFixtures]);

  return {
    fixtures,
    isLoading,
    error,
    refetch
  };
};
