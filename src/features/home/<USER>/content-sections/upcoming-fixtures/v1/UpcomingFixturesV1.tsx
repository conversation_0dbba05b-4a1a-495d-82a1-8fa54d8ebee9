'use client';

import React from 'react';
import { UpcomingFixturesProps } from '../types';
import { LeagueList } from './components/LeagueList';
import { DateBasedFixtures } from './components/DateBasedFixtures';

const UpcomingFixturesV1: React.FC<UpcomingFixturesProps> = ({
  className = ''
}) => {
  // Enhanced layout with optimized components

  return (
    <section className={`py-16 bg-gradient-to-br from-gray-50 to-white ${className}`}>
      <div className="container mx-auto px-4">
        {/* Header */}

        {/* Clean Two Column Layout */}
        <div className="grid grid-cols-1 xl:grid-cols-4 lg:grid-cols-1 gap-8 max-w-7xl mx-auto">
          {/* League Sidebar - 25% width */}
          <div className="xl:col-span-1 lg:col-span-1">
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <LeagueList />
            </div>
          </div>

          {/* Main Content - 75% width */}
          <div className="xl:col-span-3 lg:col-span-1">
            <DateBasedFixtures />
          </div>
        </div>

        {/* Footer Info */}
        <div className="text-center mt-12">
          <div className="text-sm text-gray-600">
            Click on any league to explore detailed information and standings
          </div>
        </div>
      </div>
    </section>
  );
};

export default UpcomingFixturesV1;
