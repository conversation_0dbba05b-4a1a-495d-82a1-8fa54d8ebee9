import React from 'react';

interface TimelineGroupProps {
  title: string;
  icon: string;
  color: string;
  bgColor: string;
  borderColor: string;
  children: React.ReactNode;
}

export const TimelineGroup: React.FC<TimelineGroupProps> = ({
  title,
  icon,
  color,
  bgColor,
  borderColor,
  children
}) => {
  return (
    <div className="relative">
      {/* Timeline node */}
      <div className="absolute left-6 w-4 h-4 bg-white border-4 border-blue-500 rounded-full shadow-lg z-10"></div>
      
      {/* Content */}
      <div className="ml-16">
        {/* Group header */}
        <div className={`inline-flex items-center space-x-3 ${bgColor} ${borderColor} border px-4 py-2 rounded-lg mb-6 shadow-sm`}>
          <span className="text-xl">{icon}</span>
          <h3 className={`text-lg font-semibold ${color}`}>
            {title}
          </h3>
        </div>
        
        {/* Fixtures */}
        <div className="space-y-4">
          {children}
        </div>
      </div>
    </div>
  );
};
