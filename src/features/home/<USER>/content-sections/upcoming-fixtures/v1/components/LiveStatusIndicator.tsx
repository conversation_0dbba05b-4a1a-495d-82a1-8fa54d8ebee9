import React from 'react';

interface LiveStatusIndicatorProps {
  status: string;
  minute?: string;
  isLive?: boolean;
  className?: string;
}

export const LiveStatusIndicator: React.FC<LiveStatusIndicatorProps> = ({
  status,
  minute,
  isLive = false,
  className = ''
}) => {
  const getStatusConfig = () => {
    // Live statuses
    if (['1H', '2H', 'HT', 'LIVE'].includes(status)) {
      return {
        text: minute || 'LIVE',
        bg: 'bg-red-500',
        color: 'text-white',
        border: 'border-red-500',
        pulse: true,
        icon: '🔴'
      };
    }

    // Finished statuses
    if (['FT', 'AET', 'PEN', 'FINISHED'].includes(status)) {
      return {
        text: 'Finished',
        bg: 'bg-gray-500',
        color: 'text-white',
        border: 'border-gray-500',
        pulse: false,
        icon: '✅'
      };
    }

    // Postponed/Cancelled
    if (['PST', 'CANC', 'ABD', 'AWD', 'WO'].includes(status)) {
      return {
        text: status === 'PST' ? 'Postponed' : 'Cancelled',
        bg: 'bg-yellow-500',
        color: 'text-white',
        border: 'border-yellow-500',
        pulse: false,
        icon: '⚠️'
      };
    }

    // Upcoming/Not Started
    if (['NS', 'TBD'].includes(status)) {
      return {
        text: 'Upcoming',
        bg: 'bg-blue-500',
        color: 'text-white',
        border: 'border-blue-500',
        pulse: false,
        icon: '⏰'
      };
    }

    // Suspended
    if (['SUSP', 'INT'].includes(status)) {
      return {
        text: 'Suspended',
        bg: 'bg-orange-500',
        color: 'text-white',
        border: 'border-orange-500',
        pulse: true,
        icon: '⏸️'
      };
    }

    // Default
    return {
      text: status,
      bg: 'bg-gray-400',
      color: 'text-white',
      border: 'border-gray-400',
      pulse: false,
      icon: '❓'
    };
  };

  const config = getStatusConfig();

  return (
    <div className={`flex items-center space-x-2 ${className}`}>
      {/* Status Badge */}
      <span 
        className={`
          px-3 py-1 rounded-full text-xs font-medium border
          ${config.bg} ${config.color} ${config.border}
          ${config.pulse ? 'animate-pulse' : ''}
          transition-all duration-300
        `}
      >
        <span className="flex items-center space-x-1">
          <span>{config.icon}</span>
          <span>{config.text}</span>
        </span>
      </span>

      {/* Live Indicator Dot */}
      {config.pulse && (
        <div className="relative">
          <div className="w-2 h-2 bg-red-500 rounded-full animate-ping"></div>
          <div className="absolute inset-0 w-2 h-2 bg-red-500 rounded-full"></div>
        </div>
      )}
    </div>
  );
};

// Enhanced Match Time Display
interface MatchTimeDisplayProps {
  date: string;
  status: string;
  minute?: string;
  className?: string;
}

export const MatchTimeDisplay: React.FC<MatchTimeDisplayProps> = ({
  date,
  status,
  minute,
  className = ''
}) => {
  const getTimeDisplay = () => {
    // Live matches
    if (['1H', '2H', 'HT', 'LIVE'].includes(status)) {
      return {
        primary: minute || 'LIVE',
        secondary: 'Live Now',
        color: 'text-red-600',
        bg: 'bg-red-50'
      };
    }

    // Finished matches
    if (['FT', 'AET', 'PEN', 'FINISHED'].includes(status)) {
      return {
        primary: 'Full Time',
        secondary: new Date(date).toLocaleDateString(),
        color: 'text-gray-600',
        bg: 'bg-gray-50'
      };
    }

    // Upcoming matches
    const matchDate = new Date(date);
    const now = new Date();
    const diffMs = matchDate.getTime() - now.getTime();
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffDays = Math.floor(diffHours / 24);

    if (diffDays > 0) {
      return {
        primary: `${diffDays}d ${diffHours % 24}h`,
        secondary: matchDate.toLocaleDateString(),
        color: 'text-blue-600',
        bg: 'bg-blue-50'
      };
    } else if (diffHours > 0) {
      return {
        primary: `${diffHours}h`,
        secondary: matchDate.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
        color: 'text-blue-600',
        bg: 'bg-blue-50'
      };
    } else if (diffMs > 0) {
      const diffMinutes = Math.floor(diffMs / (1000 * 60));
      return {
        primary: `${diffMinutes}m`,
        secondary: 'Starting Soon',
        color: 'text-orange-600',
        bg: 'bg-orange-50'
      };
    }

    return {
      primary: matchDate.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
      secondary: matchDate.toLocaleDateString(),
      color: 'text-gray-600',
      bg: 'bg-gray-50'
    };
  };

  const timeConfig = getTimeDisplay();

  return (
    <div className={`text-center ${className}`}>
      <div className={`text-sm font-semibold ${timeConfig.color}`}>
        {timeConfig.primary}
      </div>
      <div className={`text-xs text-gray-500 px-2 py-1 rounded ${timeConfig.bg} mt-1`}>
        {timeConfig.secondary}
      </div>
    </div>
  );
};

// Real-time Update Indicator
interface RealTimeIndicatorProps {
  lastUpdated: Date | null;
  isActive: boolean;
  onToggle: () => void;
  className?: string;
}

export const RealTimeIndicator: React.FC<RealTimeIndicatorProps> = ({
  lastUpdated,
  isActive,
  onToggle,
  className = ''
}) => {
  const getLastUpdatedText = () => {
    if (!lastUpdated) return 'Never';
    
    const now = new Date();
    const diffMs = now.getTime() - lastUpdated.getTime();
    const diffSeconds = Math.floor(diffMs / 1000);
    
    if (diffSeconds < 60) return `${diffSeconds}s ago`;
    const diffMinutes = Math.floor(diffSeconds / 60);
    if (diffMinutes < 60) return `${diffMinutes}m ago`;
    const diffHours = Math.floor(diffMinutes / 60);
    return `${diffHours}h ago`;
  };

  return (
    <div className={`flex items-center space-x-3 ${className}`}>
      {/* Real-time Toggle */}
      <button
        onClick={onToggle}
        className={`
          flex items-center space-x-2 px-3 py-2 rounded-lg text-sm font-medium
          transition-all duration-300 hover:scale-105
          ${isActive 
            ? 'bg-green-500 text-white hover:bg-green-600' 
            : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
          }
        `}
      >
        <div className={`w-2 h-2 rounded-full ${isActive ? 'bg-white animate-pulse' : 'bg-gray-500'}`}></div>
        <span>{isActive ? 'Live Updates' : 'Paused'}</span>
      </button>

      {/* Last Updated */}
      <div className="text-xs text-gray-500">
        Updated: {getLastUpdatedText()}
      </div>
    </div>
  );
};
