import React from 'react';

interface LoadingStateProps {
  className?: string;
}

export const LoadingState: React.FC<LoadingStateProps> = ({ className = '' }) => {
  return (
    <section className={`py-16 bg-gradient-to-br from-gray-50 to-white ${className}`}>
      <div className="container mx-auto px-4">
        {/* Header skeleton */}
        <div className="text-center mb-12">
          <div className="inline-block bg-gray-200 rounded-full h-8 w-32 mb-4 animate-pulse"></div>
          <div className="bg-gray-200 rounded h-10 w-80 mx-auto mb-4 animate-pulse"></div>
          <div className="bg-gray-200 rounded h-6 w-96 mx-auto animate-pulse"></div>
        </div>

        {/* Timeline skeleton */}
        <div className="max-w-4xl mx-auto">
          <div className="relative">
            {/* Timeline line */}
            <div className="absolute left-8 top-0 bottom-0 w-0.5 bg-gray-200"></div>
            
            {/* Timeline groups skeleton */}
            <div className="space-y-12">
              {[1, 2, 3].map((group) => (
                <div key={group} className="relative">
                  {/* Timeline node */}
                  <div className="absolute left-6 w-4 h-4 bg-gray-200 rounded-full animate-pulse"></div>
                  
                  {/* Content */}
                  <div className="ml-16">
                    {/* Group header skeleton */}
                    <div className="inline-block bg-gray-200 rounded-lg h-10 w-32 mb-6 animate-pulse"></div>
                    
                    {/* Fixture cards skeleton */}
                    <div className="space-y-4">
                      {[1, 2].map((card) => (
                        <div key={card} className="bg-white rounded-xl border border-gray-200 p-6">
                          {/* Header skeleton */}
                          <div className="flex items-center justify-between mb-4">
                            <div className="bg-gray-200 rounded h-5 w-32 animate-pulse"></div>
                            <div className="bg-gray-200 rounded-full h-6 w-20 animate-pulse"></div>
                          </div>

                          {/* Teams skeleton */}
                          <div className="flex items-center justify-between">
                            {/* Home team skeleton */}
                            <div className="flex items-center space-x-3 flex-1">
                              <div className="w-10 h-10 bg-gray-200 rounded animate-pulse"></div>
                              <div>
                                <div className="bg-gray-200 rounded h-5 w-24 mb-2 animate-pulse"></div>
                                <div className="bg-gray-200 rounded h-4 w-16 animate-pulse"></div>
                              </div>
                            </div>

                            {/* Score skeleton */}
                            <div className="text-center px-4">
                              <div className="bg-gray-200 rounded h-6 w-12 mx-auto mb-2 animate-pulse"></div>
                              <div className="bg-gray-200 rounded h-4 w-16 mx-auto animate-pulse"></div>
                            </div>

                            {/* Away team skeleton */}
                            <div className="flex items-center space-x-3 flex-1 justify-end">
                              <div className="text-right">
                                <div className="bg-gray-200 rounded h-5 w-24 mb-2 animate-pulse"></div>
                                <div className="bg-gray-200 rounded h-4 w-16 animate-pulse"></div>
                              </div>
                              <div className="w-10 h-10 bg-gray-200 rounded animate-pulse"></div>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Footer skeleton */}
        <div className="text-center mt-12">
          <div className="inline-block bg-gray-200 rounded-lg h-12 w-40 animate-pulse"></div>
        </div>
      </div>
    </section>
  );
};
