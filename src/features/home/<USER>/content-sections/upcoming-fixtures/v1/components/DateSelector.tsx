import React, { useState } from 'react';

interface DateSelectorProps {
  selectedDate: string | null;
  onDateSelect: (date: string | null) => void;
}

export const DateSelector: React.FC<DateSelectorProps> = ({
  selectedDate,
  onDateSelect
}) => {
  const [showCalendar, setShowCalendar] = useState(false);

  // Get today's date in YYYY-MM-DD format
  const today = new Date();
  const todayStr = today.toISOString().split('T')[0];

  // Get tomorrow's date
  const tomorrow = new Date(today);
  tomorrow.setDate(tomorrow.getDate() + 1);
  const tomorrowStr = tomorrow.toISOString().split('T')[0];

  // Format date for display
  const formatDateDisplay = (dateStr: string) => {
    const date = new Date(dateStr);
    return date.toLocaleDateString('en-US', {
      weekday: 'short',
      month: 'short',
      day: 'numeric'
    });
  };

  // Quick date options
  const quickDates = [
    { key: null, label: 'All Dates', icon: '🌍' },
    { key: todayStr, label: 'Today', icon: '📅' },
    { key: tomorrowStr, label: 'Tomorrow', icon: '➡️' }
  ];

  const handleCustomDateSelect = (date: string) => {
    onDateSelect(date);
    setShowCalendar(false);
  };

  return (
    <div className="space-y-4">
      {/* Premium Header */}
      <div className="flex items-center space-x-3 mb-6">
        <div className="w-6 h-6 bg-gradient-to-r from-green-500 to-emerald-600 rounded-full flex items-center justify-center">
          <span className="text-white text-xs font-bold">📅</span>
        </div>
        <h4 className="text-lg font-bold text-gray-900">Schedule</h4>
        <div className="flex-1 h-px bg-gradient-to-r from-gray-300 to-transparent"></div>
      </div>

      {/* Quick Date Options - Premium Design */}
      <div className="space-y-3">
        {quickDates.map((option) => (
          <button
            key={option.key || 'all'}
            onClick={() => onDateSelect(option.key)}
            className={`group w-full flex items-center space-x-4 p-4 rounded-xl transition-all duration-300 text-left transform hover:scale-[1.02] ${selectedDate === option.key
              ? 'bg-gradient-to-r from-green-500 to-emerald-600 text-white shadow-lg shadow-green-500/25'
              : 'bg-gradient-to-r from-white to-gray-50 border border-gray-200 hover:border-green-300 hover:shadow-md text-gray-700'
              }`}
          >
            <div className={`w-12 h-12 rounded-full flex items-center justify-center text-xl ${selectedDate === option.key
              ? 'bg-white/20 backdrop-blur-sm'
              : 'bg-gradient-to-br from-green-500 to-emerald-600'
              }`}>
              <span className={selectedDate === option.key ? 'text-white' : 'text-white'}>
                {option.icon}
              </span>
            </div>

            <div className="flex-1">
              <div className={`font-semibold ${selectedDate === option.key ? 'text-white' : 'text-gray-900'}`}>
                {option.label}
              </div>
              {option.key ? (
                <div className={`text-sm ${selectedDate === option.key ? 'text-white/80' : 'text-gray-500'}`}>
                  {formatDateDisplay(option.key)}
                </div>
              ) : (
                <div className={`text-sm ${selectedDate === option.key ? 'text-white/80' : 'text-gray-500'}`}>
                  View all upcoming matches
                </div>
              )}
            </div>

            {selectedDate === option.key && (
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-white rounded-full animate-pulse"></div>
                <div className="w-1 h-1 bg-white/60 rounded-full"></div>
              </div>
            )}
          </button>
        ))}
      </div>

      {/* Custom Date Picker - Premium Design */}
      <div className="border-t border-gray-200 pt-6">
        <button
          onClick={() => setShowCalendar(!showCalendar)}
          className="group w-full flex items-center space-x-4 p-4 rounded-xl border border-gray-200 hover:border-purple-300 hover:shadow-md transition-all duration-300 text-left bg-gradient-to-r from-white to-gray-50"
        >
          <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-pink-600 rounded-full flex items-center justify-center text-xl">
            <span className="text-white">📆</span>
          </div>
          <div className="flex-1">
            <div className="font-semibold text-gray-900">Custom Date</div>
            <div className="text-sm text-gray-500">Pick any specific date</div>
          </div>
          <svg
            className={`w-5 h-5 text-gray-400 transition-transform duration-300 ${showCalendar ? 'rotate-180' : ''}`}
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
          </svg>
        </button>

        {showCalendar && (
          <div className="mt-4 p-4 bg-gradient-to-br from-purple-50 to-pink-50 border border-purple-200 rounded-xl">
            <input
              type="date"
              value={selectedDate || ''}
              onChange={(e) => handleCustomDateSelect(e.target.value)}
              min={todayStr}
              max={new Date(Date.now() + 90 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]}
              className="w-full p-3 border border-purple-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 bg-white shadow-sm"
            />
            <div className="flex items-center space-x-2 mt-3">
              <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
              <p className="text-xs text-purple-700">
                Select any date up to 90 days in the future
              </p>
            </div>
          </div>
        )}
      </div>

      {/* Selected Date Display - Premium */}
      {selectedDate && !quickDates.some(q => q.key === selectedDate) && (
        <div className="p-4 bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-xl shadow-sm">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                <span className="text-white text-xs">✓</span>
              </div>
              <div>
                <div className="font-semibold text-blue-900">Selected Date</div>
                <div className="text-sm text-blue-700">
                  {formatDateDisplay(selectedDate)}
                </div>
              </div>
            </div>
            <button
              onClick={() => onDateSelect(null)}
              className="px-3 py-1 bg-blue-500 text-white text-sm rounded-lg hover:bg-blue-600 transition-colors"
            >
              Clear
            </button>
          </div>
        </div>
      )}
    </div>
  );
};
