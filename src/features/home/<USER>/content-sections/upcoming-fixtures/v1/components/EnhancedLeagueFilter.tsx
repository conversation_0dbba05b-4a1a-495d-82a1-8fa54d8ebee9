import React, { useState, useMemo } from 'react';
import { useActiveLeagues, League } from '../hooks/useActiveLeagues';
import { LoadingState } from './LoadingState';
import { ErrorState } from './ErrorState';

interface EnhancedLeagueFilterProps {
  selectedLeague?: string | null;
  selectedLeagueId?: number | null;
  onLeagueSelect: (leagueName: string | null, leagueId: number | null) => void;
  className?: string;
}

export const EnhancedLeagueFilter: React.FC<EnhancedLeagueFilterProps> = ({
  selectedLeague,
  selectedLeagueId,
  onLeagueSelect,
  className = ''
}) => {
  const { leagues, isLoading, error, refetch } = useActiveLeagues();
  const [searchTerm, setSearchTerm] = useState('');

  // Filter leagues based on search term
  const filteredLeagues = useMemo(() => {
    if (!searchTerm) return leagues;
    
    return leagues.filter(league =>
      league.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      league.country.toLowerCase().includes(searchTerm.toLowerCase())
    );
  }, [leagues, searchTerm]);

  // Group leagues by priority/popularity
  const groupedLeagues = useMemo(() => {
    const popular = ['Premier League', 'La Liga', 'Serie A', 'Bundesliga', 'Ligue 1', 'Champions League'];
    
    const popularLeagues = filteredLeagues.filter(league => 
      popular.some(p => league.name.includes(p))
    );
    
    const otherLeagues = filteredLeagues.filter(league => 
      !popular.some(p => league.name.includes(p))
    );

    return { popular: popularLeagues, other: otherLeagues };
  }, [filteredLeagues]);

  const handleLeagueClick = (league: League | null) => {
    if (!league) {
      onLeagueSelect(null, null);
      return;
    }
    
    onLeagueSelect(league.name, league.id);
  };

  const isSelected = (league: League) => {
    return selectedLeagueId ? selectedLeagueId === league.id : selectedLeague === league.name;
  };

  if (isLoading) {
    return (
      <div className={className}>
        <h3 className="text-lg font-semibold text-gray-900 mb-4">League Filter</h3>
        <div className="space-y-2">
          {[...Array(5)].map((_, i) => (
            <div key={i} className="animate-pulse">
              <div className="flex items-center space-x-3 p-3 bg-gray-100 rounded-lg">
                <div className="w-8 h-8 bg-gray-300 rounded-full"></div>
                <div className="flex-1">
                  <div className="h-4 bg-gray-300 rounded w-3/4"></div>
                  <div className="h-3 bg-gray-200 rounded w-1/2 mt-1"></div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={className}>
        <h3 className="text-lg font-semibold text-gray-900 mb-4">League Filter</h3>
        <ErrorState error={error} onRetry={refetch} />
      </div>
    );
  }

  return (
    <div className={className}>
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-gray-900">League Filter</h3>
        <span className="text-sm text-gray-500">{leagues.length} leagues</span>
      </div>

      {/* Search Input */}
      <div className="relative mb-4">
        <input
          type="text"
          placeholder="Search leagues..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="w-full px-4 py-2 pl-10 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        />
        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
          <svg className="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
          </svg>
        </div>
      </div>

      {/* All Leagues Option */}
      <button
        onClick={() => handleLeagueClick(null)}
        className={`w-full flex items-center space-x-3 p-3 rounded-lg transition-all duration-200 mb-3 ${
          !selectedLeague && !selectedLeagueId
            ? 'bg-blue-500 text-white shadow-md'
            : 'bg-gray-50 text-gray-700 hover:bg-gray-100'
        }`}
      >
        <div className="w-8 h-8 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center text-white font-bold text-sm">
          ALL
        </div>
        <div className="flex-1 text-left">
          <div className="font-medium">All Leagues</div>
          <div className="text-sm opacity-75">Show fixtures from all leagues</div>
        </div>
        {(!selectedLeague && !selectedLeagueId) && (
          <div className="w-2 h-2 bg-white rounded-full"></div>
        )}
      </button>

      {/* Popular Leagues */}
      {groupedLeagues.popular.length > 0 && (
        <div className="mb-4">
          <h4 className="text-sm font-medium text-gray-600 mb-2 px-1">Popular Leagues</h4>
          <div className="space-y-1">
            {groupedLeagues.popular.map((league) => (
              <button
                key={league.id}
                onClick={() => handleLeagueClick(league)}
                className={`w-full flex items-center space-x-3 p-3 rounded-lg transition-all duration-200 ${
                  isSelected(league)
                    ? 'bg-blue-500 text-white shadow-md transform scale-[1.02]'
                    : 'bg-white text-gray-700 hover:bg-blue-50 hover:shadow-sm border border-gray-200'
                }`}
              >
                <div className="relative w-8 h-8 flex-shrink-0">
                  <img
                    src={league.logo}
                    alt={league.name}
                    className="w-full h-full object-contain rounded-full"
                    onError={(e) => {
                      e.currentTarget.style.display = 'none';
                    }}
                  />
                </div>
                <div className="flex-1 text-left">
                  <div className="font-medium text-sm">{league.name}</div>
                  <div className={`text-xs ${isSelected(league) ? 'text-blue-100' : 'text-gray-500'}`}>
                    {league.country}
                  </div>
                </div>
                {isSelected(league) && (
                  <div className="w-2 h-2 bg-white rounded-full"></div>
                )}
              </button>
            ))}
          </div>
        </div>
      )}

      {/* Other Leagues */}
      {groupedLeagues.other.length > 0 && (
        <div>
          <h4 className="text-sm font-medium text-gray-600 mb-2 px-1">Other Leagues</h4>
          <div className="space-y-1 max-h-64 overflow-y-auto">
            {groupedLeagues.other.map((league) => (
              <button
                key={league.id}
                onClick={() => handleLeagueClick(league)}
                className={`w-full flex items-center space-x-3 p-2 rounded-lg transition-all duration-200 ${
                  isSelected(league)
                    ? 'bg-blue-500 text-white shadow-md'
                    : 'bg-white text-gray-700 hover:bg-blue-50 border border-gray-200'
                }`}
              >
                <div className="relative w-6 h-6 flex-shrink-0">
                  <img
                    src={league.logo}
                    alt={league.name}
                    className="w-full h-full object-contain rounded-full"
                    onError={(e) => {
                      e.currentTarget.style.display = 'none';
                    }}
                  />
                </div>
                <div className="flex-1 text-left">
                  <div className="font-medium text-sm">{league.name}</div>
                  <div className={`text-xs ${isSelected(league) ? 'text-blue-100' : 'text-gray-500'}`}>
                    {league.country}
                  </div>
                </div>
                {isSelected(league) && (
                  <div className="w-1.5 h-1.5 bg-white rounded-full"></div>
                )}
              </button>
            ))}
          </div>
        </div>
      )}

      {/* No Results */}
      {filteredLeagues.length === 0 && searchTerm && (
        <div className="text-center py-8">
          <div className="text-gray-400 mb-2">🔍</div>
          <div className="text-sm text-gray-600">No leagues found for "{searchTerm}"</div>
          <button
            onClick={() => setSearchTerm('')}
            className="text-blue-500 text-sm mt-2 hover:underline"
          >
            Clear search
          </button>
        </div>
      )}
    </div>
  );
};
