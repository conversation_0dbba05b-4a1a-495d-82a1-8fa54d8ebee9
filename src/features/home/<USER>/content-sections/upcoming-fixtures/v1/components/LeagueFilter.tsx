import React, { useState } from 'react';
import { useActiveLeagues, League } from '../hooks/useActiveLeagues';

interface LeagueFilterProps {
  selectedLeague: string | null;
  onLeagueSelect: (leagueName: string | null) => void;
}

export const LeagueFilter: React.FC<LeagueFilterProps> = ({
  selectedLeague,
  onLeagueSelect
}) => {
  const { leagues, isLoading, error } = useActiveLeagues();
  const [imageErrors, setImageErrors] = useState<Set<string>>(new Set());

  const handleImageError = (leagueId: number, leagueName: string, country: string) => {
    setImageErrors(prev => new Set(prev).add(leagueId.toString()));
  };

  const generateFallbackLogo = (leagueName: string, country: string) => {
    const initials = leagueName.split(' ').map(word => word.charAt(0)).join('').substring(0, 2).toUpperCase();

    const premiumColors = {
      'Premier League': { bg: '#37003c', accent: '#00ff87' },
      'La Liga': { bg: '#ff6900', accent: '#ffffff' },
      'Serie A': { bg: '#0066cc', accent: '#ffffff' },
      'Bundesliga': { bg: '#d20515', accent: '#ffffff' },
      'Ligue 1': { bg: '#1e3a8a', accent: '#fbbf24' },
      'Champions League': { bg: '#0c1e3e', accent: '#1e40af' },
      'Europa League': { bg: '#ff6900', accent: '#ffffff' }
    };

    const colorScheme = premiumColors[leagueName as keyof typeof premiumColors] ||
      { bg: '#1f2937', accent: '#60a5fa' };

    return `data:image/svg+xml,${encodeURIComponent(`
      <svg width="48" height="48" xmlns="http://www.w3.org/2000/svg">
        <defs>
          <linearGradient id="grad-${leagueName.replace(/\s+/g, '')}" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" style="stop-color:${colorScheme.bg};stop-opacity:1" />
            <stop offset="100%" style="stop-color:${colorScheme.bg}dd;stop-opacity:1" />
          </linearGradient>
        </defs>
        <circle cx="24" cy="24" r="22" fill="url(#grad-${leagueName.replace(/\s+/g, '')})" stroke="${colorScheme.accent}" stroke-width="2"/>
        <text x="24" y="30" text-anchor="middle" fill="${colorScheme.accent}" font-family="Arial" font-size="12" font-weight="bold">
          ${initials}
        </text>
        <circle cx="24" cy="24" r="18" fill="none" stroke="${colorScheme.accent}40" stroke-width="1"/>
      </svg>
    `)}`;
  };

  if (isLoading) {
    return (
      <div className="space-y-4">
        {/* Premium Header */}
        <div className="flex items-center space-x-3 mb-6">
          <div className="w-6 h-6 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full animate-pulse"></div>
          <h3 className="text-lg font-bold text-gray-900">Competitions</h3>
        </div>

        {/* Premium Loading Cards */}
        {[1, 2, 3, 4, 5].map((i) => (
          <div key={i} className="group relative">
            <div className="flex items-center space-x-4 p-4 bg-gradient-to-r from-white to-gray-50 rounded-xl border border-gray-200 shadow-sm">
              {/* Logo Skeleton */}
              <div className="relative">
                <div className="w-12 h-12 bg-gradient-to-br from-gray-200 to-gray-300 rounded-full animate-pulse"></div>
                <div className="absolute inset-0 bg-gradient-to-br from-transparent to-white/20 rounded-full"></div>
              </div>

              {/* Content Skeleton */}
              <div className="flex-1 space-y-2">
                <div className="h-4 bg-gradient-to-r from-gray-200 to-gray-300 rounded-lg w-3/4 animate-pulse"></div>
                <div className="h-3 bg-gradient-to-r from-gray-200 to-gray-300 rounded w-1/2 animate-pulse"></div>
              </div>

              {/* Status Indicator Skeleton */}
              <div className="w-3 h-3 bg-gray-300 rounded-full animate-pulse"></div>
            </div>
          </div>
        ))}
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-6 bg-gradient-to-br from-red-50 to-red-100 border border-red-200 rounded-xl shadow-sm">
        <div className="flex items-center space-x-3 mb-4">
          <div className="w-8 h-8 bg-red-500 rounded-full flex items-center justify-center shadow-sm">
            <span className="text-white text-sm">⚠️</span>
          </div>
          <h3 className="text-lg font-bold text-red-900">Competitions</h3>
        </div>
        <p className="text-red-700 text-sm mb-4">Unable to load competitions. Please check your connection and try again.</p>
        <button
          onClick={() => window.location.reload()}
          className="w-full bg-red-500 text-white px-4 py-2 rounded-lg hover:bg-red-600 transition-colors text-sm font-medium"
        >
          Retry
        </button>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Premium Header with Icon */}
      <div className="flex items-center space-x-3 mb-6">
        <div className="w-6 h-6 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
          <span className="text-white text-xs font-bold">🏆</span>
        </div>
        <h3 className="text-lg font-bold text-gray-900">Competitions</h3>
        <div className="flex-1 h-px bg-gradient-to-r from-gray-300 to-transparent"></div>
      </div>

      {/* All Leagues Option - Premium Design */}
      <button
        onClick={() => onLeagueSelect(null)}
        className={`group w-full flex items-center space-x-4 p-4 rounded-xl transition-all duration-300 text-left transform hover:scale-[1.02] ${selectedLeague === null
          ? 'bg-gradient-to-r from-blue-500 to-purple-600 text-white shadow-lg shadow-blue-500/25'
          : 'bg-gradient-to-r from-white to-gray-50 border border-gray-200 hover:border-blue-300 hover:shadow-md text-gray-700'
          }`}
      >
        <div className={`relative w-12 h-12 rounded-full flex items-center justify-center ${selectedLeague === null
          ? 'bg-white/20 backdrop-blur-sm'
          : 'bg-gradient-to-br from-blue-500 to-purple-600'
          }`}>
          <span className={`text-sm font-bold ${selectedLeague === null ? 'text-white' : 'text-white'
            }`}>ALL</span>
          {selectedLeague === null && (
            <div className="absolute inset-0 bg-white/10 rounded-full animate-pulse"></div>
          )}
        </div>

        <div className="flex-1">
          <div className={`font-semibold ${selectedLeague === null ? 'text-white' : 'text-gray-900'}`}>
            All Competitions
          </div>
          <div className={`text-sm ${selectedLeague === null ? 'text-white/80' : 'text-gray-500'}`}>
            View fixtures from all leagues
          </div>
        </div>

        {selectedLeague === null && (
          <div className="flex items-center space-x-2">
            <div className="w-2 h-2 bg-white rounded-full animate-pulse"></div>
            <div className="w-1 h-1 bg-white/60 rounded-full"></div>
          </div>
        )}
      </button>

      {/* Individual Leagues - Premium Cards */}
      {leagues.map((league) => (
        <button
          key={league.id}
          onClick={() => onLeagueSelect(league.name)}
          className={`group w-full flex items-center space-x-4 p-4 rounded-xl transition-all duration-300 text-left transform hover:scale-[1.02] ${selectedLeague === league.name
            ? 'bg-gradient-to-r from-blue-50 to-purple-50 border-2 border-blue-400 text-blue-900 shadow-lg shadow-blue-500/20'
            : 'bg-gradient-to-r from-white to-gray-50 border border-gray-200 hover:border-blue-300 hover:shadow-md text-gray-700'
            }`}
        >
          {/* Premium Logo Container */}
          <div className="relative">
            {!imageErrors.has(league.id.toString()) && league.logo ? (
              <img
                src={league.logo}
                alt={league.name}
                className="w-12 h-12 object-contain rounded-lg shadow-sm"
                onError={() => handleImageError(league.id, league.name, league.country)}
              />
            ) : (
              <img
                src={generateFallbackLogo(league.name, league.country)}
                alt={league.name}
                className="w-12 h-12 object-contain rounded-lg shadow-sm"
              />
            )}

            {/* Premium Glow Effect for Selected */}
            {selectedLeague === league.name && (
              <div className="absolute inset-0 bg-blue-400/20 rounded-lg animate-pulse"></div>
            )}
          </div>

          {/* League Info */}
          <div className="flex-1 min-w-0">
            <div className={`font-semibold truncate ${selectedLeague === league.name ? 'text-blue-900' : 'text-gray-900'
              }`}>
              {league.name}
            </div>
            <div className={`text-sm truncate ${selectedLeague === league.name ? 'text-blue-700' : 'text-gray-500'
              }`}>
              {league.country} • {league.season}
            </div>
          </div>

          {/* Status Indicators */}
          <div className="flex items-center space-x-2">
            {selectedLeague === league.name ? (
              <div className="flex items-center space-x-1">
                <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
                <div className="w-1 h-1 bg-blue-400 rounded-full"></div>
              </div>
            ) : (
              <div className="w-3 h-3 border-2 border-gray-300 rounded-full group-hover:border-blue-400 transition-colors"></div>
            )}
          </div>
        </button>
      ))}
    </div>
  );
};
