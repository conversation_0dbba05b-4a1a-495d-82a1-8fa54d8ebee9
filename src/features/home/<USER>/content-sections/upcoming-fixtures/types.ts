// Shared types for Upcoming Fixtures versions
export interface ProcessedFixture {
  id: number;
  externalId: number;
  slug: string;
  homeTeam: string;
  awayTeam: string;
  homeScore?: number;
  awayScore?: number;
  status: string;
  minute?: string;
  kickoff?: string;
  date?: string;
  lastUpdate?: string;
  competition: string;
  homeFlag: string;
  awayFlag: string;
  homeLogo?: string;
  awayLogo?: string;
  temperature?: 'HOT' | 'TRENDING' | 'VIP';
  isHot: boolean;
  isTrending?: boolean;
  isVip?: boolean;
  venue?: string;
  round?: string;
  dateCategory?: 'today' | 'tomorrow' | 'this-week' | 'next-week' | 'unknown';
}

export interface UpcomingFixturesProps {
  className?: string;
  maxItems?: number;
  showFilters?: boolean;
  variant?: 'timeline' | 'grid' | 'compact';
}

export interface DateFilter {
  key: string;
  label: string;
  icon: string;
}

export interface LeagueFilter {
  key: string;
  label: string;
}

export interface StatusDisplay {
  text: string;
  color: string;
  bg: string;
}
