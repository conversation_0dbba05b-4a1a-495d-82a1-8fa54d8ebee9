// Breaking News V1 Types

export interface NewsItem {
  id: string;
  title: string;
  excerpt: string;
  thumbnail: string;
  category: 'breaking' | 'transfer' | 'match' | 'interview';
  timestamp: string;
  author: string;
  readTime: number;
  url?: string;
  tags?: string[];
  priority?: 'high' | 'medium' | 'low';
  isBreaking?: boolean;
}

export interface BreakingNewsData {
  news: NewsItem[];
  lastUpdated: string;
  totalCount: number;
}

export interface BreakingNewsProps {
  className?: string;
  autoRotate?: boolean;
  rotationInterval?: number;
  maxItems?: number;
  showControls?: boolean;
}

// Category configuration
export interface CategoryConfig {
  color: string;
  icon: string;
  label: string;
}

export type CategoryType = NewsItem['category'];

// Filter options
export interface FilterOptions {
  category?: CategoryType[];
  dateRange?: {
    start: string;
    end: string;
  };
  priority?: ('high' | 'medium' | 'low')[];
  author?: string[];
}

// Animation configuration
export interface AnimationConfig {
  duration: number;
  easing: 'ease' | 'ease-in' | 'ease-out' | 'ease-in-out';
  delay?: number;
}
