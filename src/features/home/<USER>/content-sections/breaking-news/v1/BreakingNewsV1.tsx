'use client';

import React, { useState, useEffect } from 'react';
import { NewsItem, BreakingNewsProps, CategoryType } from './types';

export const BreakingNewsV1: React.FC<BreakingNewsProps> = ({ 
  className = '',
  autoRotate = true,
  rotationInterval = 8000,
  maxItems = 4,
  showControls = true
}) => {
  const [currentMainIndex, setCurrentMainIndex] = useState(0);
  const [isAutoRotating, setIsAutoRotating] = useState(autoRotate);

  // Mock data - replace with real API data
  const newsItems: NewsItem[] = [
    {
      id: '1',
      title: '<PERSON><PERSON> Scores Hat-trick in Champions League Final',
      excerpt: '<PERSON> delivered a masterclass performance with three goals to secure victory in the Champions League final against Manchester City.',
      thumbnail: 'https://images.unsplash.com/photo-1574629810360-7efbbe195018?w=600&h=400&fit=crop',
      category: 'breaking',
      timestamp: '2 hours ago',
      author: 'Sports Desk',
      readTime: 3,
      priority: 'high',
      isBreaking: true,
      tags: ['Champions League', '<PERSON><PERSON>', 'Final']
    },
    {
      id: '2',
      title: 'Transfer Bombshell: Mbappé to Real Madrid',
      excerpt: 'Kylian Mbappé has reportedly agreed to join Real Madrid in a record-breaking transfer deal worth €200 million this summer.',
      thumbnail: 'https://images.unsplash.com/photo-1551698618-1dfe5d97d256?w=600&h=400&fit=crop',
      category: 'transfer',
      timestamp: '4 hours ago',
      author: 'Transfer Expert',
      readTime: 5,
      priority: 'high',
      tags: ['Transfer', 'Mbappé', 'Real Madrid']
    },
    {
      id: '3',
      title: 'World Cup 2026 Venues Announced',
      excerpt: 'FIFA reveals the official stadiums that will host matches for the 2026 World Cup across USA, Canada, and Mexico.',
      thumbnail: 'https://images.unsplash.com/photo-1508098682722-e99c43a406b2?w=600&h=400&fit=crop',
      category: 'breaking',
      timestamp: '6 hours ago',
      author: 'FIFA Correspondent',
      readTime: 4,
      priority: 'medium',
      tags: ['World Cup', 'FIFA', '2026']
    },
    {
      id: '4',
      title: 'Exclusive: Guardiola Interview',
      excerpt: 'Pep Guardiola opens up about Manchester City\'s tactics and future plans in an exclusive interview with our chief reporter.',
      thumbnail: 'https://images.unsplash.com/photo-1560272564-c83b66b1ad12?w=600&h=400&fit=crop',
      category: 'interview',
      timestamp: '8 hours ago',
      author: 'Chief Reporter',
      readTime: 7,
      priority: 'medium',
      tags: ['Interview', 'Guardiola', 'Manchester City']
    },
    {
      id: '5',
      title: 'Premier League Title Race Heats Up',
      excerpt: 'With just 5 games remaining, three teams are still in contention for the Premier League title in the most exciting finish in years.',
      thumbnail: 'https://images.unsplash.com/photo-1551698618-1dfe5d97d256?w=600&h=400&fit=crop',
      category: 'match',
      timestamp: '10 hours ago',
      author: 'League Analyst',
      readTime: 6,
      priority: 'medium',
      tags: ['Premier League', 'Title Race', 'Season']
    }
  ];

  // Auto-rotate main story
  useEffect(() => {
    if (!isAutoRotating) return;

    const interval = setInterval(() => {
      setCurrentMainIndex((prev) => (prev + 1) % Math.min(newsItems.length, maxItems));
    }, rotationInterval);

    return () => clearInterval(interval);
  }, [isAutoRotating, newsItems.length, maxItems, rotationInterval]);

  // Category styling
  const getCategoryStyle = (category: CategoryType) => {
    const styles = {
      'breaking': { color: 'bg-red-500', icon: '🚨', label: 'Breaking' },
      'transfer': { color: 'bg-blue-500', icon: '🔄', label: 'Transfer' },
      'match': { color: 'bg-green-500', icon: '⚽', label: 'Match' },
      'interview': { color: 'bg-purple-500', icon: '🎤', label: 'Interview' }
    };
    return styles[category] || { color: 'bg-gray-500', icon: '📰', label: 'News' };
  };

  const displayedNews = newsItems.slice(0, maxItems);
  const mainNews = displayedNews[currentMainIndex];
  const sideNews = displayedNews.filter((_, index) => index !== currentMainIndex).slice(0, 3);

  return (
    <section className={`py-16 bg-gradient-to-br from-gray-50 to-white ${className}`}>
      <div className="container mx-auto px-4 max-w-7xl">
        {/* Section Header */}
        <div className="text-center mb-12">
          <div className="inline-flex items-center space-x-2 bg-red-500 text-white px-4 py-2 rounded-full mb-4 animate-pulse">
            <span className="text-lg">🔴</span>
            <span className="font-bold text-sm tracking-wide">BREAKING NEWS</span>
          </div>
          <h2 className="text-4xl font-bold text-gray-900 mb-4">Latest Football News</h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Stay updated with the most recent developments in the football world
          </p>
        </div>

        {/* Controls */}
        {showControls && (
          <div className="flex justify-center mb-8">
            <button
              onClick={() => setIsAutoRotating(!isAutoRotating)}
              className={`px-6 py-3 rounded-xl font-medium transition-all duration-300 ${
                isAutoRotating
                  ? 'bg-blue-500 text-white hover:bg-blue-600 shadow-lg'
                  : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
              }`}
            >
              {isAutoRotating ? '⏸️ Pause Auto-rotation' : '▶️ Resume Auto-rotation'}
            </button>
          </div>
        )}

        {/* News Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Story (Large Card) */}
          <div className="lg:col-span-2">
            <div className="bg-white rounded-2xl shadow-xl overflow-hidden group cursor-pointer transform transition-all duration-500 hover:scale-[1.02] hover:shadow-2xl">
              {/* Image */}
              <div className="relative h-72 md:h-96 overflow-hidden">
                <img
                  src={mainNews.thumbnail}
                  alt={mainNews.title}
                  className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-700"
                />
                
                {/* Overlay */}
                <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/30 to-transparent"></div>
                
                {/* Category Badge */}
                <div className={`absolute top-6 left-6 ${getCategoryStyle(mainNews.category).color} text-white px-4 py-2 rounded-full text-sm font-bold flex items-center space-x-2 shadow-lg`}>
                  <span className="text-lg">{getCategoryStyle(mainNews.category).icon}</span>
                  <span>{getCategoryStyle(mainNews.category).label.toUpperCase()}</span>
                </div>

                {/* Breaking Badge */}
                {mainNews.isBreaking && (
                  <div className="absolute top-6 right-6 bg-red-500 text-white px-3 py-1 rounded-full text-xs font-bold animate-pulse">
                    🔴 LIVE
                  </div>
                )}

                {/* Priority Indicator */}
                {mainNews.priority === 'high' && (
                  <div className="absolute bottom-6 right-6 bg-yellow-500 text-black px-3 py-1 rounded-full text-xs font-bold">
                    🔥 HOT
                  </div>
                )}

                {/* Content Overlay */}
                <div className="absolute bottom-0 left-0 right-0 p-8 text-white">
                  <h3 className="text-3xl font-bold mb-3 line-clamp-2 leading-tight">
                    {mainNews.title}
                  </h3>
                  <p className="text-gray-200 mb-4 line-clamp-2 text-lg">
                    {mainNews.excerpt}
                  </p>
                  <div className="flex items-center justify-between text-sm">
                    <div className="flex items-center space-x-4">
                      <span className="font-medium">{mainNews.author}</span>
                      <span>•</span>
                      <span>{mainNews.timestamp}</span>
                    </div>
                    <span className="bg-white/20 px-3 py-1 rounded-full">
                      {mainNews.readTime} min read
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Side News (Small Cards) */}
          <div className="space-y-6">
            {sideNews.map((news) => {
              const categoryStyle = getCategoryStyle(news.category);
              
              return (
                <div
                  key={news.id}
                  className="bg-white rounded-xl shadow-lg overflow-hidden group cursor-pointer transform transition-all duration-300 hover:scale-[1.02] hover:shadow-xl"
                >
                  <div className="flex">
                    {/* Thumbnail */}
                    <div className="relative w-28 h-24 flex-shrink-0 overflow-hidden">
                      <img
                        src={news.thumbnail}
                        alt={news.title}
                        className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500"
                      />
                      
                      {/* Category Icon */}
                      <div className={`absolute top-2 left-2 ${categoryStyle.color} text-white w-6 h-6 rounded-full flex items-center justify-center text-xs shadow-md`}>
                        {categoryStyle.icon}
                      </div>

                      {/* Breaking Indicator */}
                      {news.isBreaking && (
                        <div className="absolute top-2 right-2 w-2 h-2 bg-red-500 rounded-full animate-pulse"></div>
                      )}
                    </div>

                    {/* Content */}
                    <div className="flex-1 p-4">
                      <h4 className="font-bold text-gray-900 text-sm line-clamp-2 group-hover:text-blue-600 transition-colors duration-300 mb-2 leading-tight">
                        {news.title}
                      </h4>

                      <div className="flex items-center justify-between text-xs text-gray-500 mb-2">
                        <span>{news.timestamp}</span>
                        <span>{news.readTime}m</span>
                      </div>

                      {/* Tags */}
                      {news.tags && news.tags.length > 0 && (
                        <div className="flex flex-wrap gap-1">
                          {news.tags.slice(0, 2).map((tag) => (
                            <span
                              key={tag}
                              className="text-xs px-2 py-0.5 bg-gray-100 text-gray-600 rounded-full"
                            >
                              {tag}
                            </span>
                          ))}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>

        {/* Navigation Dots */}
        <div className="flex justify-center mt-12 space-x-3">
          {displayedNews.map((_, index) => (
            <button
              key={index}
              onClick={() => setCurrentMainIndex(index)}
              className={`w-4 h-4 rounded-full transition-all duration-300 ${
                index === currentMainIndex
                  ? 'bg-blue-500 scale-125 shadow-lg'
                  : 'bg-gray-300 hover:bg-gray-400'
              }`}
            />
          ))}
        </div>

        {/* View All Button */}
        <div className="text-center mt-8">
          <button className="px-8 py-4 bg-gradient-to-r from-blue-600 to-purple-600 text-white font-semibold rounded-xl hover:from-blue-700 hover:to-purple-700 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5">
            View All News →
          </button>
        </div>
      </div>
    </section>
  );
};
