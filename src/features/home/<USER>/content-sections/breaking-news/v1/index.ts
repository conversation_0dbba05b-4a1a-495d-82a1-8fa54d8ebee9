// Breaking News V1 - Component Exports
// This file exports all V1 components and types

// Main component
export { BreakingNewsV1 } from './BreakingNewsV1';

// Types
export type {
  NewsItem,
  BreakingNewsData,
  BreakingNewsProps,
  CategoryConfig,
  CategoryType,
  FilterOptions,
  AnimationConfig
} from './types';

// Component metadata
export const V1_COMPONENT_INFO = {
  name: 'Breaking News V1',
  description: 'Initial implementation with carousel design and thumbnail grid',
  features: [
    'Auto-rotating carousel',
    'Category-based styling',
    'Thumbnail grid layout',
    'Breaking news indicators',
    'Priority badges',
    'Read time display',
    'Author information',
    'Tag system',
    'Navigation dots',
    'Responsive design'
  ],
  version: '1.0.0',
  status: 'stable'
} as const;

// Default props for V1
export const DEFAULT_PROPS_V1 = {
  className: '',
  autoRotate: true,
  rotationInterval: 8000,
  maxItems: 4,
  showControls: true
} as const;

// Category configurations
export const CATEGORY_CONFIGS = {
  breaking: { color: 'bg-red-500', icon: '🚨', label: 'Breaking' },
  transfer: { color: 'bg-blue-500', icon: '🔄', label: 'Transfer' },
  match: { color: 'bg-green-500', icon: '⚽', label: 'Match' },
  interview: { color: 'bg-purple-500', icon: '🎤', label: 'Interview' }
} as const;

// Animation presets
export const ANIMATION_PRESETS_V1 = {
  fadeIn: {
    duration: 300,
    easing: 'ease-out' as const
  },
  slideUp: {
    duration: 400,
    easing: 'ease-out' as const
  },
  scaleIn: {
    duration: 200,
    easing: 'ease-out' as const
  }
} as const;

// Performance configuration
export const PERFORMANCE_CONFIG_V1 = {
  LAZY_LOADING: true,
  IMAGE_OPTIMIZATION: true,
  ANIMATION_REDUCED_MOTION: true,
  CACHE_STRATEGY: 'stale-while-revalidate',
  PREFETCH_ENABLED: true
} as const;
