// Breaking News - Version Management
// This file manages all versions of Breaking News components

// Version 1 - Initial implementation with carousel and thumbnails
export { BreakingNewsV1 } from './v1/BreakingNewsV1';

// Types - Export all types for external use
export type { 
  NewsItem,
  BreakingNewsData,
  BreakingNewsProps,
  CategoryConfig,
  CategoryType,
  FilterOptions,
  AnimationConfig
} from './v1/types';

// Default export - Current active version
// Change this to switch the default version used throughout the app
export { BreakingNewsV1 as BreakingNews } from './v1/BreakingNewsV1';

// Version mapping for dynamic imports
export const BREAKING_NEWS_VERSIONS = {
  v1: () => import('./v1/BreakingNewsV1').then(m => m.BreakingNewsV1),
  // v2: () => import('./v2/BreakingNewsV2').then(m => m.BreakingNewsV2),
  // v3: () => import('./v3/BreakingNewsV3').then(m => m.BreakingNewsV3),
} as const;

// Version metadata
export const VERSION_INFO = {
  v1: {
    name: 'Breaking News V1',
    description: 'Initial implementation with carousel design and thumbnail grid',
    features: ['News Carousel', 'Thumbnail Grid', 'Auto-rotation', 'Category Filtering', 'Read Time'],
    status: 'stable',
    releaseDate: '2024-01-20'
  }
  // v2: {
  //   name: 'Breaking News V2',
  //   description: 'Enhanced version with advanced features and improved UI/UX',
  //   features: ['Enhanced Carousel', 'Advanced Filtering', 'Real-time Updates', 'Social Integration'],
  //   status: 'development',
  //   releaseDate: 'TBD'
  // }
} as const;

// Current version constant
export const CURRENT_VERSION = 'v1' as const;

// Helper function to get version component
export const getBreakingNewsVersion = (version: keyof typeof BREAKING_NEWS_VERSIONS = CURRENT_VERSION) => {
  return BREAKING_NEWS_VERSIONS[version];
};

// Helper function to get version info
export const getVersionInfo = (version: keyof typeof VERSION_INFO = CURRENT_VERSION) => {
  return VERSION_INFO[version];
};

// Component registry for dynamic imports
export const COMPONENT_REGISTRY = {
  v1: {
    component: () => import('./v1').then(m => m.BreakingNewsV1),
    types: () => import('./v1/types'),
    metadata: VERSION_INFO.v1
  }
  // v2: {
  //   component: () => import('./v2').then(m => m.BreakingNewsV2),
  //   types: () => import('./v2/types'),
  //   metadata: VERSION_INFO.v2
  // }
} as const;
