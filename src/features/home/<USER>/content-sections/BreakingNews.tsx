'use client';

import React, { useState, useEffect } from 'react';

interface NewsItem {
  id: string;
  title: string;
  excerpt: string;
  thumbnail: string;
  category: 'breaking' | 'transfer' | 'match' | 'interview';
  timestamp: string;
  author: string;
  readTime: number;
}

interface BreakingNewsProps {
  className?: string;
}

const BreakingNews: React.FC<BreakingNewsProps> = ({ className = '' }) => {
  const [currentMainIndex, setCurrentMainIndex] = useState(0);
  const [isAutoRotating, setIsAutoRotating] = useState(true);

  // Mock data - replace with real API data
  const newsItems: NewsItem[] = [
    {
      id: '1',
      title: 'Manchester United Signs New Star Player in Record Deal',
      excerpt: 'The Red Devils have completed a stunning transfer that breaks their previous record...',
      thumbnail: 'https://images.unsplash.com/photo-1574629810360-7efbbe195018?w=600&h=400&fit=crop',
      category: 'transfer',
      timestamp: '2 hours ago',
      author: '<PERSON>',
      readTime: 3
    },
    {
      id: '2',
      title: 'Champions League Final Venue Changed Due to Security Concerns',
      excerpt: 'UEFA announces last-minute venue change for the upcoming Champions League final...',
      thumbnail: 'https://images.unsplash.com/photo-1551698618-1dfe5d97d256?w=600&h=400&fit=crop',
      category: 'breaking',
      timestamp: '4 hours ago',
      author: 'Sarah <PERSON>',
      readTime: 2
    },
    {
      id: '3',
      title: 'World Cup Qualifiers: Shocking Upset in Group A',
      excerpt: 'Underdog team defeats former champions in a thrilling match that changes everything...',
      thumbnail: 'https://images.unsplash.com/photo-1508098682722-e99c43a406b2?w=600&h=400&fit=crop',
      category: 'match',
      timestamp: '6 hours ago',
      author: 'Mike Wilson',
      readTime: 4
    },
    {
      id: '4',
      title: 'Exclusive Interview: Manager Reveals Transfer Strategy',
      excerpt: 'In an exclusive sit-down, the manager discusses upcoming transfers and team plans...',
      thumbnail: 'https://images.unsplash.com/photo-1560272564-c83b66b1ad12?w=600&h=400&fit=crop',
      category: 'interview',
      timestamp: '8 hours ago',
      author: 'Emma Davis',
      readTime: 5
    }
  ];

  // Auto-rotate main story every 8 seconds
  useEffect(() => {
    if (!isAutoRotating) return;

    const interval = setInterval(() => {
      setCurrentMainIndex((prev) => (prev + 1) % newsItems.length);
    }, 8000);

    return () => clearInterval(interval);
  }, [isAutoRotating, newsItems.length]);

  const getCategoryColor = (category: NewsItem['category']) => {
    switch (category) {
      case 'breaking': return 'bg-red-500';
      case 'transfer': return 'bg-blue-500';
      case 'match': return 'bg-green-500';
      case 'interview': return 'bg-purple-500';
      default: return 'bg-gray-500';
    }
  };

  const getCategoryIcon = (category: NewsItem['category']) => {
    switch (category) {
      case 'breaking': return '🚨';
      case 'transfer': return '🔄';
      case 'match': return '⚽';
      case 'interview': return '🎤';
      default: return '📰';
    }
  };

  const mainNews = newsItems[currentMainIndex];
  const sideNews = newsItems.filter((_, index) => index !== currentMainIndex).slice(0, 3);

  return (
    <section className={`py-12 bg-gray-50 ${className}`}>
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h2 className="text-3xl font-bold text-gray-900 mb-2">Breaking News</h2>
            <p className="text-gray-600">Stay updated with the latest football news</p>
          </div>

          {/* Auto-rotate control */}
          <button
            onClick={() => setIsAutoRotating(!isAutoRotating)}
            className={`px-4 py-2 rounded-lg transition-all duration-300 ${isAutoRotating
              ? 'bg-blue-500 text-white hover:bg-blue-600'
              : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
              }`}
          >
            {isAutoRotating ? '⏸️ Pause' : '▶️ Play'}
          </button>
        </div>

        {/* News Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Main News (Large) */}
          <div className="lg:col-span-2">
            <div
              className="relative bg-white rounded-2xl shadow-lg overflow-hidden group cursor-pointer transform transition-all duration-300 hover:scale-[1.02] hover:shadow-xl"
              onMouseEnter={() => setIsAutoRotating(false)}
              onMouseLeave={() => setIsAutoRotating(true)}
            >
              {/* Thumbnail */}
              <div className="relative h-64 lg:h-80 overflow-hidden">
                <img
                  src={mainNews.thumbnail}
                  alt={mainNews.title}
                  className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-110"
                />

                {/* Category Badge */}
                <div className={`absolute top-4 left-4 ${getCategoryColor(mainNews.category)} text-white px-3 py-1 rounded-full text-sm font-medium flex items-center space-x-1`}>
                  <span>{getCategoryIcon(mainNews.category)}</span>
                  <span className="capitalize">{mainNews.category}</span>
                </div>

                {/* Gradient Overlay */}
                <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent"></div>
              </div>

              {/* Content */}
              <div className="p-6">
                <h3 className="text-xl lg:text-2xl font-bold text-gray-900 mb-3 line-clamp-2 group-hover:text-blue-600 transition-colors duration-300">
                  {mainNews.title}
                </h3>
                <p className="text-gray-600 mb-4 line-clamp-3">
                  {mainNews.excerpt}
                </p>

                {/* Meta Info */}
                <div className="flex items-center justify-between text-sm text-gray-500">
                  <div className="flex items-center space-x-4">
                    <span>By {mainNews.author}</span>
                    <span>•</span>
                    <span>{mainNews.timestamp}</span>
                  </div>
                  <span>{mainNews.readTime} min read</span>
                </div>
              </div>
            </div>
          </div>

          {/* Side News (Small Cards) */}
          <div className="space-y-4">
            {sideNews.map((news, index) => (
              <div
                key={news.id}
                className="bg-white rounded-xl shadow-md overflow-hidden group cursor-pointer transform transition-all duration-300 hover:scale-[1.02] hover:shadow-lg"
              >
                <div className="flex">
                  {/* Thumbnail */}
                  <div className="relative w-24 h-24 flex-shrink-0 overflow-hidden">
                    <img
                      src={news.thumbnail}
                      alt={news.title}
                      className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-110"
                    />

                    {/* Category Icon */}
                    <div className={`absolute top-1 left-1 ${getCategoryColor(news.category)} text-white w-6 h-6 rounded-full flex items-center justify-center text-xs`}>
                      {getCategoryIcon(news.category)}
                    </div>
                  </div>

                  {/* Content */}
                  <div className="flex-1 p-4">
                    <h4 className="font-semibold text-gray-900 text-sm line-clamp-2 group-hover:text-blue-600 transition-colors duration-300 mb-2">
                      {news.title}
                    </h4>

                    <div className="flex items-center justify-between text-xs text-gray-500">
                      <span>{news.timestamp}</span>
                      <span>{news.readTime}m</span>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Navigation Dots */}
        <div className="flex justify-center mt-8 space-x-2">
          {newsItems.map((_, index) => (
            <button
              key={index}
              onClick={() => setCurrentMainIndex(index)}
              className={`w-3 h-3 rounded-full transition-all duration-300 ${index === currentMainIndex
                ? 'bg-blue-500 scale-125'
                : 'bg-gray-300 hover:bg-gray-400'
                }`}
            />
          ))}
        </div>
      </div>
    </section>
  );
};

export default BreakingNews;
