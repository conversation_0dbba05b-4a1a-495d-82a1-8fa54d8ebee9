'use client';

import React, { useState } from 'react';

interface League {
  id: string;
  name: string;
  country: string;
  logo: string;
  flag: string;
  region: 'europe' | 'asia' | 'america' | 'africa';
  tier: 'top5' | 'major' | 'regional';
  currentSeason: string;
  totalTeams: number;
  currentMatchday: number;
  nextFixtures: {
    date: string;
    homeTeam: string;
    awayTeam: string;
    time: string;
  }[];
  topScorer: {
    name: string;
    team: string;
    goals: number;
  };
  standings: {
    position: number;
    team: string;
    points: number;
  }[];
  liveMatches: number;
}

interface FeaturedLeaguesProps {
  className?: string;
}

const FeaturedLeagues: React.FC<FeaturedLeaguesProps> = ({ className = '' }) => {
  const [activeFilter, setActiveFilter] = useState<'all' | 'top5' | 'europe' | 'asia' | 'america'>('all');

  // Mock data - replace with real API data
  const leagues: League[] = [
    {
      id: '1',
      name: 'Premier League',
      country: 'England',
      logo: 'https://logos-world.net/wp-content/uploads/2020/06/Premier-League-Logo.png',
      flag: '🏴󠁧󠁢󠁥󠁮󠁧󠁿',
      region: 'europe',
      tier: 'top5',
      currentSeason: '2024/25',
      totalTeams: 20,
      currentMatchday: 15,
      nextFixtures: [
        { date: 'Today', homeTeam: 'Arsenal', awayTeam: 'Chelsea', time: '17:30' },
        { date: 'Tomorrow', homeTeam: 'Man City', awayTeam: 'Liverpool', time: '16:00' }
      ],
      topScorer: { name: 'Erling Haaland', team: 'Man City', goals: 18 },
      standings: [
        { position: 1, team: 'Arsenal', points: 40 },
        { position: 2, team: 'Man City', points: 38 },
        { position: 3, team: 'Liverpool', points: 37 }
      ],
      liveMatches: 2
    },
    {
      id: '2',
      name: 'La Liga',
      country: 'Spain',
      logo: 'https://logoeps.com/wp-content/uploads/2013/03/la-liga-vector-logo.png',
      flag: '🇪🇸',
      region: 'europe',
      tier: 'top5',
      currentSeason: '2024/25',
      totalTeams: 20,
      currentMatchday: 14,
      nextFixtures: [
        { date: 'Today', homeTeam: 'Real Madrid', awayTeam: 'Barcelona', time: '21:00' }
      ],
      topScorer: { name: 'Robert Lewandowski', team: 'Barcelona', goals: 16 },
      standings: [
        { position: 1, team: 'Real Madrid', points: 42 },
        { position: 2, team: 'Barcelona', points: 39 },
        { position: 3, team: 'Atletico Madrid', points: 35 }
      ],
      liveMatches: 1
    },
    {
      id: '3',
      name: 'Bundesliga',
      country: 'Germany',
      logo: 'https://logoeps.com/wp-content/uploads/2013/03/bundesliga-vector-logo.png',
      flag: '🇩🇪',
      region: 'europe',
      tier: 'top5',
      currentSeason: '2024/25',
      totalTeams: 18,
      currentMatchday: 13,
      nextFixtures: [
        { date: 'Tomorrow', homeTeam: 'Bayern Munich', awayTeam: 'Dortmund', time: '18:30' }
      ],
      topScorer: { name: 'Harry Kane', team: 'Bayern Munich', goals: 20 },
      standings: [
        { position: 1, team: 'Bayern Munich', points: 36 },
        { position: 2, team: 'Dortmund', points: 30 },
        { position: 3, team: 'RB Leipzig', points: 28 }
      ],
      liveMatches: 0
    },
    {
      id: '4',
      name: 'Champions League',
      country: 'Europe',
      logo: 'https://logoeps.com/wp-content/uploads/2013/03/uefa-champions-league-vector-logo.png',
      flag: '🏆',
      region: 'europe',
      tier: 'major',
      currentSeason: '2024/25',
      totalTeams: 32,
      currentMatchday: 5,
      nextFixtures: [
        { date: 'Tuesday', homeTeam: 'PSG', awayTeam: 'Man City', time: '21:00' }
      ],
      topScorer: { name: 'Kylian Mbappé', team: 'PSG', goals: 8 },
      standings: [
        { position: 1, team: 'Man City', points: 12 },
        { position: 2, team: 'Bayern Munich', points: 11 },
        { position: 3, team: 'PSG', points: 10 }
      ],
      liveMatches: 0
    }
  ];

  const filters = [
    { key: 'all', label: 'All Leagues', icon: '🌍' },
    { key: 'top5', label: 'Top 5', icon: '⭐' },
    { key: 'europe', label: 'Europe', icon: '🇪🇺' },
    { key: 'asia', label: 'Asia', icon: '🌏' },
    { key: 'america', label: 'America', icon: '🌎' }
  ];

  const filteredLeagues = leagues.filter(league => {
    if (activeFilter === 'all') return true;
    if (activeFilter === 'top5') return league.tier === 'top5';
    return league.region === activeFilter;
  });

  return (
    <section className={`py-12 bg-white ${className}`}>
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <div className="text-center mb-8">
          <h2 className="text-3xl font-bold text-gray-900 mb-2">Featured Leagues</h2>
          <p className="text-gray-600">Explore top football competitions worldwide</p>
        </div>

        {/* Filter Tabs */}
        <div className="flex flex-wrap justify-center gap-2 mb-8">
          {filters.map((filter) => (
            <button
              key={filter.key}
              onClick={() => setActiveFilter(filter.key as any)}
              className={`px-4 py-2 rounded-full transition-all duration-300 flex items-center space-x-2 ${activeFilter === filter.key
                ? 'bg-blue-500 text-white shadow-lg scale-105'
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
            >
              <span>{filter.icon}</span>
              <span className="font-medium">{filter.label}</span>
            </button>
          ))}
        </div>

        {/* Leagues Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {filteredLeagues.map((league) => (
            <div
              key={league.id}
              className="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden group cursor-pointer transform transition-all duration-300 hover:scale-105 hover:shadow-xl"
            >
              {/* Header */}
              <div className="relative p-6 bg-gradient-to-br from-blue-50 to-indigo-50">
                {/* Live Indicator */}
                {league.liveMatches > 0 && (
                  <div className="absolute top-4 right-4 bg-red-500 text-white px-2 py-1 rounded-full text-xs font-bold flex items-center space-x-1">
                    <div className="w-2 h-2 bg-white rounded-full animate-pulse"></div>
                    <span>{league.liveMatches} LIVE</span>
                  </div>
                )}

                {/* League Info */}
                <div className="flex items-center space-x-4 mb-4">
                  <div className="relative">
                    <img
                      src={league.logo}
                      alt={league.name}
                      className="w-16 h-16 object-contain rounded-lg bg-white p-2 shadow-md"
                    />
                  </div>
                  <div>
                    <h3 className="font-bold text-gray-900 text-lg">{league.name}</h3>
                    <div className="flex items-center space-x-2 text-gray-600">
                      <span className="text-lg">{league.flag}</span>
                      <span className="text-sm">{league.country}</span>
                    </div>
                  </div>
                </div>

                {/* Season Info */}
                <div className="flex justify-between text-sm text-gray-600">
                  <span>Season {league.currentSeason}</span>
                  <span>MD {league.currentMatchday}</span>
                </div>
              </div>

              {/* Content */}
              <div className="p-6">
                {/* Top Scorer */}
                <div className="mb-4">
                  <h4 className="text-sm font-semibold text-gray-700 mb-2">🥅 Top Scorer</h4>
                  <div className="flex justify-between items-center">
                    <div>
                      <div className="font-medium text-gray-900">{league.topScorer.name}</div>
                      <div className="text-sm text-gray-600">{league.topScorer.team}</div>
                    </div>
                    <div className="text-xl font-bold text-blue-600">{league.topScorer.goals}</div>
                  </div>
                </div>

                {/* Standings Preview */}
                <div className="mb-4">
                  <h4 className="text-sm font-semibold text-gray-700 mb-2">📊 Top 3</h4>
                  <div className="space-y-1">
                    {league.standings.slice(0, 3).map((team) => (
                      <div key={team.position} className="flex justify-between items-center text-sm">
                        <div className="flex items-center space-x-2">
                          <span className="w-4 text-gray-500">{team.position}.</span>
                          <span className="text-gray-900">{team.team}</span>
                        </div>
                        <span className="font-medium text-blue-600">{team.points}pts</span>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Next Fixtures */}
                <div>
                  <h4 className="text-sm font-semibold text-gray-700 mb-2">⚽ Next Fixtures</h4>
                  <div className="space-y-2">
                    {league.nextFixtures.slice(0, 2).map((fixture, index) => (
                      <div key={index} className="text-xs bg-gray-50 rounded-lg p-2">
                        <div className="flex justify-between items-center">
                          <span className="text-gray-600">{fixture.date}</span>
                          <span className="font-medium text-gray-900">{fixture.time}</span>
                        </div>
                        <div className="text-gray-900 mt-1">
                          {fixture.homeTeam} vs {fixture.awayTeam}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>

              {/* Footer */}
              <div className="px-6 pb-6">
                <button className="w-full bg-blue-500 text-white py-2 rounded-lg font-medium transition-all duration-300 hover:bg-blue-600 group-hover:scale-105">
                  View League
                </button>
              </div>
            </div>
          ))}
        </div>

        {/* Show More Button */}
        {filteredLeagues.length === 0 && (
          <div className="text-center py-12">
            <div className="text-gray-400 text-lg mb-2">🔍</div>
            <p className="text-gray-600">No leagues found for this filter</p>
          </div>
        )}
      </div>
    </section>
  );
};

export default FeaturedLeagues;
