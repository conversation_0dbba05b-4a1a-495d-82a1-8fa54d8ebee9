import { QuickNavCard } from '../types';

/**
 * Mock data for Quick Navigation Cards
 */

export const mockQuickNavCards: QuickNavCard[] = [
        {
                id: 'todays-fixtures',
                title: "Today's Fixtures",
                description: "Check out all the matches scheduled for today",
                count: 12,
                href: '/fixtures?date=today',
                icon: 'fixtures',
                theme: 'blue',
                isActive: true
        },
        {
                id: 'live-matches',
                title: 'Live Matches',
                description: 'Watch live scores and real-time updates',
                count: 3,
                href: '/fixtures?status=live',
                icon: 'live',
                theme: 'red',
                isActive: true
        },
        {
                id: 'top-leagues',
                title: 'Top Leagues',
                description: 'Explore the most popular football leagues',
                count: 8,
                href: '/leagues',
                icon: 'leagues',
                theme: 'gold'
        },
        {
                id: 'latest-news',
                title: 'Latest News',
                description: 'Stay updated with the latest sports news',
                count: 24,
                href: '/news',
                icon: 'news',
                theme: 'green'
        }
];

// Export default for easy import
export default mockQuickNavCards;
