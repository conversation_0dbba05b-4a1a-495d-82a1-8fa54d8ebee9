'use client';

import React from 'react';
import { MagneticFieldDemo } from '../components/MagneticFieldDemo';

/**
 * Example page demonstrating the Magnetic Field System
 *
 * This example shows how to integrate the magnetic field system
 * into a sports application with full mobile and accessibility support.
 */
export default function MagneticFieldExample() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 p-4">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-white mb-4">
            ⚡ Magnetic Field System Demo
          </h1>
          <p className="text-gray-300 text-lg max-w-2xl mx-auto">
            Advanced 3D magnetic field effects system with mobile optimization,
            accessibility support, and real-time performance monitoring.
          </p>
        </div>

        {/* Features Grid */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 text-white">
            <div className="text-2xl mb-2">📱</div>
            <h3 className="text-lg font-semibold mb-2">Mobile Optimized</h3>
            <p className="text-sm text-gray-300">
              Auto-detects mobile devices and applies optimizations for better performance and battery life.
            </p>
          </div>

          <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 text-white">
            <div className="text-2xl mb-2">♿</div>
            <h3 className="text-lg font-semibold mb-2">Accessible</h3>
            <p className="text-sm text-gray-300">
              High contrast mode, reduced motion, and screen reader support for inclusive design.
            </p>
          </div>

          <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 text-white">
            <div className="text-2xl mb-2">⚡</div>
            <h3 className="text-lg font-semibold mb-2">High Performance</h3>
            <p className="text-sm text-gray-300">
              Spatial partitioning, performance monitoring, and adaptive quality for smooth 60 FPS.
            </p>
          </div>
        </div>

        {/* Instructions */}
        <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 mb-8 text-white">
          <h2 className="text-xl font-semibold mb-4">🎮 How to Use</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div>
              <h3 className="font-medium mb-2">Desktop:</h3>
              <ul className="space-y-1 text-gray-300">
                <li>• Click anywhere to create magnetic fields</li>
                <li>• Use the debug panel (top-right) to monitor performance</li>
                <li>• Try the &quot;Goal Celebration&quot; and &quot;Energy Wave&quot; buttons</li>
                <li>• Toggle pause/play to see field decay</li>
              </ul>
            </div>
            <div>
              <h3 className="font-medium mb-2">Mobile:</h3>
              <ul className="space-y-1 text-gray-300">
                <li>• Tap to create fields at touch position</li>
                <li>• Swipe to create energy surge along path</li>
                <li>• System auto-optimizes for your device</li>
                <li>• Battery optimization activates when low</li>
              </ul>
            </div>
          </div>
        </div>

        {/* Main Demo */}
        <div className="bg-white/5 backdrop-blur-sm rounded-lg p-4">
          <MagneticFieldDemo
            width={800}
            height={600}
            className="mx-auto"
          />
        </div>

        {/* Technical Details */}
        <div className="mt-8 bg-white/10 backdrop-blur-sm rounded-lg p-6 text-white">
          <h2 className="text-xl font-semibold mb-4">🔧 Technical Features</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 text-sm">
            <div>
              <h3 className="font-medium mb-2 text-blue-300">State Management</h3>
              <ul className="space-y-1 text-gray-300">
                <li>• useReducer for complex state</li>
                <li>• Immutable state updates</li>
                <li>• Optimized re-renders</li>
              </ul>
            </div>
            <div>
              <h3 className="font-medium mb-2 text-green-300">Performance</h3>
              <ul className="space-y-1 text-gray-300">
                <li>• Spatial partitioning O(n) complexity</li>
                <li>• Canvas-based rendering</li>
                <li>• Adaptive quality scaling</li>
              </ul>
            </div>
            <div>
              <h3 className="font-medium mb-2 text-purple-300">Integrations</h3>
              <ul className="space-y-1 text-gray-300">
                <li>• Audio system hooks</li>
                <li>• Particle system support</li>
                <li>• Debug panel interface</li>
              </ul>
            </div>
          </div>
        </div>

        {/* Code Example */}
        <div className="mt-8 bg-gray-900/50 backdrop-blur-sm rounded-lg p-6">
          <h2 className="text-xl font-semibold mb-4 text-white">💻 Code Example</h2>
          <pre className="text-sm text-gray-300 overflow-x-auto">
            <code>{`import { useMagneticFieldSystem } from './hooks/useMagneticFieldSystem';

const MyComponent = () => {
  const magneticSystem = useMagneticFieldSystem({
    maxFields: 25,
    enableVisualization: true,
    performanceMode: false,
    accessibilityMode: false,
    mobileOptimization: true,
    batteryOptimization: true,
    audioSystem: {
      playFieldCreation: (type, strength) => {
        // Play creation sound
      }
    }
  });

  const handleClick = (x, y) => {
    magneticSystem.createMagneticField(x, y, {
      type: 'attract',
      strength: 150,
      radius: 80
    });
  };

  return (
    <div onClick={(e) => handleClick(e.clientX, e.clientY)}>
      <MagneticFieldRenderer
        fields={magneticSystem.visualizationData.fields}
        interactions={magneticSystem.visualizationData.interactions}
        width={800}
        height={600}
      />
    </div>
  );
};`}</code>
          </pre>
        </div>

        {/* Footer */}
        <div className="mt-8 text-center text-gray-400 text-sm">
          <p>
            Built with React, TypeScript, and Canvas API •
            Optimized for performance and accessibility •
            Mobile-first design
          </p>
        </div>
      </div>
    </div>
  );
}
