// Home feature types

export interface NewsItem {
        id: string;
        title: string;
        summary?: string;
        content?: string;
        publishedAt: string;
        author?: string;
        category: 'match' | 'transfer' | 'league' | 'general';
        imageUrl?: string;
        tags?: string[];
        readTime?: number; // in minutes
        slug: string;
        isFeatured?: boolean;
        priority: 'high' | 'medium' | 'low';
}

export interface League {
        id: string;
        name: string;
        logo?: string;
        country: string;
        season: string;
        description?: string;
        teams: number;
        currentMatchday?: number;
        totalMatchdays?: number;
        status: 'active' | 'finished' | 'upcoming';
        startDate?: string;
        endDate?: string;
}

export interface Fixture {
        id: string;
        homeTeam: Team;
        awayTeam: Team;
        scheduledTime: string;
        date: string; // alias for scheduledTime for backwards compatibility
        venue?: string;
        league: {
                id: string;
                name: string;
                logo?: string;
        };
        status: 'scheduled' | 'live' | 'finished' | 'postponed' | 'cancelled';
        homeScore?: number;
        awayScore?: number;
        matchTime?: string;
        minute?: number; // current minute for live matches
        round?: string;
        importance: 'high' | 'medium' | 'low';
}

// Hero Section Types
export interface Team {
        id: string;
        name: string;
        logo?: string;
        abbreviation?: string;
        shortName?: string;
}

export interface LiveScore {
        id: string;
        homeTeam: Team;
        awayTeam: Team;
        homeScore: number;
        awayScore: number;
        matchTime: string; // "45+2", "FT", "HT", etc.
        status: 'live' | 'finished' | 'upcoming' | 'halftime' | 'coming';
        venue?: string;
        league: {
                id: string;
                name: string;
                logo?: string;
        };
        isHot?: boolean; // For trending/important matches
        priority?: 'high' | 'medium' | 'low';
}

export interface FeaturedMatch {
        id: string;
        homeTeam: Team;
        awayTeam: Team;
        scheduledTime: string; // ISO date string
        venue?: string;
        league: {
                id: string;
                name: string;
                logo?: string;
        };
        importance: 'high' | 'medium' | 'low';
        backgroundImage?: string;
        description?: string;
        isHot?: boolean; // For trending/hot matches
        status?: 'live' | 'coming' | 'scheduled';
}

export interface HeroCTAButton {
        label: string;
        href: string;
        variant: 'primary' | 'secondary';
        icon?: React.ReactNode;
}

export interface HeroSectionProps {
        liveScores?: LiveScore[];
        featuredMatch?: FeaturedMatch;
        ctaButtons?: HeroCTAButton[];
        className?: string;
}

// Quick Navigation Types
export interface QuickNavCard {
        id: string;
        title: string;
        description: string;
        count: number;
        href: string;
        icon: 'fixtures' | 'live' | 'leagues' | 'news';
        theme: 'blue' | 'red' | 'gold' | 'green';
        isActive?: boolean;
}

export interface NavigationItem {
        label: string;
        href: string;
        count?: number;
        isNew?: boolean;
}

export interface QuickNavSectionProps {
        cards: QuickNavCard[];
        title?: string;
        className?: string;
}

// Content Section Props
export interface NewsSectionProps {
        news: NewsItem[];
        title?: string;
        showViewAll?: boolean;
        maxItems?: number;
        className?: string;
}

export interface LeaguesSectionProps {
        leagues: League[];
        title?: string;
        showViewAll?: boolean;
        maxItems?: number;
        className?: string;
}

export interface FixturesSectionProps {
        fixtures: Fixture[];
        title?: string;
        showViewAll?: boolean;
        maxItems?: number;
        filterType?: 'today' | 'upcoming' | 'live' | 'all';
        className?: string;
}
