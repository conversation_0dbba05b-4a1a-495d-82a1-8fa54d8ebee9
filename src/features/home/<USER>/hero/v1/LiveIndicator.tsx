'use client';

import React from 'react';

interface LiveIndicatorProps {
        isLive?: boolean;
        size?: 'sm' | 'md' | 'lg';
        className?: string;
}

/**
 * Live Indicator Component
 * Animated red dot to indicate live status
 */
export const LiveIndicator: React.FC<LiveIndicatorProps> = ({
        isLive = false,
        size = 'md',
        className = ''
}) => {
        if (!isLive) return null;

        const sizeClasses = {
                sm: 'w-2 h-2',
                md: 'w-3 h-3',
                lg: 'w-4 h-4'
        };

        return (
                <div className={`flex items-center space-x-2 ${className}`}>
                        <div className={`${sizeClasses[size]} bg-red-500 rounded-full animate-pulse relative`}>
                                {/* Pulse ring effect */}
                                <div className={`absolute inset-0 ${sizeClasses[size]} bg-red-500 rounded-full animate-ping opacity-20`} />
                        </div>
                        <span className="text-red-400 text-sm font-medium">LIVE</span>
                </div>
        );
};

export default LiveIndicator;
