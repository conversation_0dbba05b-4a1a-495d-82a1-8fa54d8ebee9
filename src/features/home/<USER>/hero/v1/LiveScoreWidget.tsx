'use client';

import React from 'react';
import { LiveScore } from '../../../types';
import Link from 'next/link';

interface LiveScoreWidgetProps {
        liveScore: LiveScore;
        className?: string;
}

/**
 * Live Score Widget Component
 * Displays real-time match score with team information
 */
export const LiveScoreWidget: React.FC<LiveScoreWidgetProps> = ({
        liveScore,
        className = ''
}) => {
        const {
                id,
                homeTeam,
                awayTeam,
                homeScore,
                awayScore,
                matchTime,
                status,
                venue,
                league,
                isHot
        } = liveScore;

        const getStatusBadge = () => {
                const baseClasses = "px-2 py-1 rounded-full text-xs font-medium";

                switch (status) {
                        case 'live':
                                return (
                                        <span className={`${baseClasses} bg-red-500 text-white flex items-center space-x-1`}>
                                                <div className="w-1.5 h-1.5 bg-white rounded-full animate-pulse"></div>
                                                <span>LIVE</span>
                                        </span>
                                );
                        case 'halftime':
                                return (
                                        <span className={`${baseClasses} bg-orange-500 text-white`}>
                                                HT
                                        </span>
                                );
                        case 'finished':
                                return (
                                        <span className={`${baseClasses} bg-gray-500 text-white`}>
                                                FT
                                        </span>
                                );
                        case 'coming':
                                return (
                                        <span className={`${baseClasses} bg-green-500 text-white`}>
                                                {matchTime}
                                        </span>
                                );
                        case 'upcoming':
                                return (
                                        <span className={`${baseClasses} bg-blue-500 text-white`}>
                                                {matchTime}
                                        </span>
                                );
                        default:
                                return null;
                }
        };

        const displayScore = status === 'upcoming' || status === 'coming' ? 'vs' : `${homeScore} - ${awayScore}`;
        const showTime = status === 'live' || status === 'halftime';

        // Get dynamic styling based on match importance
        const getCardStyle = () => {
                let baseStyle = "bg-white/10 backdrop-blur-sm rounded-lg p-4 transition-all duration-200 hover:bg-white/20 hover:scale-105";

                if (isHot && status === 'live') {
                        baseStyle += " ring-2 ring-red-400/50 bg-red-500/10";
                } else if (isHot) {
                        baseStyle += " ring-2 ring-orange-400/50 bg-orange-500/10";
                } else if (status === 'live') {
                        baseStyle += " ring-1 ring-red-400/30";
                }

                return baseStyle;
        };

        return (
                <Link
                        href={`/fixtures/${id}`}
                        className={`block group ${className}`}
                        aria-label={`Match between ${homeTeam.name} and ${awayTeam.name}`}
                >
                        <div className={getCardStyle()}>
                                {/* Hot indicator */}
                                {isHot && (
                                        <div className="absolute -top-2 -right-2 bg-red-500 text-white text-xs px-2 py-1 rounded-full font-bold flex items-center space-x-1">
                                                <span>🔥</span>
                                                <span>HOT</span>
                                        </div>
                                )}

                                {/* League info */}
                                <div className="flex items-center justify-between mb-3">
                                        <div className="flex items-center space-x-2">
                                                {league.logo && (
                                                        <img
                                                                src={league.logo}
                                                                alt={`${league.name} logo`}
                                                                className="w-4 h-4 object-contain"
                                                        />
                                                )}
                                                <span className="text-xs text-white/70 font-medium">
                                                        {league.name}
                                                </span>
                                        </div>
                                        {getStatusBadge()}
                                </div>

                                {/* Teams and Score */}
                                <div className="grid grid-cols-5 items-center gap-2">
                                        {/* Home Team */}
                                        <div className="col-span-2 flex items-center space-x-2">
                                                {homeTeam.logo && (
                                                        <img
                                                                src={homeTeam.logo}
                                                                alt={`${homeTeam.name} logo`}
                                                                className="w-6 h-6 object-contain flex-shrink-0"
                                                        />
                                                )}
                                                <span className="text-sm font-medium truncate">
                                                        {homeTeam.abbreviation || homeTeam.name}
                                                </span>
                                        </div>

                                        {/* Score */}
                                        <div className="col-span-1 text-center">
                                                <span className="text-lg font-bold text-white">
                                                        {displayScore}
                                                </span>
                                                {showTime && (
                                                        <div className="text-xs text-white/70">
                                                                {matchTime}&apos;
                                                        </div>
                                                )}
                                        </div>

                                        {/* Away Team */}
                                        <div className="col-span-2 flex items-center justify-end space-x-2">
                                                <span className="text-sm font-medium truncate">
                                                        {awayTeam.abbreviation || awayTeam.name}
                                                </span>
                                                {awayTeam.logo && (
                                                        <img
                                                                src={awayTeam.logo}
                                                                alt={`${awayTeam.name} logo`}
                                                                className="w-6 h-6 object-contain flex-shrink-0"
                                                        />
                                                )}
                                        </div>
                                </div>

                                {/* Venue info */}
                                {venue && (
                                        <div className="mt-2 text-xs text-white/60 text-center">
                                                {venue}
                                        </div>
                                )}
                        </div>
                </Link>
        );
};

export default LiveScoreWidget;
