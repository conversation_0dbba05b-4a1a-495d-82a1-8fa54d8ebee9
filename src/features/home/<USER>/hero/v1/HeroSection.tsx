'use client';

import React from 'react';
import { HeroSectionProps, LiveScore } from '../../../types';
import { LiveScoreWidget } from './LiveScoreWidget';
import { FeaturedMatchCard } from './FeaturedMatchCard';
import { HeroCTA } from './HeroCTA';

/**
 * Hero Section Component
 * Main showcase area at the top of the home page featuring:
 * - Live scores widget with scrollable list
 * - Featured match highlight with hot indicator
 * - Call-to-action buttons
 * - Enhanced focus on live and coming matches
 */
export const HeroSection: React.FC<HeroSectionProps> = ({
        liveScores = [],
        featuredMatch,
        ctaButtons = [],
        className = ''
}) => {
        // Sort scores to prioritize live/hot matches
        const sortedScores = React.useMemo(() => {
                return [...liveScores].sort((a, b) => {
                        // Priority order: live hot > live > coming hot > coming > others
                        const getScore = (score: LiveScore) => {
                                if (score.status === 'live' && score.isHot) return 5;
                                if (score.status === 'live') return 4;
                                if (score.status === 'coming' && score.isHot) return 3;
                                if (score.status === 'coming') return 2;
                                return 1;
                        };
                        return getScore(b) - getScore(a);
                });
        }, [liveScores]);

        return (
                <section
                        className={`relative bg-gradient-to-br from-blue-900 via-blue-800 to-purple-900 text-white py-12 md:py-16 lg:py-20 ${className}`}
                        aria-label="Hero section with live scores and featured content"
                >
                        {/* Background overlay */}
                        <div className="absolute inset-0 bg-black/20" />

                        {/* Content container */}
                        <div className="relative z-10 container mx-auto px-4 sm:px-6 lg:px-8">
                                <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 lg:gap-12">

                                        {/* Left Column: Live Scores with enhanced UI */}
                                        <div className="lg:col-span-1">
                                                <div className="flex items-center justify-between mb-6">
                                                        <h2 className="text-xl md:text-2xl font-bold text-center lg:text-left">
                                                                🔴 Live Sports
                                                        </h2>
                                                        <div className="hidden lg:flex items-center space-x-2">
                                                                <div className="w-2 h-2 bg-red-500 rounded-full animate-pulse"></div>
                                                                <span className="text-sm text-white/70">Live</span>
                                                        </div>
                                                </div>

                                                {/* Scrollable scores container */}
                                                <div className="max-h-96 overflow-y-auto scrollbar-thin scrollbar-thumb-white/20 scrollbar-track-transparent space-y-4">
                                                        {sortedScores.length > 0 ? (
                                                                sortedScores.map(score => (
                                                                        <LiveScoreWidget
                                                                                key={score.id}
                                                                                liveScore={score}
                                                                        />
                                                                ))
                                                        ) : (
                                                                <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4 text-center">
                                                                        <p className="text-white/70">No live matches at the moment</p>
                                                                        <p className="text-sm text-white/50 mt-2">Check back soon for live updates</p>
                                                                </div>
                                                        )}
                                                </div>
                                        </div>

                                        {/* Center Column: Featured Match with Hot indicator */}
                                        <div className="lg:col-span-2">
                                                <div className="flex items-center justify-between mb-6">
                                                        <h2 className="text-xl md:text-2xl font-bold text-center lg:text-left">
                                                                ⭐ Featured Match
                                                        </h2>
                                                        {featuredMatch?.isHot && (
                                                                <div className="flex items-center space-x-2 bg-red-500/20 px-3 py-1 rounded-full">
                                                                        <span className="text-red-400 text-sm">🔥</span>
                                                                        <span className="text-red-300 text-sm font-medium">HOT</span>
                                                                </div>
                                                        )}
                                                </div>

                                                {featuredMatch ? (
                                                        <div className="relative">
                                                                <FeaturedMatchCard
                                                                        featuredMatch={featuredMatch}
                                                                        className="mb-8"
                                                                />
                                                                {/* Live countdown or status indicator */}
                                                                {featuredMatch.status === 'coming' && (
                                                                        <div className="absolute top-4 right-4 bg-green-500/90 text-white px-3 py-1 rounded-full text-sm font-medium">
                                                                                Coming Soon
                                                                        </div>
                                                                )}
                                                                {featuredMatch.status === 'live' && (
                                                                        <div className="absolute top-4 right-4 bg-red-500/90 text-white px-3 py-1 rounded-full text-sm font-medium flex items-center space-x-1">
                                                                                <div className="w-2 h-2 bg-white rounded-full animate-pulse"></div>
                                                                                <span>LIVE</span>
                                                                        </div>
                                                                )}
                                                        </div>
                                                ) : (
                                                        <div className="bg-white/10 backdrop-blur-sm rounded-lg p-8 text-center mb-8">
                                                                <p className="text-white/70">No featured match available</p>
                                                                <p className="text-sm text-white/50 mt-2">Check back for exciting upcoming matches</p>
                                                        </div>
                                                )}

                                                {/* Call-to-Action Buttons */}
                                                {ctaButtons.length > 0 && (
                                                        <HeroCTA
                                                                buttons={ctaButtons}
                                                                className="flex justify-center lg:justify-start"
                                                        />
                                                )}
                                        </div>
                                </div>
                        </div>

                        {/* Decorative elements */}
                        <div className="absolute top-0 right-0 w-64 h-64 bg-white/5 rounded-full blur-3xl" />
                        <div className="absolute bottom-0 left-0 w-48 h-48 bg-purple-500/10 rounded-full blur-3xl" />
                </section>
        );
};

export default HeroSection;
