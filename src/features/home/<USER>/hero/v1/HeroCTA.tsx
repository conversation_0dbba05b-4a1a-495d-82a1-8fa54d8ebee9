'use client';

import React from 'react';
import Link from 'next/link';
import { HeroCTAButton } from '../../../types';

interface HeroCTAProps {
        buttons: HeroCTAButton[];
        className?: string;
}

/**
 * Hero Call-to-Action Component
 * Displays action buttons with different variants and styles
 */
export const HeroCTA: React.FC<HeroCTAProps> = ({
        buttons,
        className = ''
}) => {
        const getButtonClasses = (variant: 'primary' | 'secondary') => {
                const baseClasses = "inline-flex items-center px-6 py-3 rounded-lg font-semibold transition-all duration-200 text-center";

                switch (variant) {
                        case 'primary':
                                return `${baseClasses} bg-yellow-500 hover:bg-yellow-400 text-black shadow-lg hover:shadow-xl transform hover:scale-105`;
                        case 'secondary':
                                return `${baseClasses} bg-white/20 hover:bg-white/30 text-white border border-white/30 hover:border-white/50 backdrop-blur-sm`;
                        default:
                                return baseClasses;
                }
        };

        const getIcon = (label: string) => {
                if (label.toLowerCase().includes('fixture')) {
                        return (
                                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                </svg>
                        );
                }
                if (label.toLowerCase().includes('highlight') || label.toLowerCase().includes('watch')) {
                        return (
                                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1.586a1 1 0 01.707.293l2.414 2.414a1 1 0 00.707.293H15M9 10v4a2 2 0 002 2h2a2 2 0 002-2v-4M9 10V9a2 2 0 012-2h2a2 2 0 012 2v1" />
                                </svg>
                        );
                }
                return null;
        };

        if (buttons.length === 0) return null;

        return (
                <div className={`flex flex-wrap gap-4 ${className}`}>
                        {buttons.map((button, index) => {
                                const icon = button.icon || getIcon(button.label);

                                return (
                                        <Link
                                                key={index}
                                                href={button.href}
                                                className={getButtonClasses(button.variant)}
                                                aria-label={button.label}
                                        >
                                                {icon && (
                                                        <span className="mr-2 flex-shrink-0">
                                                                {icon}
                                                        </span>
                                                )}
                                                <span>{button.label}</span>
                                        </Link>
                                );
                        })}
                </div>
        );
};

export default HeroCTA;
