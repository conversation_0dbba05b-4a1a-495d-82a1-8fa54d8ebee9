'use client';

import React from 'react';
import { FeaturedMatch } from '../../../types';
import Link from 'next/link';
import { formatDate, formatTime } from '../../../../../shared/utils/dateUtils';

interface FeaturedMatchCardProps {
        featuredMatch: FeaturedMatch;
        className?: string;
}

/**
 * Featured Match Card Component
 * Showcases an important upcoming match with attractive design
 */
export const FeaturedMatchCard: React.FC<FeaturedMatchCardProps> = ({
        featuredMatch,
        className = ''
}) => {
        const {
                id,
                homeTeam,
                awayTeam,
                scheduledTime,
                venue,
                league,
                importance,
                backgroundImage,
                description,
                isHot
        } = featuredMatch;

        const matchDate = new Date(scheduledTime);
        const isToday = new Date().toDateString() === matchDate.toDateString();

        const getImportanceBadge = () => {
                const baseClasses = "inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold";

                if (isHot) {
                        return (
                                <span className={`${baseClasses} bg-red-500 text-white animate-pulse`}>
                                        🔥 HOT MATCH
                                </span>
                        );
                }

                switch (importance) {
                        case 'high':
                                return (
                                        <span className={`${baseClasses} bg-red-500 text-white`}>
                                                🔥 Must Watch
                                        </span>
                                );
                        case 'medium':
                                return (
                                        <span className={`${baseClasses} bg-orange-500 text-white`}>
                                                ⭐ Featured
                                        </span>
                                );
                        case 'low':
                                return (
                                        <span className={`${baseClasses} bg-blue-500 text-white`}>
                                                📺 Live
                                        </span>
                                );
                        default:
                                return null;
                }
        };

        // Get dynamic card styling based on hot status
        const getCardStyle = () => {
                let baseStyle = "relative bg-gradient-to-r from-indigo-600 via-purple-600 to-pink-600 rounded-xl overflow-hidden shadow-2xl transition-all duration-300 hover:scale-105 hover:shadow-3xl";

                if (isHot) {
                        baseStyle += " ring-4 ring-red-400/50 animate-pulse";
                }

                return baseStyle;
        };

        return (
                <Link
                        href={`/fixtures/${id}`}
                        className={`block group ${className}`}
                        aria-label={`Featured match: ${homeTeam.name} vs ${awayTeam.name}`}
                >
                        <div
                                className={getCardStyle()}
                                style={{
                                        backgroundImage: backgroundImage ? `url(${backgroundImage})` : undefined,
                                        backgroundSize: 'cover',
                                        backgroundPosition: 'center',
                                }}
                        >
                                {/* Overlay */}
                                <div className="absolute inset-0 bg-gradient-to-r from-black/60 via-black/40 to-black/60" />

                                {/* Content */}
                                <div className="relative z-10 p-6 md:p-8">
                                        <div className="flex items-center justify-between mb-6">
                                                <div className="flex items-center space-x-3">
                                                        {league.logo && (
                                                                <img
                                                                        src={league.logo}
                                                                        alt={`${league.name} logo`}
                                                                        className="w-8 h-8 object-contain"
                                                                />
                                                        )}
                                                        <div>
                                                                <h3 className="text-lg font-bold text-white">
                                                                        {league.name}
                                                                </h3>
                                                                {isToday && (
                                                                        <span className="text-sm text-yellow-400 font-medium">
                                                                                Today
                                                                        </span>
                                                                )}
                                                        </div>
                                                </div>
                                                {getImportanceBadge()}
                                        </div>

                                        {/* Teams */}
                                        <div className="grid grid-cols-3 items-center gap-4 mb-6">
                                                {/* Home Team */}
                                                <div className="text-center">
                                                        {homeTeam.logo && (
                                                                <img
                                                                        src={homeTeam.logo}
                                                                        alt={`${homeTeam.name} logo`}
                                                                        className="w-16 h-16 md:w-20 md:h-20 object-contain mx-auto mb-3"
                                                                />
                                                        )}
                                                        <h4 className="text-lg md:text-xl font-bold text-white">
                                                                {homeTeam.name}
                                                        </h4>
                                                </div>

                                                {/* VS and Time */}
                                                <div className="text-center">
                                                        <div className="text-2xl md:text-3xl font-bold text-white mb-2">
                                                                VS
                                                        </div>
                                                        <div className="bg-white/20 backdrop-blur-sm rounded-lg px-3 py-2">
                                                                <div className="text-sm font-semibold text-white">
                                                                        {formatDate(matchDate)}
                                                                </div>
                                                                <div className="text-lg font-bold text-yellow-400">
                                                                        {formatTime(matchDate)}
                                                                </div>
                                                        </div>
                                                </div>

                                                {/* Away Team */}
                                                <div className="text-center">
                                                        {awayTeam.logo && (
                                                                <img
                                                                        src={awayTeam.logo}
                                                                        alt={`${awayTeam.name} logo`}
                                                                        className="w-16 h-16 md:w-20 md:h-20 object-contain mx-auto mb-3"
                                                                />
                                                        )}
                                                        <h4 className="text-lg md:text-xl font-bold text-white">
                                                                {awayTeam.name}
                                                        </h4>
                                                </div>
                                        </div>

                                        {/* Match Info */}
                                        <div className="text-center">
                                                {venue && (
                                                        <p className="text-white/80 mb-2">
                                                                📍 {venue}
                                                        </p>
                                                )}
                                                {description && (
                                                        <p className="text-white/90 text-sm md:text-base leading-relaxed">
                                                                {description}
                                                        </p>
                                                )}
                                        </div>

                                        {/* Hover effect indicator */}
                                        <div className="absolute bottom-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                                                <div className="bg-white/20 backdrop-blur-sm rounded-full p-2">
                                                        <svg
                                                                className="w-5 h-5 text-white"
                                                                fill="none"
                                                                stroke="currentColor"
                                                                viewBox="0 0 24 24"
                                                        >
                                                                <path
                                                                        strokeLinecap="round"
                                                                        strokeLinejoin="round"
                                                                        strokeWidth={2}
                                                                        d="M9 5l7 7-7 7"
                                                                />
                                                        </svg>
                                                </div>
                                        </div>
                                </div>
                        </div>
                </Link>
        );
};

export default FeaturedMatchCard;
