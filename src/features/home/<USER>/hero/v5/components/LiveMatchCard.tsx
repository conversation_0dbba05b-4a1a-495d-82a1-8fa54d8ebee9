import React from 'react';

interface MatchData {
        id: number;
        homeTeam: string;
        awayTeam: string;
        homeScore?: number;
        awayScore?: number;
        status: 'LIVE' | 'UPCOMING' | 'FINISHED';
        isHot: boolean;
        minute?: string;
        kickoff?: string;
        competition: string;
        homeFlag: string;
        awayFlag: string;
        temperature?: 'HOT' | 'TRENDING' | 'VIP';
        socialBuzz?: {
                tweets: number;
                reactions: number;
                trending: boolean;
        };
}

interface LiveMatchCardProps {
        match: MatchData;
        className?: string;
        onClick?: () => void;
}

/**
 * Live Match Card Component
 * Displays match information with live updates and social buzz
 */
export const LiveMatchCard: React.FC<LiveMatchCardProps> = ({
        match,
        className = '',
        onClick
}) => {
        return (
                <div
                        className={`flex-shrink-0 w-80 bg-white/10 backdrop-blur-md rounded-xl p-4 border transition-all duration-300 hover:scale-105 hover:bg-white/15 cursor-pointer ${match.isHot ? 'border-red-500 shadow-lg shadow-red-500/30' : 'border-white/20'
                                } ${className}`}
                        onClick={onClick}
                >
                        {/* Temperature indicator */}
                        {match.temperature && (
                                <div className="flex justify-end mb-2">
                                        <span className={`text-xs font-bold px-2 py-1 rounded-full animate-pulse ${match.temperature === 'HOT' ? 'bg-gradient-to-r from-red-500 to-orange-500 text-white' :
                                                        match.temperature === 'VIP' ? 'bg-gradient-to-r from-yellow-500 to-orange-500 text-white' :
                                                                'bg-gradient-to-r from-purple-500 to-pink-500 text-white'
                                                }`}>
                                                {match.temperature === 'HOT' ? '🔥 HOT' :
                                                        match.temperature === 'VIP' ? '💎 VIP' : '⭐ TRENDING'}
                                        </span>
                                </div>
                        )}

                        <div className="text-xs text-gray-300 mb-3 font-medium">
                                {match.competition}
                        </div>

                        <div className="space-y-3">
                                <div className="flex items-center justify-between">
                                        <div className="flex items-center space-x-3">
                                                <span className="text-2xl">{match.homeFlag}</span>
                                                <span className="text-white font-semibold">{match.homeTeam}</span>
                                        </div>
                                        {match.status === 'LIVE' && (
                                                <span className="text-2xl font-bold text-white">{match.homeScore}</span>
                                        )}
                                </div>

                                <div className="flex items-center justify-center py-2">
                                        {match.status === 'LIVE' ? (
                                                <div className="flex items-center space-x-2">
                                                        <div className="w-2 h-2 bg-red-500 rounded-full animate-pulse"></div>
                                                        <span className="text-xs text-red-400 font-bold">{match.minute}</span>
                                                        <div className="w-2 h-2 bg-red-500 rounded-full animate-pulse"></div>
                                                </div>
                                        ) : (
                                                <div className="flex items-center space-x-2">
                                                        <div className="w-8 h-0.5 bg-gray-500"></div>
                                                        <span className="text-xs text-gray-400">VS</span>
                                                        <div className="w-8 h-0.5 bg-gray-500"></div>
                                                </div>
                                        )}
                                </div>

                                <div className="flex items-center justify-between">
                                        <div className="flex items-center space-x-3">
                                                <span className="text-2xl">{match.awayFlag}</span>
                                                <span className="text-white font-semibold">{match.awayTeam}</span>
                                        </div>
                                        {match.status === 'LIVE' ? (
                                                <span className="text-2xl font-bold text-white">{match.awayScore}</span>
                                        ) : (
                                                <span className="text-sm text-gray-400">{match.kickoff}</span>
                                        )}
                                </div>
                        </div>

                        <div className="mt-4 flex justify-between items-center">
                                <span className={`text-xs font-bold px-3 py-1 rounded-full ${match.status === 'LIVE' ? 'bg-red-500 text-white animate-pulse' :
                                                match.status === 'UPCOMING' ? 'bg-blue-500 text-white' : 'bg-green-500 text-white'
                                        }`}>
                                        {match.status}
                                </span>

                                {match.socialBuzz && (
                                        <div className="flex items-center space-x-2 text-xs text-gray-400">
                                                <span>🐦 {(match.socialBuzz.tweets / 1000).toFixed(1)}k</span>
                                                <span>❤️ {(match.socialBuzz.reactions / 1000).toFixed(1)}k</span>
                                        </div>
                                )}
                        </div>
                </div>
        );
};

export default LiveMatchCard;