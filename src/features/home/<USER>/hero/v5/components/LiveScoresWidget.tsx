'use client';

import React, { useState, useEffect, useRef } from 'react';
import { ChevronLeftIcon, ChevronRightIcon } from '@heroicons/react/24/outline';

// Live Score Types
interface LiveScore {
  id: number;
  homeTeam: {
    name: string;
    logo: string;
    score: number;
  };
  awayTeam: {
    name: string;
    logo: string;
    score: number;
  };
  league: {
    name: string;
    logo: string;
  };
  status: 'LIVE' | 'FT' | 'HT' | 'UPCOMING';
  minute: number;
  isHot: boolean;
}

// Mock Live Scores Data
const mockLiveScores: LiveScore[] = [
  {
    id: 1,
    homeTeam: { name: 'Arsenal', logo: 'http://116.203.125.65/public/images/teams/42.png', score: 2 },
    awayTeam: { name: 'Chelsea', logo: 'http://116.203.125.65/public/images/teams/49.png', score: 1 },
    league: { name: 'Premier League', logo: 'http://116.203.125.65/public/images/leagues/39.png' },
    status: 'LIVE',
    minute: 67,
    isHot: true
  },
  {
    id: 2,
    homeTeam: { name: 'Man City', logo: 'http://116.203.125.65/public/images/teams/50.png', score: 3 },
    awayTeam: { name: 'Liverpool', logo: 'http://116.203.125.65/public/images/teams/40.png', score: 2 },
    league: { name: 'Premier League', logo: 'http://116.203.125.65/public/images/leagues/39.png' },
    status: 'FT',
    minute: 90,
    isHot: false
  },
  {
    id: 3,
    homeTeam: { name: 'Barcelona', logo: 'http://116.203.125.65/public/images/teams/529.png', score: 1 },
    awayTeam: { name: 'Real Madrid', logo: 'http://116.203.125.65/public/images/teams/541.png', score: 1 },
    league: { name: 'La Liga', logo: 'http://116.203.125.65/public/images/leagues/140.png' },
    status: 'HT',
    minute: 45,
    isHot: true
  },
  {
    id: 4,
    homeTeam: { name: 'Bayern Munich', logo: 'http://116.203.125.65/public/images/teams/157.png', score: 0 },
    awayTeam: { name: 'Dortmund', logo: 'http://116.203.125.65/public/images/teams/165.png', score: 0 },
    league: { name: 'Bundesliga', logo: 'http://116.203.125.65/public/images/leagues/78.png' },
    status: 'UPCOMING',
    minute: 0,
    isHot: false
  },
  {
    id: 5,
    homeTeam: { name: 'PSG', logo: 'http://116.203.125.65/public/images/teams/85.png', score: 2 },
    awayTeam: { name: 'Marseille', logo: 'http://116.203.125.65/public/images/teams/80.png', score: 0 },
    league: { name: 'Ligue 1', logo: 'http://116.203.125.65/public/images/leagues/61.png' },
    status: 'LIVE',
    minute: 34,
    isHot: true
  },
  {
    id: 6,
    homeTeam: { name: 'Juventus', logo: 'http://116.203.125.65/public/images/teams/496.png', score: 1 },
    awayTeam: { name: 'AC Milan', logo: 'http://116.203.125.65/public/images/teams/489.png', score: 3 },
    league: { name: 'Serie A', logo: 'http://116.203.125.65/public/images/leagues/135.png' },
    status: 'FT',
    minute: 90,
    isHot: false
  }
];

interface LiveScoresWidgetProps {
  title?: string;
  maxVisible?: number;
  autoScroll?: boolean;
  scrollInterval?: number;
}

export const LiveScoresWidget: React.FC<LiveScoresWidgetProps> = ({
  title = "Live Scores",
  maxVisible = 3,
  autoScroll = true,
  scrollInterval = 5000
}) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isHovered, setIsHovered] = useState(false);
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const autoScrollRef = useRef<NodeJS.Timeout>();

  // Auto-scroll functionality
  useEffect(() => {
    if (autoScroll && !isHovered && mockLiveScores.length > maxVisible) {
      autoScrollRef.current = setInterval(() => {
        setCurrentIndex(prev => 
          prev + maxVisible >= mockLiveScores.length ? 0 : prev + 1
        );
      }, scrollInterval);
    }

    return () => {
      if (autoScrollRef.current) {
        clearInterval(autoScrollRef.current);
      }
    };
  }, [autoScroll, isHovered, maxVisible, scrollInterval]);

  // Manual scroll functions
  const scrollLeft = () => {
    setCurrentIndex(prev => Math.max(0, prev - 1));
  };

  const scrollRight = () => {
    setCurrentIndex(prev => 
      Math.min(mockLiveScores.length - maxVisible, prev + 1)
    );
  };

  // Get status styling
  const getStatusStyle = (status: string) => {
    switch (status) {
      case 'LIVE':
        return 'bg-red-500 text-white animate-pulse';
      case 'FT':
        return 'bg-gray-500 text-white';
      case 'HT':
        return 'bg-orange-500 text-white';
      case 'UPCOMING':
        return 'bg-blue-500 text-white';
      default:
        return 'bg-gray-500 text-white';
    }
  };

  // Get visible scores
  const visibleScores = mockLiveScores.slice(currentIndex, currentIndex + maxVisible);
  const canScrollLeft = currentIndex > 0;
  const canScrollRight = currentIndex + maxVisible < mockLiveScores.length;

  return (
    <div className="w-full">
      {/* Header */}
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-xl font-bold text-white flex items-center">
          <span className="w-2 h-2 bg-red-500 rounded-full mr-2 animate-pulse"></span>
          {title}
        </h3>
        
        {/* Navigation Controls */}
        {mockLiveScores.length > maxVisible && (
          <div className="flex space-x-2">
            <button
              onClick={scrollLeft}
              disabled={!canScrollLeft}
              className={`p-2 rounded-full transition-all duration-200 ${
                canScrollLeft 
                  ? 'bg-white/20 hover:bg-white/30 text-white' 
                  : 'bg-white/10 text-white/50 cursor-not-allowed'
              }`}
            >
              <ChevronLeftIcon className="w-4 h-4" />
            </button>
            <button
              onClick={scrollRight}
              disabled={!canScrollRight}
              className={`p-2 rounded-full transition-all duration-200 ${
                canScrollRight 
                  ? 'bg-white/20 hover:bg-white/30 text-white' 
                  : 'bg-white/10 text-white/50 cursor-not-allowed'
              }`}
            >
              <ChevronRightIcon className="w-4 h-4" />
            </button>
          </div>
        )}
      </div>

      {/* Scores Container */}
      <div 
        ref={scrollContainerRef}
        className="relative overflow-hidden"
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
      >
        <div 
          className="flex transition-transform duration-500 ease-in-out space-x-4"
          style={{ 
            transform: `translateX(-${currentIndex * (100 / maxVisible)}%)`,
            width: `${(mockLiveScores.length / maxVisible) * 100}%`
          }}
        >
          {mockLiveScores.map((score) => (
            <div
              key={score.id}
              className={`flex-shrink-0 bg-white/10 backdrop-blur-md rounded-xl p-4 border transition-all duration-300 hover:scale-105 hover:bg-white/15 ${
                score.isHot ? 'border-red-500 shadow-lg shadow-red-500/30' : 'border-white/20'
              }`}
              style={{ width: `${100 / maxVisible}%` }}
            >
              {/* League Info */}
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center space-x-2">
                  <img 
                    src={score.league.logo} 
                    alt={score.league.name}
                    className="w-4 h-4 object-contain"
                    onError={(e) => {
                      e.currentTarget.src = 'data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="white"><circle cx="12" cy="12" r="10"/></svg>';
                    }}
                  />
                  <span className="text-xs text-white/70 truncate">{score.league.name}</span>
                </div>
                <span className={`px-2 py-1 rounded-full text-xs font-bold ${getStatusStyle(score.status)}`}>
                  {score.status === 'LIVE' ? `${score.minute}'` : score.status}
                </span>
              </div>

              {/* Teams & Scores */}
              <div className="space-y-2">
                {/* Home Team */}
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2 flex-1 min-w-0">
                    <img 
                      src={score.homeTeam.logo} 
                      alt={score.homeTeam.name}
                      className="w-6 h-6 object-contain flex-shrink-0"
                      onError={(e) => {
                        e.currentTarget.src = 'data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="white"><circle cx="12" cy="12" r="10"/></svg>';
                      }}
                    />
                    <span className="text-white font-medium truncate">{score.homeTeam.name}</span>
                  </div>
                  <span className="text-xl font-bold text-white ml-2">{score.homeTeam.score}</span>
                </div>

                {/* Away Team */}
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2 flex-1 min-w-0">
                    <img 
                      src={score.awayTeam.logo} 
                      alt={score.awayTeam.name}
                      className="w-6 h-6 object-contain flex-shrink-0"
                      onError={(e) => {
                        e.currentTarget.src = 'data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="white"><circle cx="12" cy="12" r="10"/></svg>';
                      }}
                    />
                    <span className="text-white font-medium truncate">{score.awayTeam.name}</span>
                  </div>
                  <span className="text-xl font-bold text-white ml-2">{score.awayTeam.score}</span>
                </div>
              </div>

              {/* Hot Match Indicator */}
              {score.isHot && (
                <div className="mt-3 flex items-center justify-center">
                  <span className="text-xs text-red-400 font-bold animate-pulse">🔥 HOT MATCH</span>
                </div>
              )}
            </div>
          ))}
        </div>
      </div>

      {/* Pagination Dots */}
      {mockLiveScores.length > maxVisible && (
        <div className="flex justify-center mt-4 space-x-2">
          {Array.from({ length: Math.ceil(mockLiveScores.length / maxVisible) }).map((_, index) => (
            <button
              key={index}
              onClick={() => setCurrentIndex(index * maxVisible)}
              className={`w-2 h-2 rounded-full transition-all duration-200 ${
                Math.floor(currentIndex / maxVisible) === index
                  ? 'bg-white'
                  : 'bg-white/30 hover:bg-white/50'
              }`}
            />
          ))}
        </div>
      )}
    </div>
  );
};
