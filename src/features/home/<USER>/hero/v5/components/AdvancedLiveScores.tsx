'use client';

import React, { useState, useEffect, useRef, useCallback, useMemo } from 'react';
// Using SVG icons instead of Heroicons for compatibility

// Enhanced Live Score Types
interface EnhancedLiveScore {
  id: number;
  homeTeam: {
    name: string;
    shortName: string;
    logo: string;
    score: number;
  };
  awayTeam: {
    name: string;
    shortName: string;
    logo: string;
    score: number;
  };
  league: {
    name: string;
    shortName: string;
    logo: string;
    country: string;
  };
  status: 'LIVE' | 'FT' | 'HT' | 'UPCOMING' | 'POSTPONED';
  minute: number;
  isHot: boolean;
  priority: 'HIGH' | 'MEDIUM' | 'LOW';
  lastUpdate: string;
}

// Extended Mock Data for Scrolling Demo
const enhancedMockScores: EnhancedLiveScore[] = [
  {
    id: 1,
    homeTeam: { name: 'Arsenal', shortName: 'ARS', logo: 'http://116.203.125.65/public/images/teams/42.png', score: 2 },
    awayTeam: { name: '<PERSON>', shortName: 'CHE', logo: 'http://116.203.125.65/public/images/teams/49.png', score: 1 },
    league: { name: 'Premier League', shortName: 'EPL', logo: 'http://116.203.125.65/public/images/leagues/39.png', country: 'England' },
    status: 'LIVE', minute: 67, isHot: true, priority: 'HIGH', lastUpdate: '2 min ago'
  },
  {
    id: 2,
    homeTeam: { name: 'Manchester City', shortName: 'MCI', logo: 'http://116.203.125.65/public/images/teams/50.png', score: 3 },
    awayTeam: { name: 'Liverpool', shortName: 'LIV', logo: 'http://116.203.125.65/public/images/teams/40.png', score: 2 },
    league: { name: 'Premier League', shortName: 'EPL', logo: 'http://116.203.125.65/public/images/leagues/39.png', country: 'England' },
    status: 'FT', minute: 90, isHot: false, priority: 'HIGH', lastUpdate: '5 min ago'
  },
  {
    id: 3,
    homeTeam: { name: 'FC Barcelona', shortName: 'BAR', logo: 'http://116.203.125.65/public/images/teams/529.png', score: 1 },
    awayTeam: { name: 'Real Madrid', shortName: 'RMA', logo: 'http://116.203.125.65/public/images/teams/541.png', score: 1 },
    league: { name: 'La Liga', shortName: 'LAL', logo: 'http://116.203.125.65/public/images/leagues/140.png', country: 'Spain' },
    status: 'HT', minute: 45, isHot: true, priority: 'HIGH', lastUpdate: '1 min ago'
  },
  {
    id: 4,
    homeTeam: { name: 'Bayern Munich', shortName: 'BAY', logo: 'http://116.203.125.65/public/images/teams/157.png', score: 0 },
    awayTeam: { name: 'Borussia Dortmund', shortName: 'DOR', logo: 'http://116.203.125.65/public/images/teams/165.png', score: 0 },
    league: { name: 'Bundesliga', shortName: 'BUN', logo: 'http://116.203.125.65/public/images/leagues/78.png', country: 'Germany' },
    status: 'UPCOMING', minute: 0, isHot: false, priority: 'MEDIUM', lastUpdate: '10 min ago'
  },
  {
    id: 5,
    homeTeam: { name: 'Paris Saint-Germain', shortName: 'PSG', logo: 'http://116.203.125.65/public/images/teams/85.png', score: 2 },
    awayTeam: { name: 'Olympique Marseille', shortName: 'MAR', logo: 'http://116.203.125.65/public/images/teams/80.png', score: 0 },
    league: { name: 'Ligue 1', shortName: 'L1', logo: 'http://116.203.125.65/public/images/leagues/61.png', country: 'France' },
    status: 'LIVE', minute: 34, isHot: true, priority: 'MEDIUM', lastUpdate: '30 sec ago'
  },
  {
    id: 6,
    homeTeam: { name: 'Juventus', shortName: 'JUV', logo: 'http://116.203.125.65/public/images/teams/496.png', score: 1 },
    awayTeam: { name: 'AC Milan', shortName: 'MIL', logo: 'http://116.203.125.65/public/images/teams/489.png', score: 3 },
    league: { name: 'Serie A', shortName: 'SA', logo: 'http://116.203.125.65/public/images/leagues/135.png', country: 'Italy' },
    status: 'FT', minute: 90, isHot: false, priority: 'MEDIUM', lastUpdate: '15 min ago'
  },
  {
    id: 7,
    homeTeam: { name: 'Atletico Madrid', shortName: 'ATM', logo: 'http://116.203.125.65/public/images/teams/530.png', score: 0 },
    awayTeam: { name: 'Sevilla FC', shortName: 'SEV', logo: 'http://116.203.125.65/public/images/teams/536.png', score: 1 },
    league: { name: 'La Liga', shortName: 'LAL', logo: 'http://116.203.125.65/public/images/leagues/140.png', country: 'Spain' },
    status: 'LIVE', minute: 78, isHot: false, priority: 'LOW', lastUpdate: '3 min ago'
  },
  {
    id: 8,
    homeTeam: { name: 'Manchester United', shortName: 'MUN', logo: 'http://116.203.125.65/public/images/teams/33.png', score: 2 },
    awayTeam: { name: 'Tottenham', shortName: 'TOT', logo: 'http://116.203.125.65/public/images/teams/47.png', score: 2 },
    league: { name: 'Premier League', shortName: 'EPL', logo: 'http://116.203.125.65/public/images/leagues/39.png', country: 'England' },
    status: 'LIVE', minute: 89, isHot: true, priority: 'HIGH', lastUpdate: '1 min ago'
  }
];

// Import ProcessedFixture type from the hook
interface ProcessedFixture {
  id: number;
  externalId: number;
  slug: string;
  homeTeam: string;
  awayTeam: string;
  homeScore?: number;
  awayScore?: number;
  status: 'LIVE' | 'UPCOMING' | 'FINISHED' | 'NS' | '1H' | '2H' | 'HT' | 'FT' | 'AET' | 'PEN' | 'PST' | 'CANC' | 'ABD' | 'AWD' | 'WO' | string;
  minute?: string;
  kickoff?: string;
  date?: string;
  lastUpdate?: string;
  competition: string;
  homeFlag: string;
  awayFlag: string;
  homeLogo?: string;
  awayLogo?: string;
  temperature?: 'HOT' | 'TRENDING' | 'VIP';
  isHot: boolean;
  isTrending?: boolean;
  isVip?: boolean;
  venue?: string;
  round?: string;
}

interface AdvancedLiveScoresProps {
  title?: string;
  maxVisible?: number;
  autoScroll?: boolean;
  scrollInterval?: number;
  showControls?: boolean;
  compactMode?: boolean;
  // Add props to receive data from parent
  fixtures?: ProcessedFixture[];
  isLoading?: boolean;
  error?: string | null;
}

export const AdvancedLiveScores: React.FC<AdvancedLiveScoresProps> = ({
  title = "Live Scores",
  maxVisible = 10, // Show more items with scrollbar
  autoScroll = true,
  scrollInterval = 3000,
  showControls = true,
  compactMode = false,
  // Receive data from parent instead of calling hook
  fixtures = [],
  isLoading = false,
  error = null
}) => {
  const [isPlaying, setIsPlaying] = useState(autoScroll);
  const [isHovered, setIsHovered] = useState(false);
  const [refreshTime, setRefreshTime] = useState(Date.now()); // Force re-render for relative time updates
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const autoScrollRef = useRef<NodeJS.Timeout>();

  // Update relative time every minute
  useEffect(() => {
    const timeUpdateInterval = setInterval(() => {
      setRefreshTime(Date.now());
    }, 60000); // Update every minute

    return () => clearInterval(timeUpdateInterval);
  }, []);

  // Helper function to recalculate relative time
  const getUpdatedRelativeTime = (utcDateString?: string): string => {
    if (!utcDateString) return 'Unknown';

    try {
      const utcDate = new Date(utcDateString);
      const now = new Date();
      const diffMs = now.getTime() - utcDate.getTime();
      const diffMins = Math.floor(diffMs / (1000 * 60));
      const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
      const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

      if (Math.abs(diffMins) < 1) {
        return 'Just now';
      } else if (Math.abs(diffMins) < 60) {
        return diffMins > 0 ? `${diffMins} min ago` : `in ${Math.abs(diffMins)} min`;
      } else if (Math.abs(diffHours) < 24) {
        return diffHours > 0 ? `${diffHours}h ago` : `in ${Math.abs(diffHours)}h`;
      } else {
        return diffDays > 0 ? `${diffDays} days ago` : `in ${Math.abs(diffDays)} days`;
      }
    } catch (error) {
      console.error('Error parsing UTC date:', utcDateString, error);
      return 'Unknown';
    }
  };

  // Convert API fixtures to enhanced format and sort by priority and live status
  // Use useMemo to optimize performance and include refreshTime as dependency
  const sortedScores = useMemo(() => {
    return fixtures.map(fixture => ({
      id: fixture.id,
      externalId: fixture.externalId, // Use API externalId for navigation
      slug: fixture.slug || `fixture-${fixture.id}`, // Use API slug
      homeTeam: {
        name: fixture.homeTeam,
        shortName: fixture.homeTeam.split(' ').map(w => w[0]).join('').slice(0, 3).toUpperCase(),
        logo: fixture.homeLogo || '',
        score: fixture.homeScore || 0
      },
      awayTeam: {
        name: fixture.awayTeam,
        shortName: fixture.awayTeam.split(' ').map(w => w[0]).join('').slice(0, 3).toUpperCase(),
        logo: fixture.awayLogo || '',
        score: fixture.awayScore || 0
      },
      league: {
        name: fixture.competition,
        shortName: fixture.competition.split(' ').map(w => w[0]).join('').slice(0, 3).toUpperCase(),
        logo: 'http://116.203.125.65/public/images/leagues/39.png', // Default league logo
        country: 'Unknown'
      },
      status: fixture.status,
      minute: parseInt(fixture.minute?.replace("'", '') || '0'),
      isHot: fixture.isHot || fixture.temperature === 'HOT',
      priority: fixture.temperature === 'HOT' ? 'HIGH' : fixture.temperature === 'TRENDING' ? 'MEDIUM' : 'LOW',
      lastUpdate: getUpdatedRelativeTime(fixture.date) // Use real-time calculation
    })).sort((a, b) => {
      if (a.status === 'LIVE' && b.status !== 'LIVE') return -1;
      if (a.status !== 'LIVE' && b.status === 'LIVE') return 1;
      if (a.priority === 'HIGH' && b.priority !== 'HIGH') return -1;
      if (a.priority !== 'HIGH' && b.priority === 'HIGH') return 1;
      return 0;
    });
  }, [fixtures, refreshTime]); // Include refreshTime to trigger re-calculation

  // Auto-scroll functionality for scrollbar
  useEffect(() => {
    if (isPlaying && !isHovered && sortedScores.length > 0) {
      autoScrollRef.current = setInterval(() => {
        if (scrollContainerRef.current) {
          const container = scrollContainerRef.current;
          const scrollAmount = 300; // Scroll 300px each time
          const maxScroll = container.scrollWidth - container.clientWidth;

          if (container.scrollLeft >= maxScroll) {
            // Reset to beginning
            container.scrollTo({ left: 0, behavior: 'smooth' });
          } else {
            // Scroll right
            container.scrollBy({ left: scrollAmount, behavior: 'smooth' });
          }
        }
      }, scrollInterval);
    }

    return () => {
      if (autoScrollRef.current) {
        clearInterval(autoScrollRef.current);
      }
    };
  }, [isPlaying, isHovered, sortedScores.length, scrollInterval]);

  // Navigation functions for scrollbar
  const scrollLeft = useCallback(() => {
    if (scrollContainerRef.current) {
      scrollContainerRef.current.scrollBy({ left: -300, behavior: 'smooth' });
    }
  }, []);

  const scrollRight = useCallback(() => {
    if (scrollContainerRef.current) {
      scrollContainerRef.current.scrollBy({ left: 300, behavior: 'smooth' });
    }
  }, []);

  // Calculate navigation states for scrollbar
  const [canScrollLeft, setCanScrollLeft] = useState(false);
  const [canScrollRight, setCanScrollRight] = useState(true);

  // Update scroll states
  const updateScrollStates = useCallback(() => {
    if (scrollContainerRef.current) {
      const container = scrollContainerRef.current;
      setCanScrollLeft(container.scrollLeft > 0);
      setCanScrollRight(container.scrollLeft < container.scrollWidth - container.clientWidth);
    }
  }, []);

  // Listen for scroll events
  useEffect(() => {
    const container = scrollContainerRef.current;
    if (container) {
      container.addEventListener('scroll', updateScrollStates);
      updateScrollStates(); // Initial check

      return () => container.removeEventListener('scroll', updateScrollStates);
    }
  }, [updateScrollStates]);

  // Get status styling - Updated to handle all API status values
  const getStatusStyle = (status: string, isHot: boolean) => {
    const baseClasses = "text-xs font-bold px-2 py-1 rounded-full";

    // Live statuses (red with animation)
    if (['1H', '2H', 'LIVE'].includes(status)) {
      return `${baseClasses} bg-red-500 text-white ${isHot ? 'animate-pulse' : ''}`;
    }

    // Half time (orange)
    if (status === 'HT') {
      return `${baseClasses} bg-orange-500 text-white`;
    }

    // Finished statuses (gray)
    if (['FT', 'AET', 'PEN'].includes(status)) {
      return `${baseClasses} bg-gray-600 text-white`;
    }

    // Upcoming/Not started (blue)
    if (['NS', 'UPCOMING'].includes(status)) {
      return `${baseClasses} bg-blue-500 text-white`;
    }

    // Postponed/Cancelled statuses (yellow/red)
    if (['PST', 'POSTPONED'].includes(status)) {
      return `${baseClasses} bg-yellow-600 text-white`;
    }

    if (['CANC', 'ABD'].includes(status)) {
      return `${baseClasses} bg-red-600 text-white`;
    }

    if (['AWD', 'WO'].includes(status)) {
      return `${baseClasses} bg-purple-600 text-white`;
    }

    // Default for unknown statuses
    return `${baseClasses} bg-gray-500 text-white`;
  };

  // Get priority indicator
  const getPriorityIndicator = (priority: string) => {
    switch (priority) {
      case 'HIGH': return '🔥';
      case 'MEDIUM': return '⭐';
      case 'LOW': return '📊';
      default: return '';
    }
  };

  // Display all scores with scrollbar
  const visibleScores = sortedScores;

  // Loading state
  if (isLoading) {
    return (
      <div className="w-full">
        <div className="flex items-center justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white"></div>
          <span className="ml-3 text-white">Loading live scores...</span>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="w-full">
        <div className="flex items-center justify-center py-8">
          <span className="text-red-400">Error loading live scores: {error}</span>
        </div>
      </div>
    );
  }

  // No data state
  if (sortedScores.length === 0) {
    return (
      <div className="w-full">
        <div className="flex items-center justify-center py-8">
          <span className="text-white/70">No live scores available</span>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full">
      {/* Header with Controls */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-3">
          <h3 className="text-xl font-bold text-white flex items-center">
            <span className="w-2 h-2 bg-red-500 rounded-full mr-2 animate-pulse"></span>
            {title}
          </h3>
          <span className="text-sm text-gray-400">
            {sortedScores.filter(s => ['1H', '2H', 'HT', 'LIVE'].includes(s.status)).length} live
          </span>
        </div>

        {/* Controls */}
        {showControls && sortedScores.length > 3 && (
          <div className="flex items-center space-x-2">
            {/* Scroll indicator */}
            <span className="text-xs text-gray-400 mr-2">
              {sortedScores.length} matches
            </span>

            {/* Play/Pause */}
            <button
              onClick={() => setIsPlaying(!isPlaying)}
              className="p-2 rounded-full bg-white/20 hover:bg-white/30 text-white transition-all duration-200"
              title={isPlaying ? 'Pause auto-scroll' : 'Resume auto-scroll'}
            >
              {isPlaying ? (
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 9v6m4-6v6" />
                </svg>
              ) : (
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h.01M20 4v5h-5" />
                </svg>
              )}
            </button>

            {/* Navigation */}
            <button
              onClick={scrollLeft}
              disabled={!canScrollLeft}
              className={`p-2 rounded-full transition-all duration-200 ${canScrollLeft
                ? 'bg-white/20 hover:bg-white/30 text-white'
                : 'bg-white/10 text-white/50 cursor-not-allowed'
                }`}
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
            </button>
            <button
              onClick={scrollRight}
              disabled={!canScrollRight}
              className={`p-2 rounded-full transition-all duration-200 ${canScrollRight
                ? 'bg-white/20 hover:bg-white/30 text-white'
                : 'bg-white/10 text-white/50 cursor-not-allowed'
                }`}
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </button>
          </div>
        )}
      </div>

      {/* Scores Container with Scrollbar */}
      <div
        ref={scrollContainerRef}
        className="relative overflow-x-auto overflow-y-hidden scrollbar-thin scrollbar-thumb-white/20 scrollbar-track-transparent hover:scrollbar-thumb-white/40"
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
        style={{
          scrollbarWidth: 'thin',
          scrollbarColor: 'rgba(255,255,255,0.2) transparent'
        }}
      >
        <div className="flex space-x-3 pb-2" style={{ minWidth: 'max-content' }}>
          {visibleScores.map((score) => (
            <div
              key={score.id}
              className={`flex-shrink-0 bg-white/10 backdrop-blur-md rounded-xl border transition-all duration-300 hover:scale-[1.02] hover:bg-white/15 cursor-pointer ${score.isHot ? 'border-red-500 shadow-lg shadow-red-500/30' : 'border-white/20'
                } ${compactMode ? 'p-4' : 'p-5'}`}
              style={{
                width: '280px', // Fixed width for scrollbar
                minWidth: '280px',
                maxWidth: '280px'
              }}
              onClick={() => {
                // Navigate to fixture detail page using API slug and externalId
                window.location.href = `/${score.slug}/${score.externalId}`;
              }}
            >
              {/* Header with League and Status */}
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center space-x-2">
                  <img
                    src={score.league.logo}
                    alt={score.league.name}
                    className="w-4 h-4 object-contain"
                    onError={(e) => {
                      e.currentTarget.src = 'data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="white"><circle cx="12" cy="12" r="10"/></svg>';
                    }}
                  />
                  <span className="text-xs text-white/70 truncate max-w-[140px]">
                    {compactMode ? score.league.shortName : score.league.name}
                  </span>
                  {score.isHot && (
                    <span className="text-xs">🔥</span>
                  )}
                </div>
                <span className={getStatusStyle(score.status, score.isHot)}>
                  {['1H', '2H', 'LIVE'].includes(score.status) ? `${score.minute}'` : score.status}
                </span>
              </div>

              {/* Teams Layout - Side by Side with Large Logos */}
              <div className="flex items-center justify-between mb-4">
                {/* Home Team */}
                <div className="flex flex-col items-center flex-1">
                  <img
                    src={score.homeTeam.logo}
                    alt={score.homeTeam.name}
                    className="w-12 h-12 object-contain mb-2"
                    onError={(e) => {
                      e.currentTarget.src = 'data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="white"><circle cx="12" cy="12" r="10"/></svg>';
                    }}
                  />
                  <span className="text-white font-medium text-center text-xs leading-tight max-w-[80px] truncate">
                    {compactMode ? score.homeTeam.shortName : score.homeTeam.name}
                  </span>
                </div>

                {/* Score and VS */}
                <div className="flex flex-col items-center px-4">
                  {/* Show scores for matches that have started or finished */}
                  {!['NS', 'UPCOMING'].includes(score.status) ? (
                    <div className="flex items-center space-x-2 mb-1">
                      <span className="text-2xl font-bold text-white">{score.homeTeam.score}</span>
                      <span className="text-white/60 text-sm">-</span>
                      <span className="text-2xl font-bold text-white">{score.awayTeam.score}</span>
                    </div>
                  ) : (
                    <div className="text-white/60 text-sm mb-1">VS</div>
                  )}

                  {/* Match Time/Status */}
                  {['1H', '2H', 'LIVE'].includes(score.status) && (
                    <div className="flex items-center space-x-1">
                      <div className="w-1.5 h-1.5 bg-red-500 rounded-full animate-pulse"></div>
                      <span className="text-xs text-red-400 font-bold">{score.minute}'</span>
                    </div>
                  )}
                  {['NS', 'UPCOMING'].includes(score.status) && (
                    <span className="text-xs text-blue-400">Upcoming</span>
                  )}
                  {['FT', 'AET', 'PEN'].includes(score.status) && (
                    <span className="text-xs text-gray-400">Full Time</span>
                  )}
                  {score.status === 'HT' && (
                    <span className="text-xs text-orange-400">Half Time</span>
                  )}
                  {['PST', 'CANC', 'ABD'].includes(score.status) && (
                    <span className="text-xs text-yellow-400">Cancelled</span>
                  )}
                  {['AWD', 'WO'].includes(score.status) && (
                    <span className="text-xs text-purple-400">Awarded</span>
                  )}
                </div>

                {/* Away Team */}
                <div className="flex flex-col items-center flex-1">
                  <img
                    src={score.awayTeam.logo}
                    alt={score.awayTeam.name}
                    className="w-12 h-12 object-contain mb-2"
                    onError={(e) => {
                      e.currentTarget.src = 'data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="white"><circle cx="12" cy="12" r="10"/></svg>';
                    }}
                  />
                  <span className="text-white font-medium text-center text-xs leading-tight max-w-[80px] truncate">
                    {compactMode ? score.awayTeam.shortName : score.awayTeam.name}
                  </span>
                </div>
              </div>

              {/* Footer */}
              <div className="flex items-center justify-center">
                <span className="text-xs text-gray-400">{score.lastUpdate}</span>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Scroll Indicator */}
      {sortedScores.length > 3 && (
        <div className="flex justify-center mt-4">
          <div className="text-xs text-white/50">
            Scroll to see more matches ({sortedScores.length} total)
          </div>
        </div>
      )}
    </div>
  );
};
