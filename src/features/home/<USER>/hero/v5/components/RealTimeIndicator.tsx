import React from 'react';

interface RealTimeIndicatorProps {
        isLive?: boolean;
        className?: string;
}

/**
 * Real-time indicator component
 * Shows live status with pulsing animation
 */
export const RealTimeIndicator: React.FC<RealTimeIndicatorProps> = ({
        isLive = true,
        className = ''
}) => {
        return (
                <div className={`flex items-center space-x-2 ${className}`}>
                        <div
                                className={`w-2 h-2 rounded-full ${isLive ? 'bg-red-500 animate-pulse' : 'bg-gray-500'
                                        }`}
                        />
                        <span
                                className={`text-sm font-medium ${isLive ? 'text-red-400' : 'text-gray-400'
                                        }`}
                        >
                                {isLive ? 'LIVE' : 'OFFLINE'}
                        </span>
                </div>
        );
};

export default RealTimeIndicator;