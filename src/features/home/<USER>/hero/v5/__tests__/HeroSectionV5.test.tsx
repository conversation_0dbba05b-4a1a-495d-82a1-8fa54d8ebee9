import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import { HeroSectionV5 } from '../HeroSectionV5';

// Mock the useRealTimeFixtures hook
jest.mock('../hooks/useRealTimeFixtures', () => ({
  useRealTimeFixtures: () => ({
    fixtures: [],
    isLoading: false,
    error: null,
    refetch: jest.fn()
  })
}));

// Mock IntersectionObserver
global.IntersectionObserver = jest.fn().mockImplementation(() => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn(),
}));

// Mock requestAnimationFrame
global.requestAnimationFrame = jest.fn((cb) => setTimeout(cb, 16));
global.cancelAnimationFrame = jest.fn();

describe('HeroSectionV5', () => {
  const defaultProps = {
    liveScores: [],
    featuredMatch: null,
    ctaButtons: [],
    className: ''
  };

  beforeEach(() => {
    jest.clearAllMocks();
    jest.useFakeTimers();
  });

  afterEach(() => {
    jest.useRealTimers();
  });

  it('renders without crashing', () => {
    render(<HeroSectionV5 {...defaultProps} />);
    expect(screen.getByText('SPORTS')).toBeInTheDocument();
    expect(screen.getByText('COMMAND CENTER')).toBeInTheDocument();
  });

  it('displays the main title and description', () => {
    render(<HeroSectionV5 {...defaultProps} />);

    expect(screen.getByText('SPORTS')).toBeInTheDocument();
    expect(screen.getByText('COMMAND CENTER')).toBeInTheDocument();
    expect(screen.getByText(/Experience live sports like never before/)).toBeInTheDocument();
  });

  it('shows real-time clock', () => {
    render(<HeroSectionV5 {...defaultProps} />);

    // Should show current time
    const timeElement = screen.getByText(/\d{1,2}:\d{2}:\d{2}/);
    expect(timeElement).toBeInTheDocument();
  });

  it('displays live scores section', () => {
    render(<HeroSectionV5 {...defaultProps} />);

    expect(screen.getByText('Live Scores')).toBeInTheDocument();
    expect(screen.getByText('DEMO')).toBeInTheDocument();
  });

  it('shows mock match data when API data is not available', () => {
    render(<HeroSectionV5 {...defaultProps} />);

    // Should show mock matches (using getAllByText since teams appear multiple times)
    expect(screen.getAllByText('Arsenal')[0]).toBeInTheDocument();
    expect(screen.getAllByText('Chelsea')[0]).toBeInTheDocument();
    expect(screen.getAllByText('Premier League')[0]).toBeInTheDocument();
  });

  it('displays featured match spotlight', () => {
    render(<HeroSectionV5 {...defaultProps} />);

    expect(screen.getByText('Featured Match')).toBeInTheDocument();
    expect(screen.getByText("Don't miss the hottest match right now!")).toBeInTheDocument();
  });

  it('shows quick actions section', () => {
    render(<HeroSectionV5 {...defaultProps} />);

    expect(screen.getByText('Quick Actions')).toBeInTheDocument();
    expect(screen.getByText('Watch Live')).toBeInTheDocument();
    expect(screen.getByText('Full Schedule')).toBeInTheDocument();
    expect(screen.getByText('Highlights')).toBeInTheDocument();
  });

  it('displays secondary action buttons', () => {
    render(<HeroSectionV5 {...defaultProps} />);

    expect(screen.getByText('📈 Statistics')).toBeInTheDocument();
    expect(screen.getByText('🏆 Tournaments')).toBeInTheDocument();
    expect(screen.getByText('👥 Teams')).toBeInTheDocument();
    expect(screen.getByText('📰 News')).toBeInTheDocument();
    expect(screen.getByText('🎮 Fantasy')).toBeInTheDocument();
  });

  it('shows floating action button for auto-rotation control', () => {
    render(<HeroSectionV5 {...defaultProps} />);

    const floatingButton = screen.getByTitle(/auto-rotation/);
    expect(floatingButton).toBeInTheDocument();
  });

  it('toggles auto-rotation when floating button is clicked', () => {
    render(<HeroSectionV5 {...defaultProps} />);

    const floatingButton = screen.getByTitle(/Pause auto-rotation/);
    expect(floatingButton).toBeInTheDocument();

    fireEvent.click(floatingButton);

    expect(screen.getByTitle(/Resume auto-rotation/)).toBeInTheDocument();
  });

  it('updates time every second', async () => {
    render(<HeroSectionV5 {...defaultProps} />);

    const initialTime = screen.getByText(/\d{1,2}:\d{2}:\d{2}/);
    const initialTimeText = initialTime.textContent;

    // Fast-forward time by 1 second
    jest.advanceTimersByTime(1000);

    await waitFor(() => {
      const updatedTime = screen.getByText(/\d{1,2}:\d{2}:\d{2}/);
      // Time should have updated (or at least the component should have re-rendered)
      expect(updatedTime).toBeInTheDocument();
    });
  });

  it('displays live match indicators correctly', () => {
    render(<HeroSectionV5 {...defaultProps} />);

    // Should show live indicators for live matches
    const liveIndicators = screen.getAllByText('LIVE');
    expect(liveIndicators.length).toBeGreaterThan(0);

    // Should show pulse animations for live matches
    const pulseElements = document.querySelectorAll('.animate-pulse');
    expect(pulseElements.length).toBeGreaterThan(0);
  });

  it('shows temperature indicators for hot matches', () => {
    render(<HeroSectionV5 {...defaultProps} />);

    expect(screen.getByText('🔥 HOT')).toBeInTheDocument();
    expect(screen.getByText('💎 VIP')).toBeInTheDocument();
    expect(screen.getByText('⭐ TRENDING')).toBeInTheDocument();
  });

  it('displays social buzz metrics', () => {
    render(<HeroSectionV5 {...defaultProps} />);

    // Should show tweet and reaction counts
    const tweetElements = screen.getAllByText(/🐦/);
    const reactionElements = screen.getAllByText(/❤️/);

    expect(tweetElements.length).toBeGreaterThan(0);
    expect(reactionElements.length).toBeGreaterThan(0);
  });

  it('handles mouse movement for interactive effects', () => {
    render(<HeroSectionV5 {...defaultProps} />);

    const heroSection = document.querySelector('section');
    expect(heroSection).toBeInTheDocument();

    // Simulate mouse movement
    fireEvent.mouseMove(heroSection!, { clientX: 100, clientY: 100 });
    fireEvent.mouseEnter(heroSection!);
    fireEvent.mouseLeave(heroSection!);

    // Should not throw any errors
    expect(heroSection).toBeInTheDocument();
  });

  it('applies custom className prop', () => {
    const customClass = 'custom-hero-class';
    render(<HeroSectionV5 {...defaultProps} className={customClass} />);

    const heroSection = document.querySelector('section');
    expect(heroSection).toHaveClass(customClass);
  });

  it('shows loading state when API is loading', () => {
    render(<HeroSectionV5 {...defaultProps} />);

    // Should show DEMO indicator when using mock data
    expect(screen.getByText('DEMO')).toBeInTheDocument();
  });

  it('displays live statistics for live matches', () => {
    render(<HeroSectionV5 {...defaultProps} />);

    // Should show possession, shots, corners for live matches
    expect(screen.getByText('Possession')).toBeInTheDocument();
    expect(screen.getByText('Shots')).toBeInTheDocument();
    expect(screen.getByText('Corners')).toBeInTheDocument();
  });

  it('shows key players information', () => {
    render(<HeroSectionV5 {...defaultProps} />);

    // Should show key players from mock data
    expect(screen.getByText('Bukayo Saka')).toBeInTheDocument();
    expect(screen.getByText('Raheem Sterling')).toBeInTheDocument();
  });

  it('renders energy particles and floating elements', () => {
    render(<HeroSectionV5 {...defaultProps} />);

    // Should have floating sports elements and energy particles
    const floatingElements = document.querySelectorAll('.animate-bounce');
    const pulseElements = document.querySelectorAll('.animate-pulse');

    expect(floatingElements.length).toBeGreaterThan(0);
    expect(pulseElements.length).toBeGreaterThan(0);
  });
});
