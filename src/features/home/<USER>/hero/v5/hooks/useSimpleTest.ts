import { useState, useEffect } from 'react';

export const useSimpleTest = () => {
  console.log('🧪 useSimpleTest hook initialized');

  const [data, setData] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    console.log('🔄 useSimpleTest useEffect triggered');

    const fetchData = async () => {
      try {
        console.log('🚀 Starting simple fetch...');
        setLoading(true);
        setError(null);

        const response = await fetch('/api/football/fixtures?page=1&limit=5');
        console.log('📡 Response received:', response.status);

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const result = await response.json();
        console.log('📊 Data received:', { status: result.status, dataLength: result.data?.length });

        if (result.data && Array.isArray(result.data)) {
          setData(result.data);
          console.log('✅ Data set successfully:', result.data.length, 'items');
        } else {
          console.log('⚠️ No data array found in response');
          setData([]);
        }
      } catch (err) {
        console.error('❌ Fetch error:', err);
        setError(err instanceof Error ? err.message : 'Unknown error');
        setData([]);
      } finally {
        setLoading(false);
        console.log('🏁 Fetch completed');
      }
    };

    fetchData();
  }, []);

  console.log('🎯 useSimpleTest returning:', { data: data.length, loading, error });

  return { data, loading, error };
};
