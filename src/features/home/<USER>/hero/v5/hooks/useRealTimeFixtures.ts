import { useState, useEffect, useCallback, useRef } from 'react';

interface ApiTeam {
        id: number;
        name: string;
        logo?: string;
        code?: string;
}

interface ApiFixture {
        id: number;
        externalId: number;
        leagueId: number;
        leagueName: string;
        isHot: boolean;
        season: number;
        round: string;
        homeTeamId: number;
        homeTeamName: string;
        homeTeamLogo: string;
        awayTeamId: number;
        awayTeamName: string;
        awayTeamLogo: string;
        slug: string;
        date: string;
        venue: {
                id: number;
                name: string;
                city: string;
        };
        referee: string;
        status: string;
        statusLong: string;
        statusExtra: number;
        elapsed: number;
        goalsHome: number;
        goalsAway: number;
        scoreHalftimeHome: number;
        scoreHalftimeAway: number;
        scoreFulltimeHome: number;
        scoreFulltimeAway: number;
        periods: {
                first: number;
                second: number;
        };
        timestamp: string;
        thumb: string | null;
        computedStatus: string;
        minutesUntilStart: number;
}

interface ApiResponse {
        data: ApiFixture[];
        meta: {
                totalItems: number;
                totalPages: number;
                currentPage: number;
                limit: number;
        };
        status: number;
}

interface ProcessedFixture {
        id: number;
        externalId: number; // Add externalId for navigation
        slug: string; // Add slug for navigation
        homeTeam: string;
        awayTeam: string;
        homeScore?: number;
        awayScore?: number;
        status: 'LIVE' | 'UPCOMING' | 'FINISHED' | 'NS' | '1H' | '2H' | 'HT' | 'FT' | 'AET' | 'PEN' | 'PST' | 'CANC' | 'ABD' | 'AWD' | 'WO' | string; // Allow all API status values
        minute?: string;
        kickoff?: string;
        date?: string; // UTC date from API
        lastUpdate?: string; // Relative time string
        competition: string;
        homeFlag: string;
        awayFlag: string;
        homeLogo?: string;
        awayLogo?: string;
        temperature?: 'HOT' | 'TRENDING' | 'VIP';
        isHot: boolean;
        isTrending?: boolean;
        isVip?: boolean;
        venue?: string;
        round?: string;
        liveStats?: {
                possession: [number, number];
                shots: [number, number];
                corners: [number, number];
        };
        keyPlayers?: {
                home: { name: string; position: string; };
                away: { name: string; position: string; };
        };
        socialBuzz?: {
                tweets: number;
                reactions: number;
                trending: boolean;
        };
}

interface UseRealTimeFixturesReturn {
        fixtures: ProcessedFixture[];
        isLoading: boolean;
        error: string | null;
        refetch: () => void;
}

// Helper function to get country flag emoji
const getCountryFlag = (country: string): string => {
        const flagMap: Record<string, string> = {
                'England': '🏴󠁧󠁢󠁥󠁮󠁧󠁿',
                'Spain': '🇪🇸',
                'Germany': '🇩🇪',
                'Italy': '🇮🇹',
                'France': '🇫🇷',
                'Portugal': '🇵🇹',
                'Netherlands': '🇳🇱',
                'Brazil': '🇧🇷',
                'Argentina': '🇦🇷',
                'Belgium': '🇧🇪',
                'Croatia': '🇭🇷',
                'Poland': '🇵🇱',
                'Denmark': '🇩🇰',
                'Sweden': '🇸🇪',
                'Norway': '🇳🇴',
                'Switzerland': '🇨🇭',
                'Austria': '🇦🇹',
                'Czech Republic': '🇨🇿',
                'Ukraine': '🇺🇦',
                'Turkey': '🇹🇷',
                'Greece': '🇬🇷',
                'Serbia': '🇷🇸',
                'Scotland': '🏴󠁧󠁢󠁳󠁣󠁴󠁿',
                'Wales': '🏴󠁧󠁢󠁷󠁬󠁳󠁿',
                'Ireland': '🇮🇪',
                'Russia': '🇷🇺',
                'Mexico': '🇲🇽',
                'USA': '🇺🇸',
                'Canada': '🇨🇦',
                'Japan': '🇯🇵',
                'South Korea': '🇰🇷',
                'Australia': '🇦🇺',
                'Saudi Arabia': '🇸🇦',
                'Qatar': '🇶🇦',
                'UAE': '🇦🇪',
                'Morocco': '🇲🇦',
                'Egypt': '🇪🇬',
                'Nigeria': '🇳🇬',
                'Ghana': '🇬🇭',
                'Senegal': '🇸🇳',
                'Cameroon': '🇨🇲',
                'South Africa': '🇿🇦',
                'Algeria': '🇩🇿',
                'Tunisia': '🇹🇳',
                'Chile': '🇨🇱',
                'Uruguay': '🇺🇾',
                'Colombia': '🇨🇴',
                'Peru': '🇵🇪',
                'Ecuador': '🇪🇨',
                'Venezuela': '🇻🇪',
                'Paraguay': '🇵🇾',
                'Bolivia': '🇧🇴'
        };
        return flagMap[country] || '🏳️';
};

// Helper function to determine match temperature
const getMatchTemperature = (fixture: ApiFixture): 'HOT' | 'TRENDING' | 'VIP' | undefined => {
        const isLive = fixture.status === '1H' || fixture.status === '2H' || fixture.status === 'HT';
        const isImportantLeague = ['Premier League', 'La Liga', 'Serie A', 'Bundesliga', 'Ligue 1', 'Champions League', 'Europa League'].includes(fixture.leagueName);
        const isClassico = (fixture.homeTeamName.includes('Real Madrid') && fixture.awayTeamName.includes('Barcelona')) ||
                (fixture.homeTeamName.includes('Barcelona') && fixture.awayTeamName.includes('Real Madrid')) ||
                (fixture.homeTeamName.includes('Liverpool') && fixture.awayTeamName.includes('Manchester')) ||
                (fixture.homeTeamName.includes('Manchester') && fixture.awayTeamName.includes('Liverpool'));

        if (isClassico) return 'VIP';
        if (isLive && isImportantLeague) return 'HOT';
        if (isImportantLeague) return 'TRENDING';
        return fixture.isHot ? 'HOT' : undefined;
};

// Helper function to generate deterministic social buzz to prevent hydration mismatch
const generateSocialBuzz = (fixture: ApiFixture) => {
        const baseMultiplier = getMatchTemperature(fixture) === 'VIP' ? 3 :
                getMatchTemperature(fixture) === 'HOT' ? 2 : 1;

        // Use deterministic values based on fixture data to prevent hydration mismatch
        const seedValue = fixture.id || 0;

        return {
                tweets: Math.floor((5000 + (seedValue * 1234) % 45000) * baseMultiplier),
                reactions: Math.floor((10000 + (seedValue * 5678) % 190000) * baseMultiplier),
                trending: baseMultiplier > 1
        };
};

// Helper function to generate deterministic live stats to prevent hydration mismatch
const generateLiveStats = (fixture: ApiFixture) => {
        if (fixture.status !== '1H' && fixture.status !== '2H') return undefined;

        // Use deterministic values based on fixture data to prevent hydration mismatch
        const seedValue = fixture.id || 0;

        return {
                possession: [30 + (seedValue * 123) % 40, 30 + (seedValue * 456) % 40] as [number, number],
                shots: [1 + (seedValue * 789) % 15, 1 + (seedValue * 321) % 15] as [number, number],
                corners: [(seedValue * 654) % 10, (seedValue * 987) % 10] as [number, number]
        };
};

// Helper function to generate mock key players
const generateKeyPlayers = (fixture: ApiFixture) => {
        const positions = ['ST', 'LW', 'RW', 'CAM', 'CM', 'CDM', 'LB', 'RB', 'CB', 'GK'];
        const homeNames = ['Player A', 'Star Forward', 'Captain', 'Key Midfielder', 'Top Scorer'];
        const awayNames = ['Player B', 'Main Striker', 'Playmaker', 'Defender', 'Goalkeeper'];

        // Use deterministic selection based on fixture id to prevent hydration mismatch
        const seedValue = fixture.id || 0;

        return {
                home: {
                        name: homeNames[seedValue % homeNames.length],
                        position: positions[seedValue % positions.length]
                },
                away: {
                        name: awayNames[seedValue % awayNames.length],
                        position: positions[(seedValue + 1) % positions.length]
                }
        };
};

// Helper function to calculate relative time from UTC date
const getRelativeTimeFromUTC = (utcDateString?: string): string => {
        if (!utcDateString) return 'Unknown';

        try {
                const utcDate = new Date(utcDateString);
                const now = new Date();
                const diffMs = now.getTime() - utcDate.getTime();
                const diffMins = Math.floor(diffMs / (1000 * 60));
                const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
                const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

                if (Math.abs(diffMins) < 1) {
                        return 'Just now';
                } else if (Math.abs(diffMins) < 60) {
                        return diffMins > 0 ? `${diffMins} min ago` : `in ${Math.abs(diffMins)} min`;
                } else if (Math.abs(diffHours) < 24) {
                        return diffHours > 0 ? `${diffHours}h ago` : `in ${Math.abs(diffHours)}h`;
                } else {
                        return diffDays > 0 ? `${diffDays} days ago` : `in ${Math.abs(diffDays)} days`;
                }
        } catch (error) {
                console.error('Error parsing UTC date:', utcDateString, error);
                return 'Unknown';
        }
};

// Cache for API responses to reduce requests
const fixturesCache = new Map<string, { data: ProcessedFixture[]; timestamp: number; }>();
const CACHE_DURATION = 30000; // 30 seconds cache
const REQUEST_DEBOUNCE_TIME = 1000; // 1 second debounce

/**
 * Custom hook for real-time fixtures data from API
 * Fetches data from http://localhost:3000/football/fixtures/upcoming-and-live
 * Optimized with caching and debouncing to reduce API calls
 */
export const useRealTimeFixtures = (): UseRealTimeFixturesReturn => {
        console.log('🎣 useRealTimeFixtures hook initialized');
        const [fixtures, setFixtures] = useState<ProcessedFixture[]>([]);
        const [isLoading, setIsLoading] = useState(true);
        const [error, setError] = useState<string | null>(null);
        const lastRequestTimeRef = useRef(0);

        const processApiFixture = (apiFixture: ApiFixture): ProcessedFixture => {
                console.log('🔄 Processing fixture:', apiFixture.id, apiFixture.homeTeamName, 'vs', apiFixture.awayTeamName);

                // New logic: If match has started (status !== 'NS'), use API status; otherwise use current logic
                const status = apiFixture.status !== 'NS' ?
                        apiFixture.status : // Use original API status for matches that have started
                        'UPCOMING'; // Use UPCOMING for matches that haven't started (NS = Not Started)





                const temperature = getMatchTemperature(apiFixture);

                // Helper function to build CDN image URL from API path
                const buildImageUrl = (path?: string): string | undefined => {
                        if (!path) return undefined;

                        // Get CDN domain from environment variables
                        const cdnDomain = process.env.NEXT_PUBLIC_CDN_DOMAIN_PICTURE;
                        if (!cdnDomain) {
                                console.warn('⚠️ NEXT_PUBLIC_CDN_DOMAIN_PICTURE not configured');
                                return path; // Fallback to original path
                        }

                        // If path already includes domain, return as is
                        if (path.startsWith('http')) return path;

                        // Build CDN URL: NEXT_PUBLIC_CDN_DOMAIN_PICTURE + path
                        // Example: "public/images/teams/293.png" → "http://116.203.125.65/public/images/teams/293.png"
                        const cdnUrl = `${cdnDomain}/${path}`;
                        console.log('🖼️ Built CDN URL from API path:', path, '→', cdnUrl);
                        return cdnUrl;
                };

                return {
                        id: apiFixture.id,
                        externalId: apiFixture.externalId, // Add externalId for navigation
                        slug: apiFixture.slug, // Add slug for navigation
                        homeTeam: apiFixture.homeTeamName,
                        awayTeam: apiFixture.awayTeamName,
                        homeScore: apiFixture.goalsHome,
                        awayScore: apiFixture.goalsAway,
                        status,
                        minute: apiFixture.elapsed ? `${apiFixture.elapsed}'` : undefined,
                        kickoff: status === 'UPCOMING' ? apiFixture.date ? new Date(apiFixture.date).toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit', hour12: false }) : '20:00' : undefined,
                        date: apiFixture.date, // Store original UTC date
                        lastUpdate: getRelativeTimeFromUTC(apiFixture.date), // Calculate relative time
                        competition: apiFixture.leagueName,
                        homeFlag: getCountryFlag('Japan'), // Default for now, can be improved
                        awayFlag: getCountryFlag('Japan'), // Default for now, can be improved
                        homeLogo: buildImageUrl(apiFixture.homeTeamLogo),
                        awayLogo: buildImageUrl(apiFixture.awayTeamLogo),
                        temperature,
                        isHot: temperature === 'HOT' || temperature === 'VIP',
                        isTrending: temperature === 'TRENDING',
                        isVip: temperature === 'VIP',
                        venue: apiFixture.venue?.name,
                        round: apiFixture.round,
                        liveStats: generateLiveStats(apiFixture),
                        keyPlayers: generateKeyPlayers(apiFixture),
                        socialBuzz: generateSocialBuzz(apiFixture)
                };
        };

        const fetchFixtures = useCallback(async () => {
                try {
                        const now = Date.now();
                        const cacheKey = 'fixtures-page-1-limit-100';

                        // Check debounce - prevent too frequent requests
                        if (now - lastRequestTimeRef.current < REQUEST_DEBOUNCE_TIME) {
                                console.log('⏳ Request debounced, skipping...');
                                return;
                        }

                        // Check cache first
                        const cached = fixturesCache.get(cacheKey);
                        if (cached && (now - cached.timestamp) < CACHE_DURATION) {
                                console.log('💾 Using cached fixtures data');
                                setFixtures(cached.data);
                                setIsLoading(false);
                                setError(null);
                                return;
                        }

                        console.log('🚀 Starting fetchFixtures...');
                        setIsLoading(true);
                        setError(null);
                        lastRequestTimeRef.current = now;

                        // Use Next.js API proxy to protect the main API
                        const proxyUrl = '/api/football/fixtures?page=1&limit=100';
                        console.log('🔌 Fetching fixtures via Next.js proxy:', proxyUrl);

                        const response = await fetch(proxyUrl);

                        if (!response.ok) {
                                throw new Error(`HTTP error! status: ${response.status}`);
                        }

                        const data: ApiResponse = await response.json();
                        console.log('🔍 Raw API response:', { status: data.status, dataLength: data.data?.length });

                        if (data.status !== 200) {
                                throw new Error(`API error: Status ${data.status}`);
                        }

                        if (!data.data || !Array.isArray(data.data)) {
                                throw new Error('Invalid API response: data.data is not an array');
                        }

                        console.log('🔄 Processing', data.data.length, 'fixtures...');



                        const processedFixtures = data.data.map((fixture, index) => {
                                try {
                                        return processApiFixture(fixture);
                                } catch (error) {
                                        console.error(`❌ Error processing fixture ${index}:`, error, fixture);
                                        throw error;
                                }
                        });

                        // Keep original API order - display from left to right corresponding to top to bottom in API response
                        const sortedFixtures = processedFixtures; // No sorting, preserve API order

                        // Original sorting logic (commented out to preserve API order):
                        // const sortedFixtures = processedFixtures.sort((a: ProcessedFixture, b: ProcessedFixture) => {
                        //         // Helper function to get status priority
                        //         const getStatusPriority = (status: string): number => {
                        //                 // Live statuses (highest priority)
                        //                 if (['1H', '2H', 'HT', 'LIVE'].includes(status)) return 0;
                        //                 // Upcoming statuses
                        //                 if (['NS', 'UPCOMING'].includes(status)) return 1;
                        //                 // Finished statuses (lowest priority)
                        //                 if (['FT', 'AET', 'PEN', 'FINISHED', 'PST', 'CANC', 'ABD', 'AWD', 'WO'].includes(status)) return 2;
                        //                 // Unknown statuses
                        //                 return 3;
                        //         };
                        //
                        //         const statusDiff = getStatusPriority(a.status) - getStatusPriority(b.status);
                        //         if (statusDiff !== 0) return statusDiff;
                        //
                        //         // Within same status, prioritize hot/VIP matches
                        //         if (a.isVip && !b.isVip) return -1;
                        //         if (!a.isVip && b.isVip) return 1;
                        //         if (a.isHot && !b.isHot) return -1;
                        //         if (!a.isHot && b.isHot) return 1;
                        //
                        //         return 0;
                        // });

                        // Cache the processed data
                        fixturesCache.set(cacheKey, {
                                data: sortedFixtures,
                                timestamp: now
                        });

                        setFixtures(sortedFixtures);
                        console.log('✅ Successfully processed', sortedFixtures.length, 'fixtures');
                        console.log('💾 Data cached for', CACHE_DURATION / 1000, 'seconds');
                        console.log('🎯 Sample fixture:', sortedFixtures[0]);
                } catch (err) {
                        console.error('Error fetching fixtures:', err);
                        setError(err instanceof Error ? err.message : 'Failed to fetch fixtures');

                        // Fallback to empty array on error
                        setFixtures([]);
                } finally {
                        setIsLoading(false);
                }
        }, []); // Remove lastRequestTime from dependency to prevent infinite re-creation

        useEffect(() => {
                console.log('🔄 useEffect triggered, calling fetchFixtures...');
                fetchFixtures();

                // Set up polling for live updates every 30 seconds
                const interval = setInterval(() => {
                        console.log('⏰ Interval triggered, calling fetchFixtures...');
                        fetchFixtures();
                }, 30000);

                return () => clearInterval(interval);
        }, [fetchFixtures]);

        const refetch = () => {
                // Clear cache on manual refetch
                fixturesCache.clear();
                console.log('🗑️ Cache cleared for manual refetch');
                fetchFixtures();
        };

        return { fixtures, isLoading, error, refetch };
};