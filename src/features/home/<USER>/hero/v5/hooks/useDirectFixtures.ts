'use client';

import { useState } from 'react';

// Direct Fixture Type for UI
export interface DirectFixture {
  id: number;
  homeTeam: {
    id: number;
    name: string;
    logo: string;
  };
  awayTeam: {
    id: number;
    name: string;
    logo: string;
  };
  league: {
    id: number;
    name: string;
  };
  match: {
    date: string;
    time: string;
    venue: string;
    status: string;
    statusLong: string;
    elapsed: number;
  };
  score: {
    home: number;
    away: number;
    halftimeHome: number;
    halftimeAway: number;
  };
  isLive: boolean;
  isUpcoming: boolean;
  minutesUntilStart: number;
}

export const useDirectFixtures = () => {
  console.log('🎣 useDirectFixtures hook initialized');

  const [fixtures, setFixtures] = useState<DirectFixture[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Helper function to build CDN image URL
  const buildCDNImageUrl = (path: string): string => {
    const cdnDomain = process.env.NEXT_PUBLIC_CDN_DOMAIN_PICTURE || 'http://116.203.125.65';
    if (!path) return `${cdnDomain}/public/images/teams/default.png`;

    // Remove leading slash if present
    const cleanPath = path.startsWith('/') ? path.slice(1) : path;
    const fullUrl = `${cdnDomain}/${cleanPath}`;

    console.log('🖼️ Built CDN URL:', fullUrl);
    return fullUrl;
  };

  // Process API fixture to UI format
  const processFixture = (apiFixture: any): DirectFixture => {
    const matchDate = new Date(apiFixture.date);
    const isLive = apiFixture.computedStatus === 'LIVE' || apiFixture.status === 'LIVE';
    const isUpcoming = apiFixture.computedStatus === 'UPCOMING' || apiFixture.status === 'NS';

    return {
      id: apiFixture.id,
      homeTeam: {
        id: apiFixture.homeTeamId,
        name: apiFixture.homeTeamName,
        logo: buildCDNImageUrl(apiFixture.homeTeamLogo)
      },
      awayTeam: {
        id: apiFixture.awayTeamId,
        name: apiFixture.awayTeamName,
        logo: buildCDNImageUrl(apiFixture.awayTeamLogo)
      },
      league: {
        id: apiFixture.leagueId,
        name: apiFixture.leagueName
      },
      match: {
        date: matchDate.toLocaleDateString(),
        time: matchDate.toLocaleTimeString('en-US', {
          hour: '2-digit',
          minute: '2-digit',
          hour12: false
        }),
        venue: `${apiFixture.venue.name}, ${apiFixture.venue.city}`,
        status: apiFixture.status,
        statusLong: apiFixture.statusLong,
        elapsed: apiFixture.elapsed
      },
      score: {
        home: apiFixture.goalsHome || 0,
        away: apiFixture.goalsAway || 0,
        halftimeHome: apiFixture.scoreHalftimeHome || 0,
        halftimeAway: apiFixture.scoreHalftimeAway || 0
      },
      isLive,
      isUpcoming,
      minutesUntilStart: apiFixture.minutesUntilStart || 0
    };
  };

  // Direct fetch function
  const fetchFixtures = async () => {
    try {
      console.log('🚀 Starting direct fetchFixtures...');
      setIsLoading(true);
      setError(null);

      const baseUrl = typeof window !== 'undefined' ? window.location.origin : 'http://localhost:5000';
      const response = await fetch(`${baseUrl}/api/football/fixtures?page=1&limit=15`);
      console.log('📡 Direct response status:', response.status);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      console.log('📊 Direct API Response:', {
        status: result.status,
        dataLength: result.data?.length,
        totalItems: result.meta?.totalItems
      });

      if (result.data && Array.isArray(result.data)) {
        const processedFixtures = result.data.map(processFixture);
        setFixtures(processedFixtures);
        console.log('✅ Direct processed fixtures:', processedFixtures.length);
        console.log('🎯 Direct sample fixture:', processedFixtures[0]);
      } else {
        console.log('⚠️ No data array found in direct response');
        setFixtures([]);
      }
    } catch (err) {
      console.error('❌ Direct fetch error:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch fixtures');
      setFixtures([]);
    } finally {
      setIsLoading(false);
      console.log('🏁 Direct fetch completed');
    }
  };

  // Auto-fetch on first call
  if (fixtures.length === 0 && !isLoading && !error) {
    console.log('🔄 Auto-triggering direct fetch...');
    fetchFixtures();
  }

  console.log('🎯 Direct hook returning:', {
    fixtures: fixtures.length,
    isLoading,
    error: error ? 'Has error' : 'No error'
  });

  return {
    fixtures,
    isLoading,
    error,
    refetch: fetchFixtures,
    totalMatches: fixtures.length
  };
};
