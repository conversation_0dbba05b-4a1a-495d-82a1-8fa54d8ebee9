'use client';

import dynamic from 'next/dynamic';
import { HeroSectionProps } from '../types';

// Dynamic import with SSR disabled to prevent hydration mismatch
const HeroSectionV5 = dynamic(
  () => import('./HeroSectionV5'),
  {
    ssr: false,
    loading: () => (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-blue-900 to-purple-900 flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <div className="text-white text-xl font-semibold">Loading Sports Command Center...</div>
          <div className="text-gray-300 text-sm mt-2">Preparing live scores and featured matches</div>
        </div>
      </div>
    )
  }
);

/**
 * Client-side wrapper for HeroSectionV5 to prevent hydration mismatch
 * This ensures the component only renders on the client side
 */
const HeroSectionV5ClientWrapper: React.FC<HeroSectionProps> = (props) => {
  return <HeroSectionV5 {...props} />;
};

export default HeroSectionV5ClientWrapper;
