'use client';

import React, { useState, useEffect } from 'react';
import { HeroSectionProps } from '../../../types';
import Link from 'next/link';

/**
 * Hero Section V2 - Modern Sports Dashboard
 * Features:
 * - Immersive stadium background with parallax effect
 * - Interactive match cards with hover animations
 * - Real-time score ticker
 * - Dynamic match spotlight carousel
 * - Live pulse animations
 * - Interactive statistics display
 */
export const HeroSectionV2: React.FC<HeroSectionProps> = ({
        liveScores = [],
        featuredMatch,
        className = ''
}) => {
        const [currentTime, setCurrentTime] = useState(new Date());
        const [activeMatchIndex, setActiveMatchIndex] = useState(0);
        const [isAutoPlay, setIsAutoPlay] = useState(true);

        // Update time every second
        useEffect(() => {
                const timer = setInterval(() => setCurrentTime(new Date()), 1000);
                return () => clearInterval(timer);
        }, []);

        // Auto-rotate featured matches
        useEffect(() => {
                if (!isAutoPlay || !liveScores.length) return;

                const interval = setInterval(() => {
                        setActiveMatchIndex(prev => (prev + 1) % Math.min(liveScores.length, 3));
                }, 5000);

                return () => clearInterval(interval);
        }, [liveScores.length, isAutoPlay]);

        // Get live matches for ticker
        const liveMatches = liveScores.filter(score => score.status === 'live');
        const upcomingMatches = liveScores.filter(score => score.status === 'coming');

        // Stats for dashboard
        const stats = {
                live: liveMatches.length,
                upcoming: upcomingMatches.length,
                total: liveScores.length
        };

        return (
                <section className={`relative overflow-hidden ${className}`}>
                        {/* Dynamic Background with Stadium Effect */}
                        <div className="absolute inset-0 bg-gradient-to-br from-slate-900 via-blue-900 to-purple-900">
                                {/* Animated background patterns */}
                                <div className="absolute inset-0 opacity-20">
                                        <div className="absolute top-0 left-1/4 w-96 h-96 bg-blue-500 rounded-full mix-blend-multiply filter blur-xl animate-pulse"></div>
                                        <div className="absolute top-0 right-1/4 w-96 h-96 bg-purple-500 rounded-full mix-blend-multiply filter blur-xl animate-pulse delay-1000"></div>
                                        <div className="absolute -bottom-8 left-1/3 w-96 h-96 bg-pink-500 rounded-full mix-blend-multiply filter blur-xl animate-pulse delay-500"></div>
                                </div>

                                {/* Stadium lights effect */}
                                <div className="absolute top-0 left-0 right-0 h-2 bg-gradient-to-r from-transparent via-white/30 to-transparent"></div>
                                <div className="absolute bottom-0 left-0 right-0 h-2 bg-gradient-to-r from-transparent via-white/20 to-transparent"></div>
                        </div>

                        {/* Live Score Ticker */}
                        {liveMatches.length > 0 && (
                                <div className="relative z-10 bg-red-600/90 backdrop-blur-sm text-white py-2 overflow-hidden">
                                        <div className="flex items-center space-x-8 animate-scroll">
                                                <div className="flex items-center space-x-2 whitespace-nowrap">
                                                        <div className="w-2 h-2 bg-white rounded-full animate-pulse"></div>
                                                        <span className="font-bold text-sm">LIVE NOW</span>
                                                </div>
                                                {liveMatches.map((match, index) => (
                                                        <div key={match.id} className="flex items-center space-x-4 whitespace-nowrap">
                                                                <span className="font-medium text-sm">
                                                                        {match.homeTeam.name} {match.homeScore} - {match.awayScore} {match.awayTeam.name}
                                                                </span>
                                                                <span className="text-yellow-300 text-xs">{match.matchTime}&apos;</span>
                                                                {index < liveMatches.length - 1 && <span className="text-white/60">•</span>}
                                                        </div>
                                                ))}
                                        </div>
                                </div>
                        )}

                        {/* Main Hero Content */}
                        <div className="relative z-10 container mx-auto px-4 py-16 lg:py-24">
                                {/* Header with Live Stats */}
                                <div className="text-center mb-12">
                                        <div className="flex items-center justify-center space-x-6 mb-6">
                                                <div className="bg-white/10 backdrop-blur-sm rounded-xl px-4 py-2 text-white">
                                                        <div className="text-2xl font-bold">{stats.live}</div>
                                                        <div className="text-xs text-white/70">Live Now</div>
                                                </div>
                                                <div className="bg-white/10 backdrop-blur-sm rounded-xl px-4 py-2 text-white">
                                                        <div className="text-2xl font-bold">{stats.upcoming}</div>
                                                        <div className="text-xs text-white/70">Coming Up</div>
                                                </div>
                                                <div className="bg-white/10 backdrop-blur-sm rounded-xl px-4 py-2 text-white">
                                                        <div className="text-lg font-bold">{currentTime.toLocaleTimeString()}</div>
                                                        <div className="text-xs text-white/70">Live Time</div>
                                                </div>
                                        </div>

                                        <h1 className="text-4xl md:text-6xl lg:text-7xl font-extrabold text-white mb-4">
                                                <span className="bg-gradient-to-r from-blue-400 via-purple-400 to-pink-400 bg-clip-text text-transparent">
                                                        SPORTS
                                                </span>
                                                <span className="text-white block mt-2">
                                                        ARENA
                                                </span>
                                        </h1>
                                        <p className="text-xl md:text-2xl text-white/80 max-w-3xl mx-auto">
                                                Experience the thrill of live sports with real-time updates, highlights, and exclusive content
                                        </p>
                                </div>

                                {/* Interactive Match Spotlight */}
                                <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-12">
                                        {/* Live Matches Spotlight */}
                                        <div className="lg:col-span-2">
                                                <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20">
                                                        <div className="flex items-center justify-between mb-6">
                                                                <h2 className="text-2xl font-bold text-white flex items-center">
                                                                        <span className="w-3 h-3 bg-red-500 rounded-full animate-pulse mr-3"></span>
                                                                        Match Spotlight
                                                                </h2>
                                                                <div className="flex items-center space-x-2">
                                                                        <button
                                                                                onClick={() => setIsAutoPlay(!isAutoPlay)}
                                                                                className="p-2 rounded-lg bg-white/10 hover:bg-white/20 transition-colors"
                                                                        >
                                                                                {isAutoPlay ? '⏸️' : '▶️'}
                                                                        </button>
                                                                </div>
                                                        </div>

                                                        {liveScores.length > 0 ? (
                                                                <div className="space-y-4">
                                                                        {liveScores.slice(0, 3).map((match, index) => (
                                                                                <div
                                                                                        key={match.id}
                                                                                        className={`transform transition-all duration-500 ${index === activeMatchIndex
                                                                                                ? 'scale-105 bg-gradient-to-r from-blue-600/30 to-purple-600/30'
                                                                                                : 'bg-white/5 hover:bg-white/10'
                                                                                                } rounded-xl p-4 cursor-pointer border border-white/10`}
                                                                                        onClick={() => setActiveMatchIndex(index)}
                                                                                >
                                                                                        <div className="flex items-center justify-between">
                                                                                                <div className="flex items-center space-x-4">
                                                                                                        {match.homeTeam.logo && (
                                                                                                                <img
                                                                                                                        src={match.homeTeam.logo}
                                                                                                                        alt={match.homeTeam.name}
                                                                                                                        className="w-10 h-10 object-contain"
                                                                                                                />
                                                                                                        )}
                                                                                                        <div>
                                                                                                                <div className="text-white font-semibold">{match.homeTeam.name}</div>
                                                                                                                <div className="text-white/60 text-sm">{match.league.name}</div>
                                                                                                        </div>
                                                                                                </div>

                                                                                                <div className="text-center">
                                                                                                        {match.status === 'live' ? (
                                                                                                                <div className="text-2xl font-bold text-white">
                                                                                                                        {match.homeScore} - {match.awayScore}
                                                                                                                </div>
                                                                                                        ) : (
                                                                                                                <div className="text-white/80">
                                                                                                                        {match.matchTime}
                                                                                                                </div>
                                                                                                        )}
                                                                                                        <div className={`text-xs px-2 py-1 rounded-full mt-1 ${match.status === 'live'
                                                                                                                ? 'bg-red-500 text-white'
                                                                                                                : 'bg-blue-500 text-white'
                                                                                                                }`}>
                                                                                                                {match.status === 'live' ? `${match.matchTime}&apos;` : 'Upcoming'}
                                                                                                        </div>
                                                                                                </div>

                                                                                                <div className="flex items-center space-x-4">
                                                                                                        <div className="text-right">
                                                                                                                <div className="text-white font-semibold">{match.awayTeam.name}</div>
                                                                                                                <div className="text-white/60 text-sm">{match.venue}</div>
                                                                                                        </div>
                                                                                                        {match.awayTeam.logo && (
                                                                                                                <img
                                                                                                                        src={match.awayTeam.logo}
                                                                                                                        alt={match.awayTeam.name}
                                                                                                                        className="w-10 h-10 object-contain"
                                                                                                                />
                                                                                                        )}
                                                                                                </div>
                                                                                        </div>

                                                                                        {match.isHot && (
                                                                                                <div className="absolute top-2 right-2 bg-red-500 text-white px-2 py-1 rounded-full text-xs font-bold animate-pulse">
                                                                                                        🔥 HOT
                                                                                                </div>
                                                                                        )}
                                                                                </div>
                                                                        ))}
                                                                </div>
                                                        ) : (
                                                                <div className="text-center py-8 text-white/60">
                                                                        <div className="text-4xl mb-4">⚽</div>
                                                                        <p>No matches available right now</p>
                                                                </div>
                                                        )}
                                                </div>
                                        </div>

                                        {/* Quick Actions Sidebar */}
                                        <div className="space-y-6">
                                                {/* Quick Stats */}
                                                <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20">
                                                        <h3 className="text-xl font-bold text-white mb-4">Quick Stats</h3>
                                                        <div className="space-y-3">
                                                                <div className="flex justify-between items-center">
                                                                        <span className="text-white/70">Goals Today</span>
                                                                        <span className="text-white font-bold text-xl">47</span>
                                                                </div>
                                                                <div className="flex justify-between items-center">
                                                                        <span className="text-white/70">Active Players</span>
                                                                        <span className="text-white font-bold text-xl">284</span>
                                                                </div>
                                                                <div className="flex justify-between items-center">
                                                                        <span className="text-white/70">Live Viewers</span>
                                                                        <span className="text-green-400 font-bold text-xl">2.1M</span>
                                                                </div>
                                                        </div>
                                                </div>

                                                {/* Action Buttons */}
                                                <div className="space-y-4">
                                                        <Link
                                                                href="/live"
                                                                className="block w-full bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800 text-white font-bold py-4 px-6 rounded-xl transition-all duration-300 transform hover:scale-105 text-center"
                                                        >
                                                                🔴 Watch Live
                                                        </Link>
                                                        <Link
                                                                href="/fixtures"
                                                                className="block w-full bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white font-bold py-4 px-6 rounded-xl transition-all duration-300 transform hover:scale-105 text-center"
                                                        >
                                                                📅 All Fixtures
                                                        </Link>
                                                        <Link
                                                                href="/highlights"
                                                                className="block w-full bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800 text-white font-bold py-4 px-6 rounded-xl transition-all duration-300 transform hover:scale-105 text-center"
                                                        >
                                                                ⭐ Highlights
                                                        </Link>
                                                </div>
                                        </div>
                                </div>

                                {/* Featured Match Card */}
                                {featuredMatch && (
                                        <div className="bg-gradient-to-r from-indigo-600/20 via-purple-600/20 to-pink-600/20 backdrop-blur-lg rounded-3xl p-8 border border-white/20">
                                                <div className="text-center mb-6">
                                                        <h2 className="text-3xl font-bold text-white mb-2">🏆 Match of the Day</h2>                                                                <p className="text-white/70">Don&apos;t miss this epic showdown</p>
                                                </div>

                                                <div className="grid grid-cols-1 md:grid-cols-3 gap-8 items-center">
                                                        {/* Home Team */}
                                                        <div className="text-center">
                                                                {featuredMatch.homeTeam.logo && (
                                                                        <img
                                                                                src={featuredMatch.homeTeam.logo}
                                                                                alt={featuredMatch.homeTeam.name}
                                                                                className="w-24 h-24 object-contain mx-auto mb-4"
                                                                        />
                                                                )}
                                                                <h3 className="text-2xl font-bold text-white">{featuredMatch.homeTeam.name}</h3>
                                                                <p className="text-white/60">{featuredMatch.homeTeam.shortName}</p>
                                                        </div>

                                                        {/* VS Section */}
                                                        <div className="text-center">
                                                                <div className="text-6xl font-bold text-white mb-4">VS</div>
                                                                <div className="bg-white/20 backdrop-blur-sm rounded-xl p-4">
                                                                        <div className="text-white font-semibold">
                                                                                {new Date(featuredMatch.scheduledTime).toLocaleDateString()}
                                                                        </div>
                                                                        <div className="text-2xl font-bold text-yellow-400">
                                                                                {new Date(featuredMatch.scheduledTime).toLocaleTimeString([], {
                                                                                        hour: '2-digit',
                                                                                        minute: '2-digit'
                                                                                })}
                                                                        </div>
                                                                        <div className="text-white/70 text-sm mt-2">
                                                                                📍 {featuredMatch.venue}
                                                                        </div>
                                                                </div>
                                                        </div>

                                                        {/* Away Team */}
                                                        <div className="text-center">
                                                                {featuredMatch.awayTeam.logo && (
                                                                        <img
                                                                                src={featuredMatch.awayTeam.logo}
                                                                                alt={featuredMatch.awayTeam.name}
                                                                                className="w-24 h-24 object-contain mx-auto mb-4"
                                                                        />
                                                                )}
                                                                <h3 className="text-2xl font-bold text-white">{featuredMatch.awayTeam.name}</h3>
                                                                <p className="text-white/60">{featuredMatch.awayTeam.shortName}</p>
                                                        </div>
                                                </div>

                                                {featuredMatch.isHot && (
                                                        <div className="text-center mt-6">
                                                                <div className="inline-flex items-center space-x-2 bg-red-500 text-white px-6 py-3 rounded-full font-bold animate-pulse">
                                                                        <span>🔥</span>
                                                                        <span>HOT MATCH</span>
                                                                        <span>🔥</span>
                                                                </div>
                                                        </div>
                                                )}
                                        </div>
                                )}
                        </div>

                        {/* Floating Action Button */}
                        <div className="fixed bottom-6 right-6 z-50">
                                <button className="bg-gradient-to-r from-red-500 to-pink-500 hover:from-red-600 hover:to-pink-600 text-white rounded-full p-4 shadow-lg transform hover:scale-110 transition-all duration-300">
                                        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h.01M20 4v5h-5M4 20v-5h5" />
                                        </svg>
                                </button>
                        </div>

                        {/* Custom CSS for animations */}
                        <style jsx>{`
                                @keyframes scroll {
                                        0% { transform: translateX(100%); }
                                        100% { transform: translateX(-100%); }
                                }
                                
                                .animate-scroll {
                                        animation: scroll 20s linear infinite;
                                }
                        `}</style>
                </section>
        );
};

export default HeroSectionV2;