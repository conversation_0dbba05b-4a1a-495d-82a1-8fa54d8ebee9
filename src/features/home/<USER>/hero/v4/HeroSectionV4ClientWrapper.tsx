'use client';

import dynamic from 'next/dynamic';
import { HeroSectionProps } from '../types';

// Dynamic import to ensure this component only loads on client side
const HeroSectionV4Enhanced = dynamic(
  () => import('./HeroSectionV4Enhanced'),
  {
    ssr: false,
    loading: () => (
      <div className="min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 flex items-center justify-center">
        <div className="text-white text-xl">Loading...</div>
      </div>
    )
  }
);

const HeroSectionV4ClientWrapper: React.FC<HeroSectionProps> = (props) => {
  return <HeroSectionV4Enhanced {...props} />;
};

export default HeroSectionV4ClientWrapper;
