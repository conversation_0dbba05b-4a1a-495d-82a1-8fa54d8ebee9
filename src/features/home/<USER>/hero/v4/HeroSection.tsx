'use client';

import React, { useState, useEffect } from 'react';
import { LiveScore, FeaturedMatch, HeroCTAButton } from '../../../types';

interface HeroSectionProps {
        liveScores?: LiveScore[];
        featuredMatch?: FeaturedMatch;
        ctaButtons?: HeroCTAButton[];
        className?: string;
}

interface MatchData {
        id: number;
        homeTeam: string;
        awayTeam: string;
        homeScore?: number;
        awayScore?: number;
        status: 'LIVE' | 'UPCOMING';
        isHot: boolean;
        minute?: string;
        kickoff?: string;
        competition: string;
        homeFlag: string;
        awayFlag: string;
}

const mockMatches: MatchData[] = [
        {
                id: 1,
                homeTeam: 'Arsenal',
                awayTeam: 'Chelsea',
                homeScore: 2,
                awayScore: 1,
                status: 'LIVE',
                isHot: true,
                minute: '67\'',
                competition: 'Premier League',
                homeFlag: '🏴󠁧󠁢󠁥󠁮󠁧󠁿',
                awayFlag: '🏴󠁧󠁢󠁥󠁮󠁧󠁿'
        },
        {
                id: 2,
                homeTeam: 'Liverpool',
                awayTeam: 'Man City',
                status: 'UPCOMING',
                isHot: true,
                kickoff: '20:00',
                competition: 'Premier League',
                homeFlag: '🏴󠁧󠁢󠁥󠁮󠁧󠁿',
                awayFlag: '🏴󠁧󠁢󠁥󠁮󠁧󠁿'
        },
        {
                id: 3,
                homeTeam: 'Barcelona',
                awayTeam: 'Real Madrid',
                status: 'UPCOMING',
                isHot: true,
                kickoff: '22:30',
                competition: 'El Clasico',
                homeFlag: '🇪🇸',
                awayFlag: '🇪🇸'
        }
];

const HeroSection: React.FC<HeroSectionProps> = ({
        liveScores = [],
        featuredMatch,
        ctaButtons = [],
        className = ''
}) => {
        // Note: Currently using mock data, will integrate real data in future iterations
        console.log('Received props:', { liveScores, featuredMatch, ctaButtons, className });
        const [currentTime, setCurrentTime] = useState(new Date());
        const [activeTab, setActiveTab] = useState<'LIVE' | 'UPCOMING'>('LIVE');

        useEffect(() => {
                const timer = setInterval(() => {
                        setCurrentTime(new Date());
                }, 1000);
                return () => clearInterval(timer);
        }, []);

        const liveMatches = [...mockMatches.filter(match => match.status === 'LIVE')];
        const upcomingMatches = [...mockMatches.filter(match => match.status === 'UPCOMING')];

        const renderMatchCard = (match: MatchData) => (
                <div
                        key={match.id}
                        className={`bg-white/10 backdrop-blur-md rounded-xl p-4 border transition-all duration-300 hover:scale-105 ${match.isHot ? 'border-red-500 shadow-lg shadow-red-500/30' : 'border-white/20'
                                }`}
                >
                        {match.isHot && (
                                <div className="flex justify-end mb-2">
                                        <span className="bg-gradient-to-r from-red-500 to-orange-500 text-white text-xs font-bold px-2 py-1 rounded-full animate-pulse">
                                                🔥 HOT
                                        </span>
                                </div>
                        )}

                        <div className="text-xs text-gray-300 mb-2 font-medium">
                                {match.competition}
                        </div>

                        <div className="space-y-3">
                                <div className="flex items-center justify-between">
                                        <div className="flex items-center space-x-2">
                                                <span className="text-lg">{match.homeFlag}</span>
                                                <span className="text-white font-semibold">{match.homeTeam}</span>
                                        </div>
                                        {match.status === 'LIVE' && (
                                                <span className="text-2xl font-bold text-white">{match.homeScore}</span>
                                        )}
                                </div>

                                <div className="flex items-center justify-center py-1">
                                        {match.status === 'LIVE' ? (
                                                <div className="flex items-center space-x-2">
                                                        <div className="w-2 h-2 bg-red-500 rounded-full animate-pulse"></div>
                                                        <span className="text-xs text-red-400 font-bold">{match.minute}</span>
                                                        <div className="w-2 h-2 bg-red-500 rounded-full animate-pulse"></div>
                                                </div>
                                        ) : (
                                                <div className="flex items-center space-x-2">
                                                        <div className="w-8 h-0.5 bg-gray-500"></div>
                                                        <span className="text-xs text-gray-400">VS</span>
                                                        <div className="w-8 h-0.5 bg-gray-500"></div>
                                                </div>
                                        )}
                                </div>

                                <div className="flex items-center justify-between">
                                        <div className="flex items-center space-x-2">
                                                <span className="text-lg">{match.awayFlag}</span>
                                                <span className="text-white font-semibold">{match.awayTeam}</span>
                                        </div>
                                        {match.status === 'LIVE' ? (
                                                <span className="text-2xl font-bold text-white">{match.awayScore}</span>
                                        ) : (
                                                <span className="text-sm text-gray-400">{match.kickoff}</span>
                                        )}
                                </div>
                        </div>

                        <div className="mt-3 flex justify-center">
                                <span className={`text-xs font-bold px-3 py-1 rounded-full ${match.status === 'LIVE' ? 'bg-red-500 text-white animate-pulse' : 'bg-blue-500 text-white'
                                        }`}>
                                        {match.status}
                                </span>
                        </div>
                </div>
        );

        return (
                <section className="relative min-h-screen bg-gradient-to-br from-slate-900 via-blue-900 to-indigo-900 overflow-hidden">
                        <div className="absolute inset-0">
                                <div className="absolute inset-0 bg-gradient-to-r from-blue-600/20 to-purple-600/20 animate-pulse"></div>
                        </div>

                        <div className="absolute inset-0 overflow-hidden">
                                <div className="absolute top-20 left-10 w-32 h-32 bg-blue-500/10 rounded-full blur-xl animate-bounce"></div>
                                <div className="absolute top-40 right-20 w-24 h-24 bg-purple-500/10 rounded-full blur-xl animate-bounce delay-300"></div>
                                <div className="absolute bottom-32 left-1/4 w-40 h-40 bg-indigo-500/10 rounded-full blur-xl animate-bounce delay-700"></div>
                        </div>

                        <div className="relative z-10 container mx-auto px-4 py-12">
                                <div className="text-center mb-12">
                                        <h1 className="text-5xl md:text-7xl font-black text-white mb-4 bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">
                                                SPORTS ARENA
                                        </h1>
                                        <p className="text-xl text-gray-300 mb-8">
                                                Live Scores & Upcoming Matches
                                        </p>

                                        <div className="flex justify-center items-center space-x-4 mb-8">
                                                <div className="bg-black/30 backdrop-blur-md rounded-lg px-4 py-2 border border-white/20">
                                                        <span className="text-green-400 text-sm font-bold">● LIVE</span>
                                                </div>
                                                <div className="bg-black/30 backdrop-blur-md rounded-lg px-4 py-2 border border-white/20">
                                                        <span className="text-white font-mono text-lg">
                                                                {currentTime.toLocaleTimeString()}
                                                        </span>
                                                </div>
                                        </div>
                                </div>

                                <div className="flex justify-center mb-8">
                                        <div className="bg-black/30 backdrop-blur-md rounded-xl p-1 border border-white/20">
                                                <button
                                                        onClick={() => setActiveTab('LIVE')}
                                                        className={`px-6 py-3 rounded-lg font-bold transition-all duration-300 ${activeTab === 'LIVE'
                                                                ? 'bg-red-500 text-white shadow-lg'
                                                                : 'text-gray-300 hover:text-white'
                                                                }`}
                                                >
                                                        🔴 LIVE ({liveMatches.length})
                                                </button>
                                                <button
                                                        onClick={() => setActiveTab('UPCOMING')}
                                                        className={`px-6 py-3 rounded-lg font-bold transition-all duration-300 ${activeTab === 'UPCOMING'
                                                                ? 'bg-blue-500 text-white shadow-lg'
                                                                : 'text-gray-300 hover:text-white'
                                                                }`}
                                                >
                                                        ⏰ UPCOMING ({upcomingMatches.length})
                                                </button>
                                        </div>
                                </div>

                                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12">
                                        {activeTab === 'LIVE'
                                                ? liveMatches.map(renderMatchCard)
                                                : upcomingMatches.map(renderMatchCard)
                                        }
                                </div>

                                <div className="bg-gradient-to-r from-red-500/20 to-orange-500/20 backdrop-blur-md rounded-2xl p-6 border border-red-500/30 mb-8">
                                        <div className="flex items-center justify-between">
                                                <div>
                                                        <h3 className="text-2xl font-bold text-white mb-2">🔥 Trận Hot Nhất</h3>
                                                        <p className="text-gray-300">Đừng bỏ lỡ những trận đấu hấp dẫn nhất!</p>
                                                </div>
                                                <div className="text-right">
                                                        <div className="text-3xl mb-2">🏆</div>
                                                        <div className="text-sm text-gray-400">
                                                                {mockMatches.filter(m => m.isHot).length} trận HOT
                                                        </div>
                                                </div>
                                        </div>
                                </div>

                                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                                        <button className="bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white font-bold py-4 px-8 rounded-xl transition-all duration-300 transform hover:scale-105 shadow-lg">
                                                📺 Xem Trực Tiếp
                                        </button>
                                        <button className="bg-gradient-to-r from-green-500 to-teal-600 hover:from-green-600 hover:to-teal-700 text-white font-bold py-4 px-8 rounded-xl transition-all duration-300 transform hover:scale-105 shadow-lg">
                                                📊 Thống Kê Chi Tiết
                                        </button>
                                        <button className="bg-gradient-to-r from-yellow-500 to-orange-600 hover:from-yellow-600 hover:to-orange-700 text-white font-bold py-4 px-8 rounded-xl transition-all duration-300 transform hover:scale-105 shadow-lg">
                                                🎯 Dự Đoán Kết Quả
                                        </button>
                                </div>
                        </div>
                </section>
        );
};

export default HeroSection;
