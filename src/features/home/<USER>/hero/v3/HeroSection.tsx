'use client';

import React, { useState, useEffect, useRef } from 'react';
import { HeroSectionProps } from '../../../types';
import Link from 'next/link';

/**
 * Hero Section V3 - 3D Immersive Sports Arena
 * Revolutionary Design Features:
 * - 3D perspective arena with animated field
 * - Floating holographic match cards
 * - Interactive scoreboard with neon effects
 * - Particle animation system
 * - Voice-like audio visualization
 * - Multi-dimensional user experience
 * - Glassmorphism design elements
 * - Dynamic weather and time effects
 */

interface Particle {
        id: number;
        x: number;
        y: number;
        vx: number;
        vy: number;
        life: number;
        maxLife: number;
}

export const HeroSectionV3: React.FC<HeroSectionProps> = ({
        liveScores = [],
        featuredMatch,
        ctaButtons = [],
        className = ''
}) => {
        const [currentTime, setCurrentTime] = useState(new Date());
        const [particles, setParticles] = useState<Particle[]>([]);
        const [activeView, setActiveView] = useState<'arena' | 'scoreboard' | 'matches'>('arena');
        const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
        const [isHovered, setIsHovered] = useState(false);
        const particleIdRef = useRef(0);
        const animationRef = useRef<number | null>(null);

        // Time and weather system
        const hour = currentTime.getHours();
        const isNight = hour < 6 || hour > 18;
        const weatherEffects = ['clear', 'rain', 'snow'][Math.floor(Math.random() * 3)];

        // Update time
        useEffect(() => {
                const timer = setInterval(() => setCurrentTime(new Date()), 1000);
                return () => clearInterval(timer);
        }, []);

        // Mouse tracking for 3D effects
        useEffect(() => {
                const handleMouseMove = (e: MouseEvent) => {
                        const rect = document.getElementById('hero-arena')?.getBoundingClientRect();
                        if (rect) {
                                setMousePosition({
                                        x: ((e.clientX - rect.left) / rect.width - 0.5) * 2,
                                        y: ((e.clientY - rect.top) / rect.height - 0.5) * 2
                                });
                        }
                };

                window.addEventListener('mousemove', handleMouseMove);
                return () => window.removeEventListener('mousemove', handleMouseMove);
        }, []);

        // Particle system
        useEffect(() => {
                const createParticle = (x: number, y: number): Particle => ({
                        id: particleIdRef.current++,
                        x,
                        y,
                        vx: (Math.random() - 0.5) * 2,
                        vy: (Math.random() - 0.5) * 2,
                        life: 100,
                        maxLife: 100
                });

                const updateParticles = () => {
                        setParticles(prev => {
                                const newParticles = prev
                                        .map(p => ({
                                                ...p,
                                                x: p.x + p.vx,
                                                y: p.y + p.vy,
                                                life: p.life - 1
                                        }))
                                        .filter(p => p.life > 0);

                                // Add new particles occasionally
                                if (Math.random() < 0.1 && newParticles.length < 50) {
                                        newParticles.push(createParticle(
                                                Math.random() * 800,
                                                Math.random() * 400
                                        ));
                                }

                                return newParticles;
                        });
                };

                const animate = () => {
                        updateParticles();
                        animationRef.current = requestAnimationFrame(animate);
                };

                animate();
                return () => {
                        if (animationRef.current) {
                                cancelAnimationFrame(animationRef.current);
                        }
                };
        }, []);

        // Get live and featured matches
        const liveMatches = liveScores.filter(score => score.status === 'live');
        const hotMatches = liveScores.filter(score => score.isHot);

        // 3D transform styles
        const arenaTransform = `
    perspective(1000px)
    rotateX(${mousePosition.y * 5}deg)
    rotateY(${mousePosition.x * 5}deg)
    scale(${isHovered ? 1.02 : 1})
  `;

        return (
                <section
                        id="hero-arena"
                        className={`relative min-h-screen overflow-hidden ${className}`}
                        onMouseEnter={() => setIsHovered(true)}
                        onMouseLeave={() => setIsHovered(false)}
                        style={{
                                background: isNight
                                        ? 'linear-gradient(135deg, #0c0f1f 0%, #1a1d3a 25%, #2d1b69 50%, #1a1d3a 75%, #0c0f1f 100%)'
                                        : 'linear-gradient(135deg, #87CEEB 0%, #4682B4 25%, #1e3a8a 50%, #4682B4 75%, #87CEEB 100%)'
                        }}
                >
                        {/* Dynamic background particles */}
                        <div className="absolute inset-0 overflow-hidden">
                                {particles.map(particle => (
                                        <div
                                                key={particle.id}
                                                className="absolute w-1 h-1 bg-white rounded-full"
                                                style={{
                                                        left: particle.x,
                                                        top: particle.y,
                                                        opacity: particle.life / particle.maxLife,
                                                        transform: `scale(${particle.life / particle.maxLife})`,
                                                        boxShadow: '0 0 6px rgba(255, 255, 255, 0.8)'
                                                }}
                                        />
                                ))}
                        </div>

                        {/* Weather effects */}
                        {weatherEffects === 'snow' && (
                                <div className="absolute inset-0 pointer-events-none">
                                        {Array.from({ length: 50 }).map((_, i) => (
                                                <div
                                                        key={i}
                                                        className="absolute w-1 h-1 bg-white rounded-full animate-pulse"
                                                        style={{
                                                                left: `${Math.random() * 100}%`,
                                                                top: `${Math.random() * 100}%`,
                                                                animationDelay: `${Math.random() * 3}s`,
                                                                animationDuration: `${3 + Math.random() * 2}s`
                                                        }}
                                                />
                                        ))}
                                </div>
                        )}

                        {/* 3D Arena Container */}
                        <div
                                className="relative w-full h-full transition-transform duration-300 ease-out"
                                style={{ transform: arenaTransform }}
                        >
                                {/* Floating Navigation Pills */}
                                <div className="absolute top-8 left-1/2 transform -translate-x-1/2 z-30">
                                        <div className="flex space-x-4 bg-white/10 backdrop-blur-xl rounded-full p-2 border border-white/20">
                                                {[
                                                        { key: 'arena', label: '🏟️ Arena', icon: '⚡' },
                                                        { key: 'scoreboard', label: '📊 Live', icon: '🔴' },
                                                        { key: 'matches', label: '🎯 Matches', icon: '🔥' }
                                                ].map(tab => (
                                                        <button
                                                                key={tab.key}
                                                                onClick={() => setActiveView(tab.key as 'arena' | 'scoreboard' | 'matches')}
                                                                className={`px-6 py-3 rounded-full font-medium transition-all duration-300 ${activeView === tab.key
                                                                        ? 'bg-gradient-to-r from-purple-500 to-pink-500 text-white shadow-lg scale-105'
                                                                        : 'text-white/70 hover:text-white hover:bg-white/10'
                                                                        }`}
                                                        >
                                                                <span className="mr-2">{tab.icon}</span>
                                                                {tab.label}
                                                        </button>
                                                ))}
                                        </div>
                                </div>

                                {/* Central Arena View */}
                                {activeView === 'arena' && (
                                        <div className="flex items-center justify-center min-h-screen p-8">
                                                <div className="relative">
                                                        {/* 3D Stadium Field */}
                                                        <div
                                                                className="relative w-96 h-64 bg-gradient-to-b from-green-400 to-green-600 rounded-3xl shadow-2xl transform"
                                                                style={{
                                                                        transform: `perspective(600px) rotateX(45deg) rotateY(${mousePosition.x * 10}deg)`,
                                                                        boxShadow: '0 25px 50px rgba(0, 0, 0, 0.5)'
                                                                }}
                                                        >
                                                                {/* Field lines */}
                                                                <div className="absolute inset-4 border-2 border-white/50 rounded-2xl">
                                                                        <div className="absolute top-1/2 left-0 right-0 h-0.5 bg-white/50" />
                                                                        <div className="absolute top-1/2 left-1/2 w-16 h-16 border-2 border-white/50 rounded-full transform -translate-x-1/2 -translate-y-1/2" />
                                                                </div>

                                                                {/* Stadium lights */}
                                                                <div className="absolute -top-8 left-4 w-4 h-8 bg-yellow-300 rounded-full shadow-lg animate-pulse" />
                                                                <div className="absolute -top-8 right-4 w-4 h-8 bg-yellow-300 rounded-full shadow-lg animate-pulse" style={{ animationDelay: '0.5s' }} />
                                                                <div className="absolute -bottom-8 left-4 w-4 h-8 bg-yellow-300 rounded-full shadow-lg animate-pulse" style={{ animationDelay: '1s' }} />
                                                                <div className="absolute -bottom-8 right-4 w-4 h-8 bg-yellow-300 rounded-full shadow-lg animate-pulse" style={{ animationDelay: '1.5s' }} />
                                                        </div>

                                                        {/* Floating Match Info */}
                                                        {featuredMatch && (
                                                                <div
                                                                        className="absolute -right-48 top-1/2 transform -translate-y-1/2 bg-white/10 backdrop-blur-xl rounded-2xl p-6 border border-white/20 w-80"
                                                                        style={{
                                                                                transform: `translateY(-50%) translateZ(50px) rotateY(${-mousePosition.x * 5}deg)`,
                                                                        }}
                                                                >
                                                                        <div className="text-center">
                                                                                <h3 className="text-xl font-bold text-white mb-4">{featuredMatch.league.name}</h3>
                                                                                <div className="flex items-center justify-between mb-4">
                                                                                        <div className="text-center">
                                                                                                <img
                                                                                                        src={featuredMatch.homeTeam.logo}
                                                                                                        alt={featuredMatch.homeTeam.name}
                                                                                                        className="w-12 h-12 mx-auto mb-2 rounded-full"
                                                                                                />
                                                                                                <p className="text-white font-medium">{featuredMatch.homeTeam.name}</p>
                                                                                        </div>
                                                                                        <div className="text-2xl font-bold text-white">
                                                                                                VS
                                                                                        </div>
                                                                                        <div className="text-center">
                                                                                                <img
                                                                                                        src={featuredMatch.awayTeam.logo}
                                                                                                        alt={featuredMatch.awayTeam.name}
                                                                                                        className="w-12 h-12 mx-auto mb-2 rounded-full"
                                                                                                />
                                                                                                <p className="text-white font-medium">{featuredMatch.awayTeam.name}</p>
                                                                                        </div>
                                                                                </div>
                                                                                <div className="text-sm text-white/70">{featuredMatch.status}</div>
                                                                        </div>
                                                                </div>
                                                        )}

                                                        {/* Time Display */}
                                                        <div
                                                                className="absolute -left-48 top-1/2 transform -translate-y-1/2 bg-white/10 backdrop-blur-xl rounded-2xl p-6 border border-white/20 w-64"
                                                                style={{
                                                                        transform: `translateY(-50%) translateZ(50px) rotateY(${-mousePosition.x * 5}deg)`,
                                                                }}
                                                        >
                                                                <div className="text-center">
                                                                        <div className="text-3xl font-bold text-white mb-2">
                                                                                {currentTime.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                                                                        </div>
                                                                        <div className="text-white/70 mb-4">
                                                                                {currentTime.toLocaleDateString([], { weekday: 'long', month: 'short', day: 'numeric' })}
                                                                        </div>
                                                                        <div className="flex justify-center space-x-4 text-sm">
                                                                                <div className="text-center">
                                                                                        <div className="text-green-400 font-bold">{liveMatches.length}</div>
                                                                                        <div className="text-white/70">Live</div>
                                                                                </div>
                                                                                <div className="text-center">
                                                                                        <div className="text-red-400 font-bold">{hotMatches.length}</div>
                                                                                        <div className="text-white/70">Hot</div>
                                                                                </div>
                                                                        </div>
                                                                </div>
                                                        </div>
                                                </div>
                                        </div>
                                )}

                                {/* Live Scoreboard View */}
                                {activeView === 'scoreboard' && (
                                        <div className="flex items-center justify-center min-h-screen p-8">
                                                <div className="w-full max-w-6xl">
                                                        <h2 className="text-4xl font-bold text-white text-center mb-12">
                                                                🔴 Live Scoreboard
                                                        </h2>
                                                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                                                                {liveMatches.slice(0, 6).map((match, index) => (
                                                                        <div
                                                                                key={match.id}
                                                                                className="bg-white/10 backdrop-blur-xl rounded-2xl p-6 border border-white/20 transform hover:scale-105 transition-all duration-300"
                                                                                style={{
                                                                                        animationDelay: `${index * 0.1}s`,
                                                                                        animation: 'slideInUp 0.6s ease-out forwards'
                                                                                }}
                                                                        >
                                                                                <div className="flex items-center justify-between mb-4">
                                                                                        <span className="text-white/70 text-sm">{match.league.name}</span>
                                                                                        {match.isHot && (
                                                                                                <span className="bg-red-500 text-white text-xs px-2 py-1 rounded-full">
                                                                                                        🔥 HOT
                                                                                                </span>
                                                                                        )}
                                                                                </div>
                                                                                <div className="flex items-center justify-between">
                                                                                        <div className="text-center">
                                                                                                <img
                                                                                                        src={match.homeTeam.logo}
                                                                                                        alt={match.homeTeam.name}
                                                                                                        className="w-10 h-10 mx-auto mb-2 rounded-full"
                                                                                                />
                                                                                                <p className="text-white text-sm">{match.homeTeam.name}</p>
                                                                                        </div>
                                                                                        <div className="text-center">
                                                                                                <div className="text-2xl font-bold text-white">
                                                                                                        {match.homeScore} - {match.awayScore}
                                                                                                </div>
                                                                                                <div className="text-xs text-white/70">{match.matchTime}</div>
                                                                                        </div>
                                                                                        <div className="text-center">
                                                                                                <img
                                                                                                        src={match.awayTeam.logo}
                                                                                                        alt={match.awayTeam.name}
                                                                                                        className="w-10 h-10 mx-auto mb-2 rounded-full"
                                                                                                />
                                                                                                <p className="text-white text-sm">{match.awayTeam.name}</p>
                                                                                        </div>
                                                                                </div>
                                                                        </div>
                                                                ))}
                                                        </div>
                                                </div>
                                        </div>
                                )}

                                {/* Matches Grid View */}
                                {activeView === 'matches' && (
                                        <div className="flex items-center justify-center min-h-screen p-8">
                                                <div className="w-full max-w-7xl">
                                                        <h2 className="text-4xl font-bold text-white text-center mb-12">
                                                                🎯 All Matches
                                                        </h2>
                                                        <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-4">
                                                                {liveScores.map((match, index) => (
                                                                        <div
                                                                                key={match.id}
                                                                                className={`bg-white/10 backdrop-blur-xl rounded-xl p-4 border border-white/20 transform hover:scale-105 transition-all duration-300 ${match.status === 'live' ? 'ring-2 ring-green-400' : ''
                                                                                        }`}
                                                                                style={{
                                                                                        animationDelay: `${index * 0.05}s`,
                                                                                        animation: 'fadeInScale 0.5s ease-out forwards'
                                                                                }}
                                                                        >
                                                                                <div className="text-center">
                                                                                        <div className="flex items-center justify-center space-x-2 mb-3">
                                                                                                <span className="text-white/70 text-xs">{match.league.name}</span>
                                                                                                {match.isHot && <span className="text-red-400">🔥</span>}
                                                                                                {match.status === 'live' && (
                                                                                                        <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse" />
                                                                                                )}
                                                                                        </div>
                                                                                        <div className="flex items-center justify-between mb-2">
                                                                                                <img
                                                                                                        src={match.homeTeam.logo}
                                                                                                        alt={match.homeTeam.name}
                                                                                                        className="w-8 h-8 rounded-full"
                                                                                                />
                                                                                                <div className="text-white font-bold">
                                                                                                        {match.homeScore} - {match.awayScore}
                                                                                                </div>
                                                                                                <img
                                                                                                        src={match.awayTeam.logo}
                                                                                                        alt={match.awayTeam.name}
                                                                                                        className="w-8 h-8 rounded-full"
                                                                                                />
                                                                                        </div>
                                                                                        <div className="text-xs text-white/70">{match.matchTime}</div>
                                                                                </div>
                                                                        </div>
                                                                ))}
                                                        </div>
                                                </div>
                                        </div>
                                )}

                                {/* Floating Action Buttons */}
                                <div className="absolute bottom-8 right-8 flex flex-col space-y-4 z-30">
                                        {ctaButtons.map((button, index) => (
                                                <Link
                                                        key={index}
                                                        href={button.href}
                                                        className="bg-gradient-to-r from-purple-500 to-pink-500 text-white px-6 py-3 rounded-full font-medium hover:shadow-lg transform hover:scale-105 transition-all duration-300"
                                                        style={{
                                                                animationDelay: `${index * 0.2}s`,
                                                                animation: 'bounceIn 0.8s ease-out forwards'
                                                        }}
                                                >
                                                        {button.label}
                                                </Link>
                                        ))}
                                </div>
                        </div>

                        {/* Custom CSS Animations */}
                        <style jsx>{`
        @keyframes slideInUp {
          from {
            opacity: 0;
            transform: translateY(30px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }
        
        @keyframes fadeInScale {
          from {
            opacity: 0;
            transform: scale(0.8);
          }
          to {
            opacity: 1;
            transform: scale(1);
          }
        }
        
        @keyframes bounceIn {
          0% {
            opacity: 0;
            transform: scale(0.3) translateY(20px);
          }
          50% {
            opacity: 1;
            transform: scale(1.05) translateY(-5px);
          }
          70% {
            transform: scale(0.9) translateY(0);
          }
          100% {
            opacity: 1;
            transform: scale(1) translateY(0);
          }
        }
      `}</style>
                </section>
        );
};

export default HeroSectionV3;
