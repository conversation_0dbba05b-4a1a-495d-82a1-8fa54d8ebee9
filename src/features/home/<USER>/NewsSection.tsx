import * as React from 'react';
import Link from 'next/link';
import { formatDistanceToNow } from '@/shared/utils/dateUtils';
import type { NewsSectionProps, NewsItem } from '../types';

const NewsCard: React.FC<{ newsItem: NewsItem }> = ({ newsItem }: { newsItem: NewsItem }) => {
        const categoryColors = {
                match: 'bg-blue-100 text-blue-800',
                transfer: 'bg-green-100 text-green-800',
                league: 'bg-purple-100 text-purple-800',
                general: 'bg-gray-100 text-gray-800'
        };

        const priorityIndicator = {
                high: 'border-l-4 border-red-500',
                medium: 'border-l-4 border-yellow-500',
                low: 'border-l-4 border-gray-300'
        };

        return (
                <article className={`bg-white rounded-xl shadow-sm hover:shadow-lg transition-all duration-300 overflow-hidden group ${priorityIndicator[newsItem.priority]}`}>
                        {newsItem.imageUrl && (
                                <div className="relative h-48 overflow-hidden">
                                        <img
                                                src={newsItem.imageUrl}
                                                alt={newsItem.title}
                                                className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                                        />
                                        {newsItem.isFeatured && (
                                                <span className="absolute top-3 left-3 bg-red-600 text-white px-2 py-1 rounded-full text-xs font-medium">
                                                        Featured
                                                </span>
                                        )}
                                        <span className={`absolute top-3 right-3 px-2 py-1 rounded-full text-xs font-medium ${categoryColors[newsItem.category]}`}>
                                                {newsItem.category.charAt(0).toUpperCase() + newsItem.category.slice(1)}
                                        </span>
                                </div>
                        )}

                        <div className="p-6">
                                <div className="flex items-center justify-between mb-3">
                                        <div className="flex items-center text-sm text-gray-500 space-x-4">
                                                {newsItem.author && (
                                                        <span className="flex items-center">
                                                                <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                                                        <path fillRule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clipRule="evenodd" />
                                                                </svg>
                                                                {newsItem.author}
                                                        </span>
                                                )}
                                                <span className="flex items-center">
                                                        <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                                                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
                                                        </svg>
                                                        {formatDistanceToNow(newsItem.publishedAt)}
                                                </span>
                                                {newsItem.readTime && (
                                                        <span className="flex items-center">
                                                                <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                                                        <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                                                </svg>
                                                                {newsItem.readTime} min read
                                                        </span>
                                                )}
                                        </div>
                                </div>

                                <Link href={`/news/${newsItem.slug}`} className="group">
                                        <h3 className="text-lg font-bold text-gray-900 mb-2 group-hover:text-blue-600 transition-colors duration-200 line-clamp-2">
                                                {newsItem.title}
                                        </h3>
                                </Link>

                                {newsItem.summary && (
                                        <p className="text-gray-600 mb-4 line-clamp-3">
                                                {newsItem.summary}
                                        </p>
                                )}

                                {newsItem.tags && newsItem.tags.length > 0 && (
                                        <div className="flex flex-wrap gap-2 mb-4">
                                                {newsItem.tags.slice(0, 3).map((tag: string) => (
                                                        <span
                                                                key={tag}
                                                                className="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded-full hover:bg-gray-200 transition-colors cursor-pointer"
                                                        >
                                                                #{tag}
                                                        </span>
                                                ))}
                                        </div>
                                )}

                                <Link
                                        href={`/news/${newsItem.slug}`}
                                        className="inline-flex items-center text-blue-600 hover:text-blue-700 font-medium text-sm transition-colors duration-200"
                                >
                                        Read more
                                        <svg className="w-4 h-4 ml-1 transform group-hover:translate-x-1 transition-transform duration-200" fill="currentColor" viewBox="0 0 20 20">
                                                <path fillRule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clipRule="evenodd" />
                                        </svg>
                                </Link>
                        </div>
                </article>
        );
};

export const NewsSection: React.FC<NewsSectionProps> = ({
        news,
        title = "Latest News",
        showViewAll = true,
        maxItems = 6,
        className = ""
}: NewsSectionProps) => {
        const displayNews: NewsItem[] = news.slice(0, maxItems);
        const featuredNews: NewsItem[] = displayNews.filter((item: NewsItem) => item.isFeatured);
        const regularNews: NewsItem[] = displayNews.filter((item: NewsItem) => !item.isFeatured);

        return (
                <section className={`space-y-8 ${className}`}>
                        {/* Section Header */}
                        <div className="flex items-center justify-between">
                                <div>
                                        <h2 className="text-3xl font-bold text-gray-900 mb-2">{title}</h2>
                                        <p className="text-gray-600">Stay updated with the latest football news and developments</p>
                                </div>
                                {showViewAll && news.length > maxItems && (
                                        <Link
                                                href="/news"
                                                className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200 font-medium"
                                        >
                                                View All News
                                                <svg className="w-4 h-4 ml-2" fill="currentColor" viewBox="0 0 20 20">
                                                        <path fillRule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clipRule="evenodd" />
                                                </svg>
                                        </Link>
                                )}
                        </div>

                        {/* Empty State */}
                        {news.length === 0 && (
                                <div className="text-center py-12">
                                        <div className="inline-flex items-center justify-center w-16 h-16 bg-gray-100 rounded-full mb-4">
                                                <svg className="w-8 h-8 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                                        <path fillRule="evenodd" d="M2 5a2 2 0 012-2h8a2 2 0 012 2v10a2 2 0 002 2H4a2 2 0 01-2-2V5zm3 1h6v4H5V6zm6 6H5v2h6v-2z" clipRule="evenodd" />
                                                        <path d="M15 7h1a2 2 0 012 2v5.5a1.5 1.5 0 01-3 0V9a1 1 0 00-1-1h-1v-1z" />
                                                </svg>
                                        </div>
                                        <h3 className="text-lg font-medium text-gray-900 mb-2">No news available</h3>
                                        <p className="text-gray-500">Check back later for the latest football updates.</p>
                                </div>
                        )}

                        {/* Featured News - Large cards */}
                        {featuredNews.length > 0 && (
                                <div className="space-y-6">
                                        <h3 className="text-xl font-semibold text-gray-900 flex items-center">
                                                <span className="w-2 h-2 bg-red-600 rounded-full mr-3"></span>
                                                Featured Stories
                                        </h3>
                                        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                                                {featuredNews.map((newsItem: NewsItem) => (
                                                        <NewsCard key={newsItem.id} newsItem={newsItem} />
                                                ))}
                                        </div>
                                </div>
                        )}

                        {/* Regular News Grid */}
                        {regularNews.length > 0 && (
                                <div className="space-y-6">
                                        {featuredNews.length > 0 && (
                                                <h3 className="text-xl font-semibold text-gray-900 flex items-center">
                                                        <span className="w-2 h-2 bg-blue-600 rounded-full mr-3"></span>
                                                        More Stories
                                                </h3>
                                        )}
                                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                                                {regularNews.map((newsItem: NewsItem) => (
                                                        <NewsCard key={newsItem.id} newsItem={newsItem} />
                                                ))}
                                        </div>
                                </div>
                        )}
                </section>
        );
};
