'use client';

import React, { useState, useEffect } from 'react';
import type { DebugMetrics } from '../hooks/useMagneticFieldSystem';

interface MagneticFieldDebugPanelProps {
  metrics: DebugMetrics;
  isVisible: boolean;
  onToggleVisibility: () => void;
  onConfigChange?: (config: Partial<Record<string, unknown>>) => void;
  className?: string;
}

interface PerformanceHistory {
  timestamp: number;
  fps: number;
  fieldCount: number;
  memoryUsage: number;
}

export const MagneticFieldDebugPanel: React.FC<MagneticFieldDebugPanelProps> = ({
  metrics,
  isVisible,
  onToggleVisibility,
  onConfigChange,
  className = ''
}) => {
  const [performanceHistory, setPerformanceHistory] = useState<PerformanceHistory[]>([]);
  const [expandedSections, setExpandedSections] = useState({
    performance: true,
    fields: false,
    config: false,
    battery: false
  });

  // Update performance history
  useEffect(() => {
    const newEntry: PerformanceHistory = {
      timestamp: Date.now(),
      fps: metrics.framesPerSecond,
      fieldCount: metrics.fieldCount,
      memoryUsage: metrics.memoryUsage
    };

    setPerformanceHistory(prev => {
      const updated = [...prev, newEntry];
      // Keep only last 60 entries (1 minute of data if updated every second)
      return updated.slice(-60);
    });
  }, [metrics.framesPerSecond, metrics.fieldCount, metrics.memoryUsage]);

  const getPerformanceColor = (value: number, thresholds: { good: number; warning: number }) => {
    if (value >= thresholds.good) return 'text-green-400';
    if (value >= thresholds.warning) return 'text-yellow-400';
    return 'text-red-400';
  };

  const formatMemoryUsage = (bytes: number) => {
    if (bytes < 1024) return `${bytes} B`;
    if (bytes < 1024 * 1024) return `${(bytes / 1024).toFixed(1)} KB`;
    return `${(bytes / (1024 * 1024)).toFixed(1)} MB`;
  };

  const toggleSection = (section: keyof typeof expandedSections) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  if (!isVisible) {
    return (
      <button
        onClick={onToggleVisibility}
        className={`fixed top-4 right-4 z-50 bg-gray-800 text-white px-3 py-2 rounded-lg shadow-lg hover:bg-gray-700 transition-colors ${className}`}
        aria-label="Show debug panel"
      >
        🔧 Debug
      </button>
    );
  }

  return (
    <div className={`fixed top-4 right-4 z-50 bg-gray-900 text-white p-4 rounded-lg shadow-xl max-w-sm w-full max-h-96 overflow-y-auto ${className}`}>
      {/* Header */}
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-lg font-semibold">Magnetic Field Debug</h3>
        <button
          onClick={onToggleVisibility}
          className="text-gray-400 hover:text-white transition-colors"
          aria-label="Hide debug panel"
        >
          ✕
        </button>
      </div>

      {/* Performance Section */}
      <div className="mb-4">
        <button
          onClick={() => toggleSection('performance')}
          className="flex items-center justify-between w-full text-left font-medium mb-2 hover:text-blue-400 transition-colors"
        >
          <span>Performance Metrics</span>
          <span>{expandedSections.performance ? '▼' : '▶'}</span>
        </button>

        {expandedSections.performance && (
          <div className="space-y-2 text-sm">
            <div className="flex justify-between">
              <span>FPS:</span>
              <span className={getPerformanceColor(metrics.framesPerSecond, { good: 50, warning: 30 })}>
                {metrics.framesPerSecond}
              </span>
            </div>
            <div className="flex justify-between">
              <span>Performance Score:</span>
              <span className={getPerformanceColor(metrics.performanceScore, { good: 80, warning: 60 })}>
                {metrics.performanceScore}%
              </span>
            </div>
            <div className="flex justify-between">
              <span>Memory Usage:</span>
              <span className="text-blue-400">{formatMemoryUsage(metrics.memoryUsage)}</span>
            </div>
            <div className="flex justify-between">
              <span>Device:</span>
              <span className={metrics.isMobile ? 'text-orange-400' : 'text-green-400'}>
                {metrics.isMobile ? 'Mobile' : 'Desktop'}
              </span>
            </div>
          </div>
        )}
      </div>

      {/* Fields Section */}
      <div className="mb-4">
        <button
          onClick={() => toggleSection('fields')}
          className="flex items-center justify-between w-full text-left font-medium mb-2 hover:text-blue-400 transition-colors"
        >
          <span>Field Statistics</span>
          <span>{expandedSections.fields ? '▼' : '▶'}</span>
        </button>

        {expandedSections.fields && (
          <div className="space-y-2 text-sm">
            <div className="flex justify-between">
              <span>Active Fields:</span>
              <span className="text-blue-400">{metrics.fieldCount}</span>
            </div>
            <div className="flex justify-between">
              <span>Interactions:</span>
              <span className="text-purple-400">{metrics.interactionCount}</span>
            </div>
            <div className="flex justify-between">
              <span>Last Update:</span>
              <span className="text-gray-400">
                {new Date(metrics.lastUpdateTime).toLocaleTimeString()}
              </span>
            </div>
          </div>
        )}
      </div>

      {/* Battery Section (if available) */}
      {metrics.batteryLevel !== undefined && (
        <div className="mb-4">
          <button
            onClick={() => toggleSection('battery')}
            className="flex items-center justify-between w-full text-left font-medium mb-2 hover:text-blue-400 transition-colors"
          >
            <span>Battery Status</span>
            <span>{expandedSections.battery ? '▼' : '▶'}</span>
          </button>

          {expandedSections.battery && (
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span>Battery Level:</span>
                <span className={getPerformanceColor(metrics.batteryLevel * 100, { good: 50, warning: 20 })}>
                  {Math.round(metrics.batteryLevel * 100)}%
                </span>
              </div>
              <div className="w-full bg-gray-700 rounded-full h-2">
                <div
                  className={`h-2 rounded-full transition-all duration-300 ${metrics.batteryLevel > 0.5 ? 'bg-green-400' :
                      metrics.batteryLevel > 0.2 ? 'bg-yellow-400' : 'bg-red-400'
                    }`}
                  style={{ width: `${metrics.batteryLevel * 100}%` }}
                />
              </div>
            </div>
          )}
        </div>
      )}

      {/* Performance History Chart */}
      {performanceHistory.length > 1 && (
        <div className="mb-4">
          <h4 className="font-medium mb-2">FPS History</h4>
          <div className="h-16 bg-gray-800 rounded relative overflow-hidden">
            <svg className="w-full h-full">
              <polyline
                fill="none"
                stroke="#60a5fa"
                strokeWidth="2"
                points={performanceHistory
                  .map((entry, index) => {
                    const x = (index / (performanceHistory.length - 1)) * 100;
                    const y = 100 - (entry.fps / 60) * 100;
                    return `${x},${y}`;
                  })
                  .join(' ')}
                vectorEffect="non-scaling-stroke"
                preserveAspectRatio="none"
              />
            </svg>
            <div className="absolute bottom-0 left-0 text-xs text-gray-400">0</div>
            <div className="absolute top-0 left-0 text-xs text-gray-400">60</div>
          </div>
        </div>
      )}

      {/* Quick Actions */}
      <div className="space-y-2">
        <h4 className="font-medium">Quick Actions</h4>
        <div className="grid grid-cols-2 gap-2">
          <button
            onClick={() => onConfigChange?.({ performanceMode: true })}
            className="px-2 py-1 bg-blue-600 hover:bg-blue-700 rounded text-xs transition-colors"
          >
            Performance Mode
          </button>
          <button
            onClick={() => onConfigChange?.({ accessibilityMode: true })}
            className="px-2 py-1 bg-green-600 hover:bg-green-700 rounded text-xs transition-colors"
          >
            Accessibility
          </button>
          <button
            onClick={() => onConfigChange?.({ mobileOptimization: true })}
            className="px-2 py-1 bg-orange-600 hover:bg-orange-700 rounded text-xs transition-colors"
          >
            Mobile Mode
          </button>
          <button
            onClick={() => onConfigChange?.({ enableVisualization: false })}
            className="px-2 py-1 bg-red-600 hover:bg-red-700 rounded text-xs transition-colors"
          >
            Disable Visual
          </button>
        </div>
      </div>
    </div>
  );
};
