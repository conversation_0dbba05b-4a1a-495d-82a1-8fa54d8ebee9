import { LiveScore, FeaturedMatch, HeroCTAButton } from '../types';

/**
 * Mock data for Hero Section components
 */

export const mockLiveScores: LiveScore[] = [
        {
                id: 'live-1',
                homeTeam: {
                        id: 'team-1',
                        name: 'Manchester United',
                        abbreviation: 'M<PERSON>',
                        logo: 'https://logos-world.net/wp-content/uploads/2020/06/Manchester-United-Logo.png'
                },
                awayTeam: {
                        id: 'team-2',
                        name: 'Liverpool',
                        abbreviation: 'LIV',
                        logo: 'https://logos-world.net/wp-content/uploads/2020/06/Liverpool-Logo.png'
                },
                homeScore: 2,
                awayScore: 1,
                matchTime: '67',
                status: 'live',
                venue: 'Old Trafford',
                league: {
                        id: 'league-1',
                        name: 'Premier League',
                        logo: 'https://logos-world.net/wp-content/uploads/2020/06/Premier-League-Logo-2016.png'
                },
                isHot: true,
                priority: 'high'
        },
        {
                id: 'live-2',
                homeTeam: {
                        id: 'team-3',
                        name: 'Real Madrid',
                        abbreviation: 'RMA',
                        logo: 'https://logos-world.net/wp-content/uploads/2020/06/Real-Madrid-Logo.png'
                },
                awayTeam: {
                        id: 'team-4',
                        name: 'Barcelona',
                        abbreviation: 'BAR',
                        logo: 'https://logos-world.net/wp-content/uploads/2020/06/Barcelona-Logo.png'
                },
                homeScore: 0,
                awayScore: 0,
                matchTime: 'HT',
                status: 'halftime',
                venue: 'Santiago Bernabéu',
                league: {
                        id: 'league-2',
                        name: 'La Liga',
                        logo: 'https://logos-world.net/wp-content/uploads/2020/06/LaLiga-Logo.png'
                },
                isHot: true,
                priority: 'high'
        },
        {
                id: 'coming-1',
                homeTeam: {
                        id: 'team-5',
                        name: 'Arsenal',
                        abbreviation: 'ARS',
                        logo: 'https://logos-world.net/wp-content/uploads/2020/06/Arsenal-Logo.png'
                },
                awayTeam: {
                        id: 'team-6',
                        name: 'Chelsea',
                        abbreviation: 'CHE',
                        logo: 'https://logos-world.net/wp-content/uploads/2020/06/Chelsea-Logo.png'
                },
                homeScore: 0,
                awayScore: 0,
                matchTime: '20:00',
                status: 'coming',
                venue: 'Emirates Stadium',
                league: {
                        id: 'league-1',
                        name: 'Premier League',
                        logo: 'https://logos-world.net/wp-content/uploads/2020/06/Premier-League-Logo-2016.png'
                },
                isHot: false,
                priority: 'medium'
        },
        {
                id: 'live-3',
                homeTeam: {
                        id: 'team-9',
                        name: 'Bayern Munich',
                        abbreviation: 'BAY',
                        logo: 'https://logos-world.net/wp-content/uploads/2020/06/Bayern-Munich-Logo.png'
                },
                awayTeam: {
                        id: 'team-10',
                        name: 'Borussia Dortmund',
                        abbreviation: 'BVB',
                        logo: 'https://logos-world.net/wp-content/uploads/2020/06/Borussia-Dortmund-Logo.png'
                },
                homeScore: 1,
                awayScore: 2,
                matchTime: '89',
                status: 'live',
                venue: 'Allianz Arena',
                league: {
                        id: 'league-3',
                        name: 'Bundesliga',
                        logo: 'https://logos-world.net/wp-content/uploads/2020/06/Bundesliga-Logo.png'
                },
                isHot: true,
                priority: 'high'
        },
        {
                id: 'coming-2',
                homeTeam: {
                        id: 'team-11',
                        name: 'PSG',
                        abbreviation: 'PSG',
                        logo: 'https://logos-world.net/wp-content/uploads/2020/06/PSG-Logo.png'
                },
                awayTeam: {
                        id: 'team-12',
                        name: 'Marseille',
                        abbreviation: 'OM',
                        logo: 'https://logos-world.net/wp-content/uploads/2020/06/Marseille-Logo.png'
                },
                homeScore: 0,
                awayScore: 0,
                matchTime: '21:30',
                status: 'coming',
                venue: 'Parc des Princes',
                league: {
                        id: 'league-4',
                        name: 'Ligue 1',
                        logo: 'https://logos-world.net/wp-content/uploads/2020/06/Ligue1-Logo.png'
                },
                isHot: false,
                priority: 'medium'
        }
];

export const mockFeaturedMatch: FeaturedMatch = {
        id: 'featured-1',
        homeTeam: {
                id: 'team-7',
                name: 'Manchester City',
                logo: 'https://logos-world.net/wp-content/uploads/2020/06/Manchester-City-Logo.png'
        },
        awayTeam: {
                id: 'team-8',
                name: 'Tottenham',
                logo: 'https://logos-world.net/wp-content/uploads/2020/06/Tottenham-Logo.png'
        },
        scheduledTime: new Date(Date.now() + 30 * 60 * 1000).toISOString(), // 30 minutes from now
        venue: 'Etihad Stadium',
        league: {
                id: 'league-1',
                name: 'Premier League',
                logo: 'https://logos-world.net/wp-content/uploads/2020/06/Premier-League-Logo-2016.png'
        },
        importance: 'high',
        description: 'Top of the table clash between two title contenders. Don\'t miss this crucial match that could decide the championship race!',
        isHot: true,
        status: 'coming'
};

export const mockCTAButtons: HeroCTAButton[] = [
        {
                label: 'View All Fixtures',
                href: '/fixtures',
                variant: 'primary'
        },
        {
                label: 'Watch Highlights',
                href: '/highlights',
                variant: 'secondary'
        }
];

// Combined data for easy import
export const mockHeroData = {
        liveScores: mockLiveScores,
        featuredMatch: mockFeaturedMatch,
        ctaButtons: mockCTAButtons
};
