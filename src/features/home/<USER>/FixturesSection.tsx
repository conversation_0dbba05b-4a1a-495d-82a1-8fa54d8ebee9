'use client';

import React from 'react';
import Link from 'next/link';
import { formatDate, formatTime } from '@/shared/utils/dateUtils';
import type { FixturesSectionProps, Fixture } from '../types';

const FixtureCard: React.FC<{ fixture: Fixture }> = ({ fixture }: { fixture: Fixture }) => {
        const statusColors = {
                scheduled: 'bg-blue-100 text-blue-800',
                live: 'bg-red-100 text-red-800 animate-pulse',
                finished: 'bg-green-100 text-green-800',
                postponed: 'bg-yellow-100 text-yellow-800',
                cancelled: 'bg-gray-100 text-gray-800'
        };

        const importanceColors = {
                high: 'border-l-4 border-red-500',
                medium: 'border-l-4 border-yellow-500',
                low: 'border-l-4 border-gray-300'
        };

        return (
                <Link href={`/fixture/${fixture.id}`} className="group">
                        <div className={`bg-white rounded-xl shadow-sm hover:shadow-lg transition-all duration-300 overflow-hidden ${importanceColors[fixture.importance]}`}>
                                {/* League Header */}
                                <div className="px-6 py-3 bg-gray-50 border-b border-gray-100">
                                        <div className="flex items-center justify-between">                                        <div className="flex items-center space-x-2">
                                                {fixture.league?.logo && (
                                                        <img
                                                                src={fixture.league.logo}
                                                                alt={`${fixture.league.name} logo`}
                                                                className="w-5 h-5 object-contain"
                                                        />
                                                )}
                                                <span className="text-sm font-medium text-gray-700">{fixture.league?.name}</span>
                                                {fixture.round && (
                                                        <>
                                                                <span className="w-1 h-1 bg-gray-400 rounded-full"></span>
                                                                <span className="text-sm text-gray-500">{fixture.round}</span>
                                                        </>
                                                )}
                                        </div>
                                                <span className={`px-2 py-1 rounded-full text-xs font-medium ${statusColors[fixture.status]}`}>
                                                        {fixture.status.charAt(0).toUpperCase() + fixture.status.slice(1)}
                                                </span>
                                        </div>
                                </div>

                                {/* Match Details */}
                                <div className="p-6">
                                        <div className="flex items-center justify-between">
                                                {/* Home Team */}
                                                <div className="flex items-center space-x-3 flex-1">
                                                        {fixture.homeTeam.logo && (
                                                                <img
                                                                        src={fixture.homeTeam.logo}
                                                                        alt={`${fixture.homeTeam.name} logo`}
                                                                        className="w-8 h-8 object-contain"
                                                                />
                                                        )}
                                                        <div>
                                                                <p className="font-medium text-gray-900">{fixture.homeTeam.name}</p>
                                                                <p className="text-sm text-gray-500">{fixture.homeTeam.shortName}</p>
                                                        </div>
                                                </div>

                                                {/* Score or Time */}
                                                <div className="flex-shrink-0 text-center px-4">
                                                        {fixture.status === 'live' && (
                                                                <div className="space-y-1">
                                                                        <div className="text-2xl font-bold text-red-600">
                                                                                {fixture.homeScore} - {fixture.awayScore}
                                                                        </div>
                                                                        <div className="text-xs text-red-600 font-medium">
                                                                                {fixture.matchTime || 'LIVE'}
                                                                        </div>
                                                                </div>
                                                        )}
                                                        {fixture.status === 'finished' && (
                                                                <div className="space-y-1">
                                                                        <div className="text-2xl font-bold text-gray-900">
                                                                                {fixture.homeScore} - {fixture.awayScore}
                                                                        </div>
                                                                        <div className="text-xs text-green-600 font-medium">FT</div>
                                                                </div>
                                                        )}
                                                        {(fixture.status === 'scheduled' || fixture.status === 'postponed') && (
                                                                <div className="space-y-1">
                                                                        <div className="text-lg font-semibold text-gray-700">
                                                                                {formatTime(fixture.scheduledTime)}
                                                                        </div>
                                                                        <div className="text-xs text-gray-500">
                                                                                {formatDate(fixture.scheduledTime)}
                                                                        </div>
                                                                </div>
                                                        )}
                                                        {fixture.status === 'cancelled' && (
                                                                <div className="text-sm font-medium text-red-600">
                                                                        CANCELLED
                                                                </div>
                                                        )}
                                                </div>

                                                {/* Away Team */}
                                                <div className="flex items-center space-x-3 flex-1 justify-end">
                                                        <div className="text-right">
                                                                <p className="font-medium text-gray-900">{fixture.awayTeam.name}</p>
                                                                <p className="text-sm text-gray-500">{fixture.awayTeam.shortName}</p>
                                                        </div>
                                                        {fixture.awayTeam.logo && (
                                                                <img
                                                                        src={fixture.awayTeam.logo}
                                                                        alt={`${fixture.awayTeam.name} logo`}
                                                                        className="w-8 h-8 object-contain"
                                                                />
                                                        )}
                                                </div>
                                        </div>

                                        {/* Additional Info */}
                                        {fixture.venue && (
                                                <div className="mt-4 pt-4 border-t border-gray-100">
                                                        <div className="flex items-center text-sm text-gray-500">
                                                                <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                                                        <path fillRule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd" />
                                                                </svg>
                                                                {fixture.venue}
                                                        </div>
                                                </div>
                                        )}
                                </div>
                        </div>
                </Link>
        );
};

export const FixturesSection: React.FC<FixturesSectionProps> = ({
        fixtures,
        title = "Fixtures & Results",
        showViewAll = true,
        maxItems = 6,
        className = ""
}: FixturesSectionProps) => {
        const [activeFilter, setActiveFilter] = React.useState<'all' | 'live' | 'upcoming' | 'completed'>('all');

        // Sort fixtures by date
        const sortedFixtures: Fixture[] = [...fixtures].sort((a: Fixture, b: Fixture) => {
                const dateA = new Date(a.scheduledTime).getTime();
                const dateB = new Date(b.scheduledTime).getTime();
                return dateA - dateB;
        });

        // Filter fixtures based on active filter
        const filteredFixtures: Fixture[] = sortedFixtures.filter((fixture: Fixture) => {
                switch (activeFilter) {
                        case 'live':
                                return fixture.status === 'live';
                        case 'upcoming':
                                return fixture.status === 'scheduled';
                        case 'completed':
                                return fixture.status === 'finished';
                        default:
                                return true;
                }
        }).slice(0, maxItems);

        // Separate live and other fixtures for better display
        const liveFixtures: Fixture[] = filteredFixtures.filter((fixture: Fixture) => fixture.status === 'live');
        const otherFixtures: Fixture[] = filteredFixtures.filter((fixture: Fixture) => fixture.status !== 'live');

        const filterOptions = [
                { key: 'all', label: 'All', count: sortedFixtures.length },
                { key: 'live', label: 'Live', count: sortedFixtures.filter((f: Fixture) => f.status === 'live').length },
                { key: 'upcoming', label: 'Upcoming', count: sortedFixtures.filter((f: Fixture) => f.status === 'scheduled').length },
                { key: 'completed', label: 'Results', count: sortedFixtures.filter((f: Fixture) => f.status === 'finished').length }
        ];

        return (
                <section className={`py-12 bg-gray-50 ${className}`}>
                        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                                {/* Section Header */}
                                <div className="flex items-center justify-between mb-8">
                                        <div>
                                                <h2 className="text-3xl font-bold text-gray-900">{title}</h2>
                                                <p className="mt-2 text-gray-600">Latest matches and upcoming fixtures</p>
                                        </div>
                                        {showViewAll && (
                                                <Link
                                                        href="/fixtures"
                                                        className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-blue-600 bg-blue-50 hover:bg-blue-100 transition-colors duration-200"
                                                >
                                                        View All Fixtures
                                                        <svg className="ml-2 w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                                                <path fillRule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clipRule="evenodd" />
                                                        </svg>
                                                </Link>
                                        )}
                                </div>

                                {/* Filter Tabs */}
                                <div className="mb-8">
                                        <div className="border-b border-gray-200">
                                                <nav className="-mb-px flex space-x-8">
                                                        {filterOptions.map((option) => (
                                                                <button
                                                                        key={option.key}
                                                                        onClick={() => setActiveFilter(option.key as 'all' | 'live' | 'upcoming' | 'completed')}
                                                                        className={`py-2 px-1 border-b-2 font-medium text-sm transition-colors duration-200 ${activeFilter === option.key
                                                                                ? 'border-blue-500 text-blue-600'
                                                                                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                                                                                }`}
                                                                >
                                                                        {option.label}
                                                                        <span className={`ml-2 py-0.5 px-2 rounded-full text-xs ${activeFilter === option.key
                                                                                ? 'bg-blue-100 text-blue-600'
                                                                                : 'bg-gray-100 text-gray-600'
                                                                                }`}>
                                                                                {option.count}
                                                                        </span>
                                                                </button>
                                                        ))}
                                                </nav>
                                        </div>
                                </div>

                                {/* Fixtures Grid */}
                                <div className="space-y-8">
                                        {filteredFixtures.length === 0 && (
                                                <div className="text-center py-12">
                                                        <div className="mx-auto h-12 w-12 text-gray-400 mb-4">
                                                                <svg className="w-8 h-8 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                                                        <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                                                </svg>
                                                        </div>
                                                        <h3 className="text-lg font-medium text-gray-900 mb-2">No fixtures found</h3>
                                                        <p className="text-gray-500">There are no matches for the selected filter.</p>
                                                </div>
                                        )}

                                        {/* Live Fixtures - Priority Display */}
                                        {liveFixtures.length > 0 && (
                                                <div className="space-y-6">
                                                        <h3 className="text-xl font-semibold text-gray-900 flex items-center">
                                                                <span className="w-2 h-2 bg-red-600 rounded-full mr-3 animate-pulse"></span>
                                                                Live Matches
                                                        </h3>
                                                        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                                                                {liveFixtures.map((fixture: Fixture) => (
                                                                        <FixtureCard key={fixture.id} fixture={fixture} />
                                                                ))}
                                                        </div>
                                                </div>
                                        )}

                                        {/* Other Fixtures */}
                                        {otherFixtures.length > 0 && (
                                                <div className="space-y-6">
                                                        <h3 className="text-xl font-semibold text-gray-900 flex items-center">
                                                                <span className="w-2 h-2 bg-blue-600 rounded-full mr-3"></span>
                                                                {activeFilter === 'upcoming' ? 'Upcoming Matches' :
                                                                        activeFilter === 'completed' ? 'Recent Results' :
                                                                                liveFixtures.length > 0 ? 'Other Matches' : 'All Matches'}
                                                        </h3>
                                                        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                                                                {otherFixtures.map((fixture: Fixture) => (
                                                                        <FixtureCard key={fixture.id} fixture={fixture} />
                                                                ))}
                                                        </div>
                                                </div>
                                        )}
                                </div>
                        </div>
                </section>
        );
};

export default FixturesSection;