# Magnetic Field System Hook - Advanced 3D Effects

## Tổng quan

Hook `useMagneticFieldSystem` là một hệ thống hiệu ứng từ trường 3D tiên tiến được thiết kế cho ứng dụng thể thao React. Hook này cung cấp khả năng tạo và quản lý các trường từ tương tác với hiệu suất cao, tối ưu hóa cho cả desktop và mobile.

## Tính năng chính

### 🎯 Core Features
- **Quản lý state với useReducer**: Sử dụng reducer pattern để quản lý state phức tạp
- **Spatial Partitioning**: Tối ưu hóa tính toán tương tác cho số lượng field lớn
- **Performance Monitoring**: Theo dõi FPS, memory usage và performance metrics
- **Field Interactions**: Tính toán và hiển thị tương tác giữa các trường từ

### 📱 Mobile Optimization
- **Auto Mobile Detection**: Tự động phát hiện thiết bị mobile
- **Battery Optimization**: Tối ưu hóa dựa trên mức pin
- **Touch Gestures**: Hỗ trợ tap và swipe gestures
- **Reduced Complexity**: Giảm độ phức tạp visual cho mobile

### ♿ Accessibility Features
- **High Contrast Mode**: Màu sắc tương phản cao
- **Reduced Motion**: Tắt animation cho accessibility
- **Screen Reader Support**: ARIA labels và descriptions
- **Simplified Interactions**: Giảm số lượng tương tác hiển thị

### 🔊 System Integrations
- **Audio System**: Tích hợp âm thanh cho field creation/destruction
- **Particle System**: Tích hợp hiệu ứng particle
- **Debug Panel**: Giao diện debug real-time

## Cách sử dụng

### Basic Usage

```typescript
import { useMagneticFieldSystem } from './hooks/useMagneticFieldSystem';

const MyComponent = () => {
  const magneticSystem = useMagneticFieldSystem({
    maxFields: 25,
    enableVisualization: true,
    fieldLifespan: 8000,
    defaultStrength: 100,
    interactionThreshold: 0.15,
    enableFieldInteractions: true,
    performanceMode: false,
    accessibilityMode: false,
    mobileOptimization: false,
    autoDetectMobile: true,
    batteryOptimization: true
  });

  const handleClick = (x: number, y: number) => {
    magneticSystem.createMagneticField(x, y, {
      type: 'attract',
      strength: 150,
      radius: 80
    });
  };

  return (
    <div onClick={(e) => handleClick(e.clientX, e.clientY)}>
      {/* Render magnetic fields */}
    </div>
  );
};
```

### Advanced Usage with Integrations

```typescript
const audioSystem = {
  playFieldCreation: (fieldType, strength) => {
    // Play sound effect
  },
  playFieldInteraction: (interactionStrength, fieldTypes) => {
    // Play interaction sound
  }
};

const particleSystem = {
  createFieldParticles: (field) => {
    // Create particle effects
  },
  updateFieldParticles: (fieldId, field) => {
    // Update particles
  }
};

const magneticSystem = useMagneticFieldSystem({
  // ... other config
  audioSystem,
  particleSystem,
  enableDebugMode: true
});
```

## API Reference

### Configuration Options

| Option | Type | Default | Description |
|--------|------|---------|-------------|
| `maxFields` | number | 25 | Số lượng field tối đa |
| `enableVisualization` | boolean | true | Bật/tắt hiển thị visual |
| `fieldLifespan` | number | 8000 | Thời gian sống của field (ms) |
| `defaultStrength` | number | 100 | Cường độ mặc định |
| `performanceMode` | boolean | false | Chế độ hiệu suất cao |
| `accessibilityMode` | boolean | false | Chế độ accessibility |
| `mobileOptimization` | boolean | false | Tối ưu hóa mobile |
| `batteryOptimization` | boolean | false | Tối ưu hóa pin |

### Returned Methods

| Method | Description |
|--------|-------------|
| `createMagneticField(x, y, options)` | Tạo field mới |
| `createGoalCelebrationFields(x, y, intensity)` | Tạo hiệu ứng ăn mừng |
| `createEnergySurgeFields(path, options)` | Tạo sóng năng lượng |
| `clearAllFields()` | Xóa tất cả fields |
| `resetSystem()` | Reset hệ thống |
| `toggleActive()` | Bật/tắt hệ thống |
| `getPerformanceMetrics()` | Lấy metrics hiệu suất |

### Returned Data

| Property | Type | Description |
|----------|------|-------------|
| `magneticFields` | MagneticField[] | Danh sách fields |
| `fieldInteractions` | MagneticFieldInteraction[] | Danh sách tương tác |
| `visualizationData` | Object | Dữ liệu để render |
| `debugMetrics` | DebugMetrics | Metrics debug |
| `isActive` | boolean | Trạng thái hoạt động |

## Performance Optimization

### Spatial Partitioning
- Sử dụng grid-based partitioning cho > 20 fields
- Giảm độ phức tạp từ O(n²) xuống O(n)
- Tự động bật trong performance mode

### Mobile Optimizations
- Giảm 30% radius và 20% strength
- Giảm 40% số lượng fields tối đa
- Tắt pulse animations
- Tăng thời gian giữa updates

### Battery Optimizations
- Tự động bật performance mode khi pin < 20%
- Giảm tần suất cập nhật
- Tắt các hiệu ứng không cần thiết

## Testing

Chạy tests:
```bash
npm test -- --testPathPattern=useMagneticFieldSystem.test.ts
```

Test coverage bao gồm:
- ✅ Basic functionality
- ✅ Mobile optimization
- ✅ Accessibility mode
- ✅ Audio/Particle integration
- ✅ Performance metrics
- ✅ Goal celebration effects

## Components

### MagneticFieldRenderer
Component để render magnetic fields với Canvas API:
- Tối ưu hóa rendering cho mobile
- Hỗ trợ accessibility mode
- Performance-aware rendering

### MagneticFieldDebugPanel
Panel debug real-time:
- Performance metrics
- Battery status
- Field statistics
- Quick configuration controls

### MagneticFieldDemo
Component demo đầy đủ tính năng:
- Click để tạo fields
- Touch gestures cho mobile
- Audio/particle integrations
- Debug panel

## Browser Support

- ✅ Chrome 80+
- ✅ Firefox 75+
- ✅ Safari 13+
- ✅ Edge 80+
- ✅ Mobile browsers

## Performance Benchmarks

| Device Type | Max Fields | FPS Target | Memory Usage |
|-------------|------------|------------|--------------|
| Desktop | 25 | 60 FPS | < 5MB |
| Mobile | 15 | 30 FPS | < 3MB |
| Low-end Mobile | 10 | 20 FPS | < 2MB |

## Roadmap

- [ ] WebGL renderer cho hiệu suất cao hơn
- [ ] Worker threads cho tính toán nặng
- [ ] VR/AR support
- [ ] Machine learning để tối ưu tự động
- [ ] Real-time multiplayer sync
