'use client';

import React from 'react';
import Link from 'next/link';
import { QuickNavCard as QuickNavCardType } from '../../types';
import { NavCardIcon } from './NavCardIcon';

interface QuickNavCardProps {
        card: QuickNavCardType;
        className?: string;
}

/**
 * Quick Navigation Card Component
 * Displays a navigation card with icon, title, description, and count
 */
export const QuickNavCard: React.FC<QuickNavCardProps> = ({
        card,
        className = ''
}) => {
        const { title, description, count, href, icon, theme, isActive } = card;

        const getHoverClasses = () => {
                switch (theme) {
                        case 'blue':
                                return 'hover:shadow-blue-100 hover:border-blue-200';
                        case 'red':
                                return 'hover:shadow-red-100 hover:border-red-200';
                        case 'gold':
                                return 'hover:shadow-yellow-100 hover:border-yellow-200';
                        case 'green':
                                return 'hover:shadow-green-100 hover:border-green-200';
                        default:
                                return 'hover:shadow-gray-100 hover:border-gray-200';
                }
        };

        const getCountColor = () => {
                switch (theme) {
                        case 'blue':
                                return 'text-blue-600';
                        case 'red':
                                return 'text-red-600';
                        case 'gold':
                                return 'text-yellow-600';
                        case 'green':
                                return 'text-green-600';
                        default:
                                return 'text-gray-600';
                }
        };

        return (
                <Link
                        href={href}
                        className={`block group ${className}`}
                        aria-label={`${title}: ${count} items`}
                >
                        <div className={`
        relative bg-white rounded-xl border border-gray-200 p-6 
        transition-all duration-200 
        hover:shadow-lg hover:-translate-y-1 
        ${getHoverClasses()}
        ${isActive ? 'ring-2 ring-blue-500 ring-opacity-20' : ''}
      `}>
                                {/* Header with Icon and Count */}
                                <div className="flex items-center justify-between mb-4">
                                        <NavCardIcon type={icon} theme={theme} />
                                        <div className="text-right">
                                                <div className={`text-2xl font-bold ${getCountColor()}`}>
                                                        {count.toLocaleString()}
                                                </div>
                                                {isActive && (
                                                        <div className="text-xs text-gray-500 uppercase tracking-wide">
                                                                Active
                                                        </div>
                                                )}
                                        </div>
                                </div>

                                {/* Content */}
                                <div className="space-y-2">
                                        <h3 className="text-lg font-semibold text-gray-900 group-hover:text-gray-700">
                                                {title}
                                        </h3>
                                        <p className="text-sm text-gray-600 leading-relaxed">
                                                {description}
                                        </p>
                                </div>

                                {/* Action Indicator */}
                                <div className="mt-4 flex items-center text-sm text-gray-500 group-hover:text-gray-700">
                                        <span>View all</span>
                                        <svg
                                                className="ml-1 w-4 h-4 transition-transform group-hover:translate-x-1"
                                                fill="none"
                                                stroke="currentColor"
                                                viewBox="0 0 24 24"
                                        >
                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                                        </svg>
                                </div>

                                {/* Live Indicator for live matches */}
                                {icon === 'live' && count > 0 && (
                                        <div className="absolute top-4 right-4 flex items-center">
                                                <div className="w-2 h-2 bg-red-500 rounded-full animate-pulse"></div>
                                                <span className="ml-1 text-xs text-red-600 font-medium">LIVE</span>
                                        </div>
                                )}
                        </div>
                </Link>
        );
};

export default QuickNavCard;
