'use client';

import React from 'react';

interface NavCardIconProps {
        type: 'fixtures' | 'live' | 'leagues' | 'news';
        theme: 'blue' | 'red' | 'gold' | 'green';
        className?: string;
}

/**
 * Navigation Card Icon Component
 * Displays appropriate icon based on card type with theme colors
 */
export const NavCardIcon: React.FC<NavCardIconProps> = ({
        type,
        theme,
        className = ''
}) => {
        const getThemeClasses = () => {
                switch (theme) {
                        case 'blue':
                                return 'text-blue-500 bg-blue-50';
                        case 'red':
                                return 'text-red-500 bg-red-50';
                        case 'gold':
                                return 'text-yellow-500 bg-yellow-50';
                        case 'green':
                                return 'text-green-500 bg-green-50';
                        default:
                                return 'text-gray-500 bg-gray-50';
                }
        };

        const getIcon = () => {
                const iconProps = {
                        className: "w-6 h-6",
                        fill: "none",
                        stroke: "currentColor",
                        viewBox: "0 0 24 24",
                        strokeWidth: 2
                };

                switch (type) {
                        case 'fixtures':
                                return (
                                        <svg {...iconProps}>
                                                <path strokeLinecap="round" strokeLinejoin="round" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                        </svg>
                                );
                        case 'live':
                                return (
                                        <svg {...iconProps}>
                                                <path strokeLinecap="round" strokeLinejoin="round" d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z" />
                                                <path strokeLinecap="round" strokeLinejoin="round" d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                        </svg>
                                );
                        case 'leagues':
                                return (
                                        <svg {...iconProps}>
                                                <path strokeLinecap="round" strokeLinejoin="round" d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z" />
                                        </svg>
                                );
                        case 'news':
                                return (
                                        <svg {...iconProps}>
                                                <path strokeLinecap="round" strokeLinejoin="round" d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z" />
                                        </svg>
                                );
                        default:
                                return (
                                        <svg {...iconProps}>
                                                <path strokeLinecap="round" strokeLinejoin="round" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                        </svg>
                                );
                }
        };

        return (
                <div className={`inline-flex items-center justify-center w-12 h-12 rounded-lg ${getThemeClasses()} ${className}`}>
                        {getIcon()}
                </div>
        );
};

export default NavCardIcon;
