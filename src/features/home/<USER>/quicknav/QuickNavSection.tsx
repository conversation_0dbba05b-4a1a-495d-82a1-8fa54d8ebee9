'use client';

import React from 'react';
import { QuickNavSectionProps } from '../../types';
import { QuickNavCard } from './QuickNavCard';

/**
 * Quick Navigation Section Component
 * Container for quick navigation cards with responsive grid layout
 */
export const QuickNavSection: React.FC<QuickNavSectionProps> = ({
        cards,
        title = 'Quick Access',
        className = ''
}) => {
        if (!cards || cards.length === 0) {
                return null;
        }

        return (
                <section className={`py-12 bg-gray-50 ${className}`} aria-label="Quick navigation">
                        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
                                {/* Section Title */}
                                {title && (
                                        <div className="text-center mb-8">
                                                <h2 className="text-2xl md:text-3xl font-bold text-gray-900 mb-2">
                                                        {title}
                                                </h2>
                                                <p className="text-gray-600 max-w-2xl mx-auto">
                                                        Quick access to the most important sections of our sports platform
                                                </p>
                                        </div>
                                )}

                                {/* Cards Grid */}
                                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 md:gap-8">
                                        {cards.map((card) => (
                                                <QuickNavCard
                                                        key={card.id}
                                                        card={card}
                                                        className="h-full"
                                                />
                                        ))}
                                </div>

                                {/* Additional Info */}
                                <div className="mt-8 text-center">
                                        <p className="text-sm text-gray-500">
                                                Data updates every few minutes to ensure you have the latest information
                                        </p>
                                </div>
                        </div>
                </section>
        );
};

export default QuickNavSection;
