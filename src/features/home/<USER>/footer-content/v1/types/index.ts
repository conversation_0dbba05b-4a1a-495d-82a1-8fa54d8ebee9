// Footer Content V1 Types

export interface PopularTeam {
  id: string;
  name: string;
  slug: string;
  logo: string;
  league: string;
  leagueId: string;
  followers: number;
  isVerified?: boolean;
  country?: string;
}

export interface PopularPlayer {
  id: string;
  name: string;
  slug: string;
  photo: string;
  position: string;
  team: string;
  teamId: string;
  teamLogo: string;
  nationality: string;
  age: number;
  marketValue?: string;
  isTopScorer?: boolean;
}

export interface AppDownloadLink {
  platform: 'ios' | 'android' | 'web';
  url: string;
  icon: string;
  label: string;
  version?: string;
  size?: string;
  rating?: number;
  downloads?: string;
}

export interface SocialLink {
  platform: 'facebook' | 'twitter' | 'instagram' | 'youtube' | 'tiktok';
  url: string;
  icon: string;
  label: string;
  followers?: string;
}

export interface FooterSection {
  title: string;
  links: {
    label: string;
    href: string;
    isExternal?: boolean;
    icon?: string;
  }[];
}

export interface FooterContentData {
  popularTeams: PopularTeam[];
  popularPlayers: PopularPlayer[];
  appDownloads: AppDownloadLink[];
  socialLinks: SocialLink[];
  footerSections: FooterSection[];
  companyInfo: {
    name: string;
    description: string;
    logo: string;
    copyright: string;
    establishedYear: number;
  };
  contactInfo: {
    email: string;
    phone?: string;
    address?: string;
    supportHours?: string;
  };
}

export interface FooterContentProps {
  className?: string;
  showPopularTeams?: boolean;
  showPopularPlayers?: boolean;
  showAppDownload?: boolean;
  showSocialLinks?: boolean;
  showCompanyInfo?: boolean;
  maxTeams?: number;
  maxPlayers?: number;
  theme?: 'light' | 'dark';
}

// Quick Links configuration
export interface QuickLinksConfig {
  teams: {
    enabled: boolean;
    maxItems: number;
    showLogos: boolean;
    showFollowers: boolean;
  };
  players: {
    enabled: boolean;
    maxItems: number;
    showPhotos: boolean;
    showTeams: boolean;
    showPositions: boolean;
  };
}

// App promotion configuration
export interface AppPromotionConfig {
  enabled: boolean;
  title: string;
  subtitle: string;
  features: string[];
  showRatings: boolean;
  showDownloadStats: boolean;
  ctaText: string;
}

// Layout configuration
export interface LayoutConfig {
  columns: 'auto' | 2 | 3 | 4 | 5;
  spacing: 'compact' | 'normal' | 'spacious';
  alignment: 'left' | 'center' | 'right';
  showDividers: boolean;
}

// Animation configuration
export interface AnimationConfig {
  enabled: boolean;
  type: 'fade' | 'slide' | 'scale';
  duration: number;
  stagger: number;
}
