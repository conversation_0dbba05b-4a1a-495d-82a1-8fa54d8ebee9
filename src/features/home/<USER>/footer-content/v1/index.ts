// Footer Content V1 - Component Exports
// This file exports all V1 components and types

// Main component
export { FooterContentV1 } from './FooterContentV1';

// Types
export type {
  PopularTeam,
  PopularPlayer,
  AppDownloadLink,
  SocialLink,
  FooterSection,
  FooterContentData,
  FooterContentProps,
  QuickLinksConfig,
  AppPromotionConfig,
  LayoutConfig,
  AnimationConfig
} from './types';

// Component metadata
export const V1_COMPONENT_INFO = {
  name: 'Footer Content V1',
  description: 'Initial implementation with Popular Teams/Players and App Download promotion',
  features: [
    'Popular Teams quick links',
    'Popular Players showcase',
    'Mobile app promotion',
    'Social media links',
    'Company information',
    'Quick navigation links',
    'Contact information',
    'Responsive design',
    'Theme support (light/dark)',
    'CDN image integration'
  ],
  version: '1.0.0',
  status: 'stable'
} as const;

// Default props for V1
export const DEFAULT_PROPS_V1 = {
  className: '',
  showPopularTeams: true,
  showPopularPlayers: true,
  showAppDownload: true,
  showSocialLinks: true,
  showCompanyInfo: true,
  maxTeams: 6,
  maxPlayers: 6,
  theme: 'dark' as const
} as const;

// Quick Links configurations
export const QUICK_LINKS_CONFIGS = {
  teams: {
    enabled: true,
    maxItems: 6,
    showLogos: true,
    showFollowers: true
  },
  players: {
    enabled: true,
    maxItems: 6,
    showPhotos: true,
    showTeams: true,
    showPositions: true
  }
} as const;

// App promotion configurations
export const APP_PROMOTION_CONFIG = {
  enabled: true,
  title: 'Get Our App',
  subtitle: 'Download our mobile app for the best experience',
  features: [
    'Live match notifications',
    'Offline reading mode',
    'Personalized news feed',
    'Video highlights'
  ],
  showRatings: true,
  showDownloadStats: true,
  ctaText: 'Download Now'
} as const;

// Layout configurations
export const LAYOUT_CONFIGS = {
  default: {
    columns: 4 as const,
    spacing: 'normal' as const,
    alignment: 'left' as const,
    showDividers: true
  },
  compact: {
    columns: 3 as const,
    spacing: 'compact' as const,
    alignment: 'left' as const,
    showDividers: false
  },
  spacious: {
    columns: 4 as const,
    spacing: 'spacious' as const,
    alignment: 'center' as const,
    showDividers: true
  }
} as const;

// Animation presets
export const ANIMATION_PRESETS_V1 = {
  fadeIn: {
    enabled: true,
    type: 'fade' as const,
    duration: 300,
    stagger: 50
  },
  slideUp: {
    enabled: true,
    type: 'slide' as const,
    duration: 400,
    stagger: 100
  },
  scaleIn: {
    enabled: true,
    type: 'scale' as const,
    duration: 200,
    stagger: 75
  }
} as const;

// Performance configuration
export const PERFORMANCE_CONFIG_V1 = {
  LAZY_LOADING: true,
  IMAGE_OPTIMIZATION: true,
  ANIMATION_REDUCED_MOTION: true,
  CACHE_STRATEGY: 'stale-while-revalidate',
  PREFETCH_ENABLED: true,
  CDN_INTEGRATION: true
} as const;

// Theme configurations
export const THEME_CONFIGS = {
  light: {
    background: 'bg-gray-50',
    text: 'text-gray-900',
    border: 'border-gray-200',
    card: 'bg-white hover:bg-gray-50 border-gray-200',
    accent: 'text-blue-600'
  },
  dark: {
    background: 'bg-gray-900',
    text: 'text-white',
    border: 'border-gray-700',
    card: 'bg-gray-800 hover:bg-gray-700 border-gray-700',
    accent: 'text-blue-400'
  }
} as const;
