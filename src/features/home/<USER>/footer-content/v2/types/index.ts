// Footer Content V2 Types - Minimalist Design

export interface QuickLink {
  id: string;
  label: string;
  href: string;
  isExternal?: boolean;
  icon?: string;
}

export interface AppDownloadV2 {
  platform: 'ios' | 'android';
  url: string;
  label: string;
  icon: string;
}

export interface SocialLinkV2 {
  platform: 'twitter' | 'instagram' | 'youtube';
  url: string;
  icon: string;
  label: string;
}

export interface FooterContentDataV2 {
  quickLinks: QuickLink[];
  appDownloads: AppDownloadV2[];
  socialLinks: SocialLinkV2[];
  companyInfo: {
    name: string;
    tagline: string;
    copyright: string;
  };
}

export interface FooterContentV2Props {
  className?: string;
  showQuickLinks?: boolean;
  showAppDownload?: boolean;
  showSocialLinks?: boolean;
  theme?: 'light' | 'dark' | 'minimal';
  layout?: 'centered' | 'spread' | 'compact';
  maxLinks?: number;
}

// Minimalist configuration
export interface MinimalistConfig {
  spacing: 'tight' | 'normal' | 'loose';
  typography: 'compact' | 'normal' | 'spacious';
  colors: 'monochrome' | 'subtle' | 'accent';
  borders: boolean;
  shadows: boolean;
  animations: 'none' | 'subtle' | 'smooth';
}

// Layout variants for V2
export type LayoutVariant = 'centered' | 'spread' | 'compact';

// Theme variants for V2
export type ThemeVariant = 'light' | 'dark' | 'minimal';

// Animation types for minimalist design
export type AnimationType = 'none' | 'subtle' | 'smooth';
