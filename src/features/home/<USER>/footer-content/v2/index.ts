// Footer Content V2 - Component Exports
// Minimalist and Modern Design

// Main component
export { FooterContentV2 } from './FooterContentV2';

// Types
export type {
  QuickLink,
  AppDownloadV2,
  SocialLinkV2,
  FooterContentDataV2,
  FooterContentV2Props,
  MinimalistConfig,
  LayoutVariant,
  ThemeVariant,
  AnimationType
} from './types';

// Component metadata
export const V2_COMPONENT_INFO = {
  name: 'Footer Content V2',
  description: 'Minimalist and modern design with clean typography and simplified layout',
  features: [
    'Minimalist design philosophy',
    'Clean typography with light fonts',
    'Simplified quick links grid',
    'Reduced visual noise',
    'Modern spacing system',
    'Focused content approach',
    'Subtle animations',
    'Three layout variants',
    'Three theme options',
    'Mobile-first responsive'
  ],
  version: '2.0.0',
  status: 'stable',
  designPrinciples: [
    'Less is more',
    'White space as design element',
    'Typography hierarchy',
    'Subtle interactions',
    'Content-first approach'
  ]
} as const;

// Default props for V2 - Minimalist defaults
export const DEFAULT_PROPS_V2 = {
  className: '',
  showQuickLinks: true,
  showAppDownload: true,
  showSocialLinks: true,
  theme: 'minimal' as const,
  layout: 'centered' as const,
  maxLinks: 6
} as const;

// Minimalist configurations
export const MINIMALIST_CONFIGS = {
  ultraMinimal: {
    spacing: 'loose' as const,
    typography: 'spacious' as const,
    colors: 'monochrome' as const,
    borders: false,
    shadows: false,
    animations: 'subtle' as const
  },
  clean: {
    spacing: 'normal' as const,
    typography: 'normal' as const,
    colors: 'subtle' as const,
    borders: true,
    shadows: false,
    animations: 'smooth' as const
  },
  modern: {
    spacing: 'tight' as const,
    typography: 'compact' as const,
    colors: 'accent' as const,
    borders: true,
    shadows: true,
    animations: 'smooth' as const
  }
} as const;

// Layout configurations for V2
export const LAYOUT_CONFIGS_V2 = {
  centered: {
    alignment: 'center',
    maxWidth: 'max-w-4xl',
    spacing: 'space-y-12',
    gridCols: 'grid-cols-6'
  },
  spread: {
    alignment: 'left',
    maxWidth: 'max-w-7xl',
    spacing: 'space-y-8',
    gridCols: 'grid-cols-8'
  },
  compact: {
    alignment: 'center',
    maxWidth: 'max-w-3xl',
    spacing: 'space-y-8',
    gridCols: 'grid-cols-4'
  }
} as const;

// Theme configurations for V2
export const THEME_CONFIGS_V2 = {
  light: {
    background: 'bg-white',
    text: 'text-gray-900',
    border: 'border-gray-100',
    hover: 'hover:bg-black/5',
    accent: 'text-blue-600'
  },
  dark: {
    background: 'bg-gray-950',
    text: 'text-gray-100',
    border: 'border-gray-800',
    hover: 'hover:bg-white/5',
    accent: 'text-blue-400'
  },
  minimal: {
    background: 'bg-gray-50',
    text: 'text-gray-800',
    border: 'border-gray-200',
    hover: 'hover:bg-black/5',
    accent: 'text-gray-900'
  }
} as const;

// Animation presets for V2 - Subtle and smooth
export const ANIMATION_PRESETS_V2 = {
  none: {
    duration: 0,
    easing: 'linear' as const,
    hover: false,
    scale: false
  },
  subtle: {
    duration: 200,
    easing: 'ease-out' as const,
    hover: true,
    scale: false
  },
  smooth: {
    duration: 300,
    easing: 'ease-out' as const,
    hover: true,
    scale: true
  }
} as const;

// Typography system for V2
export const TYPOGRAPHY_SYSTEM_V2 = {
  heading: {
    size: 'text-2xl',
    weight: 'font-light',
    spacing: 'tracking-wide',
    lineHeight: 'leading-relaxed'
  },
  subheading: {
    size: 'text-sm',
    weight: 'font-light',
    spacing: 'tracking-normal',
    opacity: 'opacity-60'
  },
  link: {
    size: 'text-sm',
    weight: 'font-medium',
    spacing: 'tracking-normal',
    opacity: 'opacity-70 hover:opacity-100'
  },
  caption: {
    size: 'text-xs',
    weight: 'font-light',
    spacing: 'tracking-wide',
    opacity: 'opacity-50'
  }
} as const;

// Performance configuration for V2
export const PERFORMANCE_CONFIG_V2 = {
  LAZY_LOADING: true,
  REDUCED_MOTION: true,
  MINIMAL_ANIMATIONS: true,
  FAST_LOADING: true,
  CACHE_STRATEGY: 'stale-while-revalidate',
  PREFETCH_ENABLED: false // Disabled for minimal approach
} as const;
