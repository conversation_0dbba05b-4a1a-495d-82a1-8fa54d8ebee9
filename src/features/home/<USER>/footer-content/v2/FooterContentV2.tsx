'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { FooterContentV2Props, FooterContentDataV2, LayoutVariant, ThemeVariant } from './types';

export const FooterContentV2: React.FC<FooterContentV2Props> = ({
  className = '',
  showQuickLinks = true,
  showAppDownload = true,
  showSocialLinks = true,
  theme = 'minimal',
  layout = 'centered',
  maxLinks = 6
}) => {
  const [isLoading, setIsLoading] = useState(true);
  const [data, setData] = useState<FooterContentDataV2 | null>(null);

  // Minimalist mock data - clean and focused
  const mockData: FooterContentDataV2 = {
    quickLinks: [
      { id: '1', label: 'Live Scores', href: '/live-scores', icon: '⚡' },
      { id: '2', label: 'Fixtures', href: '/fixtures', icon: '📅' },
      { id: '3', label: 'Teams', href: '/teams', icon: '🏆' },
      { id: '4', label: 'Players', href: '/players', icon: '⭐' },
      { id: '5', label: 'News', href: '/news', icon: '📰' },
      { id: '6', label: 'Help', href: '/help', icon: '❓' }
    ],
    appDownloads: [
      {
        platform: 'ios',
        url: 'https://apps.apple.com/app/sports-game',
        label: 'iOS',
        icon: '📱'
      },
      {
        platform: 'android',
        url: 'https://play.google.com/store/apps/details?id=com.sportsgame',
        label: 'Android',
        icon: '🤖'
      }
    ],
    socialLinks: [
      {
        platform: 'twitter',
        url: 'https://twitter.com/sportsgame',
        icon: '𝕏',
        label: 'Twitter'
      },
      {
        platform: 'instagram',
        url: 'https://instagram.com/sportsgame',
        icon: '📷',
        label: 'Instagram'
      },
      {
        platform: 'youtube',
        url: 'https://youtube.com/sportsgame',
        icon: '📺',
        label: 'YouTube'
      }
    ],
    companyInfo: {
      name: 'Sports Game',
      tagline: 'Live football, simplified.',
      copyright: '© 2024 Sports Game'
    }
  };

  useEffect(() => {
    // Simulate API call with faster loading for minimalist feel
    const loadData = async () => {
      setIsLoading(true);
      await new Promise(resolve => setTimeout(resolve, 400));
      setData(mockData);
      setIsLoading(false);
    };

    loadData();
  }, []);

  // Get theme classes - minimalist approach
  const getThemeClasses = (): string => {
    const themes = {
      light: 'bg-white text-gray-900 border-gray-100',
      dark: 'bg-gray-950 text-gray-100 border-gray-800',
      minimal: 'bg-gray-50 text-gray-800 border-gray-200'
    };
    return themes[theme];
  };

  // Get layout classes
  const getLayoutClasses = (): string => {
    const layouts = {
      centered: 'text-center max-w-4xl mx-auto',
      spread: 'text-left max-w-7xl mx-auto',
      compact: 'text-center max-w-3xl mx-auto'
    };
    return layouts[layout];
  };

  // Minimalist loading state
  if (isLoading || !data) {
    return (
      <footer className={`${getThemeClasses()} py-16 border-t ${className}`}>
        <div className="container mx-auto px-6">
          <div className={getLayoutClasses()}>
            <div className="space-y-8">
              <div className="h-8 bg-gray-200 rounded-lg w-48 mx-auto animate-pulse"></div>
              <div className="h-4 bg-gray-200 rounded w-32 mx-auto animate-pulse"></div>
              <div className="flex justify-center space-x-6">
                {[1, 2, 3, 4, 5, 6].map((i) => (
                  <div key={i} className="h-4 bg-gray-200 rounded w-16 animate-pulse"></div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </footer>
    );
  }

  return (
    <footer className={`${getThemeClasses()} py-20 border-t ${className}`}>
      <div className="container mx-auto px-6">
        <div className={getLayoutClasses()}>
          {/* Company Info - Minimalist */}
          <div className="mb-12">
            <h2 className="text-2xl font-light mb-3 tracking-wide">
              {data.companyInfo.name}
            </h2>
            <p className="text-sm opacity-60 font-light">
              {data.companyInfo.tagline}
            </p>
          </div>

          {/* Quick Links - Clean Grid */}
          {showQuickLinks && (
            <div className="mb-12">
              <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-6 max-w-2xl mx-auto">
                {data.quickLinks.slice(0, maxLinks).map((link) => (
                  <Link
                    key={link.id}
                    href={link.href}
                    className="group flex flex-col items-center space-y-2 p-4 rounded-xl transition-all duration-300 hover:bg-black/5 dark:hover:bg-white/5"
                  >
                    <span className="text-2xl group-hover:scale-110 transition-transform duration-300">
                      {link.icon}
                    </span>
                    <span className="text-sm font-medium opacity-70 group-hover:opacity-100 transition-opacity">
                      {link.label}
                    </span>
                  </Link>
                ))}
              </div>
            </div>
          )}

          {/* App Download - Simplified */}
          {showAppDownload && (
            <div className="mb-12">
              <p className="text-sm opacity-60 mb-6 font-light">
                Get the app
              </p>
              <div className="flex justify-center space-x-4">
                {data.appDownloads.map((app) => (
                  <a
                    key={app.platform}
                    href={app.url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="group flex items-center space-x-3 px-6 py-3 rounded-full border border-current/20 hover:border-current/40 transition-all duration-300 hover:scale-105"
                  >
                    <span className="text-lg">{app.icon}</span>
                    <span className="text-sm font-medium">{app.label}</span>
                  </a>
                ))}
              </div>
            </div>
          )}

          {/* Social Links - Minimal Icons */}
          {showSocialLinks && (
            <div className="mb-12">
              <div className="flex justify-center space-x-6">
                {data.socialLinks.map((social) => (
                  <a
                    key={social.platform}
                    href={social.url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="group w-12 h-12 rounded-full border border-current/20 flex items-center justify-center hover:border-current/40 transition-all duration-300 hover:scale-110"
                    title={social.label}
                  >
                    <span className="text-lg group-hover:scale-110 transition-transform duration-300">
                      {social.icon}
                    </span>
                  </a>
                ))}
              </div>
            </div>
          )}

          {/* Copyright - Ultra Minimal */}
          <div className="pt-8 border-t border-current/10">
            <p className="text-xs opacity-50 font-light tracking-wide">
              {data.companyInfo.copyright}
            </p>
          </div>
        </div>
      </div>
    </footer>
  );
};
