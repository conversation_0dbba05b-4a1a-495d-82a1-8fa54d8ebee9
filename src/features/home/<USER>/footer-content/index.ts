// Footer Content - Version Management
// This file manages all versions of Footer Content components

// Version 1 - Initial implementation with Popular Teams/Players and App Download
export { FooterContentV1 } from './v1/FooterContentV1';

// Types - Export all types for external use
export type { 
  PopularTeam,
  PopularPlayer,
  AppDownloadLink,
  SocialLink,
  FooterSection,
  FooterContentData,
  FooterContentProps,
  QuickLinksConfig,
  AppPromotionConfig,
  LayoutConfig,
  AnimationConfig
} from './v1/types';

// Default export - Current active version
// Change this to switch the default version used throughout the app
export { FooterContentV1 as FooterContent } from './v1/FooterContentV1';

// Version mapping for dynamic imports
export const FOOTER_CONTENT_VERSIONS = {
  v1: () => import('./v1/FooterContentV1').then(m => m.FooterContentV1),
  // v2: () => import('./v2/FooterContentV2').then(m => m.FooterContentV2),
  // v3: () => import('./v3/FooterContentV3').then(m => m.FooterContentV3),
} as const;

// Version metadata
export const VERSION_INFO = {
  v1: {
    name: 'Footer Content V1',
    description: 'Initial implementation with Popular Teams/Players and App Download promotion',
    features: [
      'Popular Teams Quick Links',
      'Popular Players Showcase', 
      'Mobile App Promotion',
      'Social Media Links',
      'Company Information',
      'Quick Navigation',
      'Contact Information',
      'Theme Support'
    ],
    status: 'stable',
    releaseDate: '2024-01-20'
  }
  // v2: {
  //   name: 'Footer Content V2',
  //   description: 'Enhanced version with newsletter signup and advanced features',
  //   features: [
  //     'Newsletter Subscription',
  //     'Advanced Team Stats',
  //     'Player Performance Metrics',
  //     'Enhanced App Features',
  //     'Multi-language Support',
  //     'Dark/Light Theme Toggle'
  //   ],
  //   status: 'development',
  //   releaseDate: 'TBD'
  // }
} as const;

// Current version constant
export const CURRENT_VERSION = 'v1' as const;

// Helper function to get version component
export const getFooterContentVersion = (version: keyof typeof FOOTER_CONTENT_VERSIONS = CURRENT_VERSION) => {
  return FOOTER_CONTENT_VERSIONS[version];
};

// Helper function to get version info
export const getVersionInfo = (version: keyof typeof VERSION_INFO = CURRENT_VERSION) => {
  return VERSION_INFO[version];
};

// Component registry for dynamic imports
export const COMPONENT_REGISTRY = {
  v1: {
    component: () => import('./v1').then(m => m.FooterContentV1),
    types: () => import('./v1/types'),
    metadata: VERSION_INFO.v1
  }
  // v2: {
  //   component: () => import('./v2').then(m => m.FooterContentV2),
  //   types: () => import('./v2/types'),
  //   metadata: VERSION_INFO.v2
  // }
} as const;

// Configuration presets
export const CONFIG_PRESETS = {
  minimal: {
    showPopularTeams: true,
    showPopularPlayers: false,
    showAppDownload: true,
    showSocialLinks: true,
    showCompanyInfo: true,
    maxTeams: 4,
    maxPlayers: 0
  },
  full: {
    showPopularTeams: true,
    showPopularPlayers: true,
    showAppDownload: true,
    showSocialLinks: true,
    showCompanyInfo: true,
    maxTeams: 6,
    maxPlayers: 6
  },
  teamsOnly: {
    showPopularTeams: true,
    showPopularPlayers: false,
    showAppDownload: false,
    showSocialLinks: true,
    showCompanyInfo: true,
    maxTeams: 8,
    maxPlayers: 0
  },
  playersOnly: {
    showPopularTeams: false,
    showPopularPlayers: true,
    showAppDownload: false,
    showSocialLinks: true,
    showCompanyInfo: true,
    maxTeams: 0,
    maxPlayers: 8
  }
} as const;

// API endpoints for V1
export const API_ENDPOINTS = {
  POPULAR_TEAMS: '/api/football/teams/popular',
  POPULAR_PLAYERS: '/api/football/players/popular',
  APP_INFO: '/api/app/download-info',
  SOCIAL_STATS: '/api/social/stats',
  COMPANY_INFO: '/api/company/info'
} as const;

// Error messages
export const ERROR_MESSAGES = {
  LOAD_FAILED: 'Failed to load footer content',
  TEAMS_UNAVAILABLE: 'Popular teams are currently unavailable',
  PLAYERS_UNAVAILABLE: 'Popular players are currently unavailable',
  APP_INFO_ERROR: 'App download information unavailable',
  NETWORK_ERROR: 'Network connection error'
} as const;

// Success messages
export const SUCCESS_MESSAGES = {
  DATA_LOADED: 'Footer content loaded successfully',
  TEAMS_LOADED: 'Popular teams loaded',
  PLAYERS_LOADED: 'Popular players loaded',
  APP_INFO_LOADED: 'App information loaded'
} as const;
