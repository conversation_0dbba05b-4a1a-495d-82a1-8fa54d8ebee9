import * as React from 'react';
import Link from 'next/link';
import type { LeaguesSectionProps, League } from '../types';

const LeagueCard: React.FC<{ league: League }> = ({ league }: { league: League }) => {
        const statusColors = {
                active: 'bg-green-100 text-green-800',
                finished: 'bg-gray-100 text-gray-800',
                upcoming: 'bg-blue-100 text-blue-800'
        };

        const progressPercentage = league.currentMatchday && league.totalMatchdays
                ? (league.currentMatchday / league.totalMatchdays) * 100
                : 0;

        return (
                <Link href={`/league/${league.id}`} className="group">
                        <div className="bg-white rounded-xl shadow-sm hover:shadow-lg transition-all duration-300 overflow-hidden border border-gray-100 group-hover:border-blue-200">
                                {/* League Header */}
                                <div className="p-6 border-b border-gray-50">
                                        <div className="flex items-center space-x-4">
                                                {league.logo && (
                                                        <div className="relative">
                                                                <img
                                                                        src={league.logo}
                                                                        alt={`${league.name} logo`}
                                                                        className="w-12 h-12 object-contain rounded-lg"
                                                                />
                                                        </div>
                                                )}
                                                <div className="flex-1 min-w-0">
                                                        <h3 className="text-lg font-bold text-gray-900 group-hover:text-blue-600 transition-colors duration-200 truncate">
                                                                {league.name}
                                                        </h3>
                                                        <div className="flex items-center space-x-2 mt-1">
                                                                <span className="text-sm text-gray-500">{league.country}</span>
                                                                <span className="w-1 h-1 bg-gray-300 rounded-full"></span>
                                                                <span className="text-sm text-gray-500">{league.season}</span>
                                                        </div>
                                                </div>
                                                <span className={`px-2 py-1 rounded-full text-xs font-medium ${statusColors[league.status]}`}>
                                                        {league.status.charAt(0).toUpperCase() + league.status.slice(1)}
                                                </span>
                                        </div>
                                </div>

                                {/* League Stats */}
                                <div className="p-6 space-y-4">
                                        {league.description && (
                                                <p className="text-gray-600 text-sm line-clamp-2">
                                                        {league.description}
                                                </p>
                                        )}

                                        {/* Progress Bar for Active Leagues */}
                                        {league.status === 'active' && league.currentMatchday && league.totalMatchdays && (
                                                <div className="space-y-2">
                                                        <div className="flex justify-between text-sm">
                                                                <span className="text-gray-600">Season Progress</span>
                                                                <span className="font-medium text-gray-900">
                                                                        Matchday {league.currentMatchday} of {league.totalMatchdays}
                                                                </span>
                                                        </div>
                                                        <div className="w-full bg-gray-200 rounded-full h-2">
                                                                <div
                                                                        className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                                                                        style={{ width: `${progressPercentage}%` }}
                                                                ></div>
                                                        </div>
                                                </div>
                                        )}

                                        {/* League Info Grid */}
                                        <div className="grid grid-cols-2 gap-4 pt-2">
                                                <div className="text-center p-3 bg-gray-50 rounded-lg">
                                                        <div className="text-2xl font-bold text-gray-900">{league.teams}</div>
                                                        <div className="text-xs text-gray-500 mt-1">Teams</div>
                                                </div>
                                                {league.status === 'active' && league.currentMatchday && (
                                                        <div className="text-center p-3 bg-blue-50 rounded-lg">
                                                                <div className="text-2xl font-bold text-blue-600">{league.currentMatchday}</div>
                                                                <div className="text-xs text-blue-600 mt-1">Current MD</div>
                                                        </div>
                                                )}
                                                {league.status === 'finished' && (
                                                        <div className="text-center p-3 bg-green-50 rounded-lg">
                                                                <div className="text-2xl font-bold text-green-600">✓</div>
                                                                <div className="text-xs text-green-600 mt-1">Complete</div>
                                                        </div>
                                                )}
                                                {league.status === 'upcoming' && (
                                                        <div className="text-center p-3 bg-gray-50 rounded-lg">
                                                                <div className="text-2xl font-bold text-gray-600">📅</div>
                                                                <div className="text-xs text-gray-600 mt-1">Soon</div>
                                                        </div>
                                                )}
                                        </div>

                                        {/* Action Button */}
                                        <div className="pt-2">
                                                <div className="w-full bg-blue-600 text-white text-center py-2 rounded-lg font-medium text-sm group-hover:bg-blue-700 transition-colors duration-200">
                                                        View League Details
                                                </div>
                                        </div>
                                </div>
                        </div>
                </Link>
        );
};

export const LeaguesSection: React.FC<LeaguesSectionProps> = ({
        leagues,
        title = "Popular Leagues",
        showViewAll = true,
        maxItems = 6,
        className = ""
}: LeaguesSectionProps) => {
        const displayLeagues: League[] = leagues.slice(0, maxItems);
        const activeLeagues: League[] = displayLeagues.filter((league: League) => league.status === 'active');
        const otherLeagues: League[] = displayLeagues.filter((league: League) => league.status !== 'active');

        return (
                <section className={`space-y-8 ${className}`}>
                        {/* Section Header */}
                        <div className="flex items-center justify-between">
                                <div>
                                        <h2 className="text-3xl font-bold text-gray-900 mb-2">{title}</h2>
                                        <p className="text-gray-600">Follow your favorite leagues and competitions</p>
                                </div>
                                {showViewAll && leagues.length > maxItems && (
                                        <Link
                                                href="/leagues"
                                                className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200 font-medium"
                                        >
                                                View All Leagues
                                                <svg className="w-4 h-4 ml-2" fill="currentColor" viewBox="0 0 20 20">
                                                        <path fillRule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clipRule="evenodd" />
                                                </svg>
                                        </Link>
                                )}
                        </div>

                        {/* Empty State */}
                        {leagues.length === 0 && (
                                <div className="text-center py-12">
                                        <div className="inline-flex items-center justify-center w-16 h-16 bg-gray-100 rounded-full mb-4">
                                                <svg className="w-8 h-8 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                                        <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                                </svg>
                                        </div>
                                        <h3 className="text-lg font-medium text-gray-900 mb-2">No leagues available</h3>
                                        <p className="text-gray-500">League information will be displayed here when available.</p>
                                </div>
                        )}

                        {/* Active Leagues */}
                        {activeLeagues.length > 0 && (
                                <div className="space-y-6">
                                        <h3 className="text-xl font-semibold text-gray-900 flex items-center">
                                                <span className="w-2 h-2 bg-green-600 rounded-full mr-3"></span>
                                                Active Competitions
                                        </h3>
                                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                                                {activeLeagues.map((league: League) => (
                                                        <LeagueCard key={league.id} league={league} />
                                                ))}
                                        </div>
                                </div>
                        )}

                        {/* Other Leagues */}
                        {otherLeagues.length > 0 && (
                                <div className="space-y-6">
                                        {activeLeagues.length > 0 && (
                                                <h3 className="text-xl font-semibold text-gray-900 flex items-center">
                                                        <span className="w-2 h-2 bg-gray-400 rounded-full mr-3"></span>
                                                        Other Competitions
                                                </h3>
                                        )}
                                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                                                {otherLeagues.map((league: League) => (
                                                        <LeagueCard key={league.id} league={league} />
                                                ))}
                                        </div>
                                </div>
                        )}
                </section>
        );
};
