'use client';

import React, { useState, useEffect } from 'react';
import { StatisticV2, StatisticCategory, TrendAnalysis } from '../types';

interface StatisticsWidgetV2Props {
  statistics: StatisticV2[];
  isLoading?: boolean;
  realTimeEnabled?: boolean;
  onStatisticClick?: (statistic: StatisticV2) => void;
}

export const StatisticsWidgetV2: React.FC<StatisticsWidgetV2Props> = ({
  statistics,
  isLoading = false,
  realTimeEnabled = true,
  onStatisticClick
}) => {
  const [selectedCategory, setSelectedCategory] = useState<StatisticCategory | 'all'>('all');
  const [animatingStats, setAnimatingStats] = useState<Set<string>>(new Set());
  const [hoveredStat, setHoveredStat] = useState<string | null>(null);

  // Simulate real-time updates
  useEffect(() => {
    if (!realTimeEnabled) return;

    const interval = setInterval(() => {
      const randomStat = statistics[Math.floor(Math.random() * statistics.length)];
      if (randomStat) {
        setAnimatingStats(prev => new Set([...prev, randomStat.id]));
        setTimeout(() => {
          setAnimatingStats(prev => {
            const newSet = new Set(prev);
            newSet.delete(randomStat.id);
            return newSet;
          });
        }, 1000);
      }
    }, 5000);

    return () => clearInterval(interval);
  }, [statistics, realTimeEnabled]);

  // Format values with enhanced display
  const formatValue = (value: string | number, unit?: string) => {
    if (typeof value === 'number') {
      if (value >= 1000000) return `${(value / 1000000).toFixed(1)}M`;
      if (value >= 1000) return `${(value / 1000).toFixed(1)}K`;
      return value.toLocaleString();
    }
    return value;
  };

  // Get enhanced trend icon with animation
  const getTrendIcon = (trend: TrendAnalysis) => {
    const baseClasses = "w-4 h-4 transition-all duration-300";
    
    switch (trend.direction) {
      case 'up':
        return (
          <svg className={`${baseClasses} text-green-500 ${trend.significance === 'major' ? 'animate-bounce' : ''}`} 
               fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M5.293 9.707a1 1 0 010-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 01-1.414 1.414L11 7.414V15a1 1 0 11-2 0V7.414L6.707 9.707a1 1 0 01-1.414 0z" clipRule="evenodd" />
          </svg>
        );
      case 'down':
        return (
          <svg className={`${baseClasses} text-red-500 ${trend.significance === 'major' ? 'animate-bounce' : ''}`} 
               fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M14.707 10.293a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 111.414-1.414L9 12.586V5a1 1 0 012 0v7.586l2.293-2.293a1 1 0 011.414 0z" clipRule="evenodd" />
          </svg>
        );
      default:
        return (
          <svg className={`${baseClasses} text-gray-400`} fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M3 10a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clipRule="evenodd" />
          </svg>
        );
    }
  };

  // Get enhanced category icon and style
  const getCategoryStyle = (category: StatisticCategory) => {
    const styles = {
      'goals': { icon: '⚽', color: '#10B981', bg: '#ECFDF5', gradient: 'from-green-500 to-emerald-500' },
      'matches': { icon: '🏟️', color: '#3B82F6', bg: '#EFF6FF', gradient: 'from-blue-500 to-cyan-500' },
      'players': { icon: '👤', color: '#8B5CF6', bg: '#F3E8FF', gradient: 'from-purple-500 to-violet-500' },
      'leagues': { icon: '🏆', color: '#F59E0B', bg: '#FFFBEB', gradient: 'from-yellow-500 to-orange-500' },
      'engagement': { icon: '📱', color: '#EF4444', bg: '#FEF2F2', gradient: 'from-red-500 to-pink-500' },
      'performance': { icon: '📊', color: '#06B6D4', bg: '#ECFEFF', gradient: 'from-cyan-500 to-teal-500' },
      'betting': { icon: '💰', color: '#84CC16', bg: '#F7FEE7', gradient: 'from-lime-500 to-green-500' },
      'social': { icon: '🌐', color: '#6366F1', bg: '#EEF2FF', gradient: 'from-indigo-500 to-blue-500' }
    };
    return styles[category] || styles.goals;
  };

  // Filter statistics
  const filteredStatistics = statistics.filter(stat => 
    selectedCategory === 'all' || stat.category === selectedCategory
  );

  // Get unique categories
  const categories = Array.from(new Set(statistics.map(stat => stat.category)));

  // Loading state
  if (isLoading) {
    return (
      <div className="bg-white/80 backdrop-blur-sm rounded-2xl border border-gray-200/50 p-6 shadow-lg">
        <div className="flex items-center space-x-3 mb-6">
          <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-500 rounded-xl animate-pulse"></div>
          <div className="h-6 bg-gray-200 rounded-lg w-40 animate-pulse"></div>
        </div>
        <div className="grid grid-cols-2 gap-4">
          {[1, 2, 3, 4].map((i) => (
            <div key={i} className="p-4 bg-gray-50 rounded-xl animate-pulse">
              <div className="h-4 bg-gray-200 rounded w-16 mb-2"></div>
              <div className="h-8 bg-gray-200 rounded w-20 mb-1"></div>
              <div className="h-3 bg-gray-200 rounded w-24"></div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white/80 backdrop-blur-sm rounded-2xl border border-gray-200/50 p-6 shadow-lg hover:shadow-xl transition-all duration-300">
      {/* Enhanced Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-500 rounded-xl flex items-center justify-center shadow-lg">
            <span className="text-white text-xl">📊</span>
          </div>
          <div>
            <h3 className="text-xl font-bold text-gray-900">Live Statistics</h3>
            <p className="text-sm text-gray-600">Real-time football analytics</p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          {realTimeEnabled && (
            <div className="flex items-center space-x-1">
              <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
              <span className="text-xs text-gray-500">Live</span>
            </div>
          )}
        </div>
      </div>

      {/* Category Filters */}
      <div className="flex flex-wrap gap-2 mb-6">
        <button
          onClick={() => setSelectedCategory('all')}
          className={`px-3 py-1.5 rounded-full text-xs font-medium transition-all duration-200 ${
            selectedCategory === 'all'
              ? 'bg-blue-500 text-white shadow-md'
              : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
          }`}
        >
          All
        </button>
        {categories.map((category) => {
          const style = getCategoryStyle(category);
          return (
            <button
              key={category}
              onClick={() => setSelectedCategory(category)}
              className={`px-3 py-1.5 rounded-full text-xs font-medium transition-all duration-200 flex items-center space-x-1 ${
                selectedCategory === category
                  ? 'text-white shadow-md'
                  : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
              }`}
              style={selectedCategory === category ? { backgroundColor: style.color } : {}}
            >
              <span>{style.icon}</span>
              <span>{category.charAt(0).toUpperCase() + category.slice(1)}</span>
            </button>
          );
        })}
      </div>

      {/* Enhanced Statistics Grid */}
      <div className="grid grid-cols-2 gap-4 mb-6">
        {filteredStatistics.slice(0, 4).map((stat) => {
          const categoryStyle = getCategoryStyle(stat.category);
          const isAnimating = animatingStats.has(stat.id);
          const isHovered = hoveredStat === stat.id;
          
          return (
            <div
              key={stat.id}
              onClick={() => onStatisticClick?.(stat)}
              onMouseEnter={() => setHoveredStat(stat.id)}
              onMouseLeave={() => setHoveredStat(null)}
              className={`group p-4 rounded-xl border transition-all duration-300 cursor-pointer ${
                isAnimating ? 'animate-pulse border-blue-300 shadow-lg' : 'border-gray-100 hover:border-gray-200'
              } ${isHovered ? 'scale-105 shadow-lg' : ''}`}
              style={{ backgroundColor: `${categoryStyle.color}08` }}
            >
              {/* Header with Icon and Trend */}
              <div className="flex items-center justify-between mb-3">
                <div className={`w-8 h-8 rounded-lg flex items-center justify-center bg-gradient-to-r ${categoryStyle.gradient} shadow-md`}>
                  <span className="text-white text-sm">{categoryStyle.icon}</span>
                </div>
                {stat.trend && (
                  <div className="flex items-center space-x-1">
                    {getTrendIcon(stat.trend)}
                    <span className={`text-xs font-bold ${
                      stat.trend.direction === 'up' ? 'text-green-600' :
                      stat.trend.direction === 'down' ? 'text-red-600' : 'text-gray-500'
                    }`}>
                      {stat.trend.percentage}%
                    </span>
                  </div>
                )}
              </div>

              {/* Value and Title */}
              <div className="mb-2">
                <div className={`text-2xl font-bold text-gray-900 transition-all duration-300 ${
                  isAnimating ? 'scale-110' : ''
                }`}>
                  {formatValue(stat.value, stat.unit)}
                  {stat.unit && <span className="text-sm text-gray-500 ml-1">{stat.unit}</span>}
                </div>
                <div className="text-sm font-medium text-gray-700 group-hover:text-gray-900 transition-colors">
                  {stat.title}
                </div>
              </div>

              {/* Subtitle and Real-time Indicator */}
              <div className="text-xs text-gray-500 space-y-1">
                {stat.subtitle && <div>{stat.subtitle}</div>}
                {stat.trend && (
                  <div className="flex items-center space-x-1">
                    <span>{stat.trend.period}</span>
                    {stat.trend.confidence === 'high' && (
                      <span className="w-1 h-1 bg-green-500 rounded-full"></span>
                    )}
                  </div>
                )}
                {stat.isRealTime && (
                  <div className="flex items-center space-x-1 text-green-600">
                    <div className="w-1 h-1 bg-green-500 rounded-full animate-pulse"></div>
                    <span>Live</span>
                  </div>
                )}
              </div>

              {/* Comparison Data (on hover) */}
              {isHovered && stat.comparison && (
                <div className="mt-3 pt-3 border-t border-gray-200 text-xs">
                  <div className="flex items-center justify-between">
                    <span className="text-gray-600">{stat.comparison.period}:</span>
                    <div className="flex items-center space-x-1">
                      <span className="font-medium">{formatValue(stat.comparison.value)}</span>
                      <span className={`${
                        stat.comparison.changeType === 'increase' ? 'text-green-600' :
                        stat.comparison.changeType === 'decrease' ? 'text-red-600' : 'text-gray-500'
                      }`}>
                        ({stat.comparison.changeType === 'increase' ? '+' : stat.comparison.changeType === 'decrease' ? '-' : ''}
                        {Math.abs(stat.comparison.change)}%)
                      </span>
                    </div>
                  </div>
                </div>
              )}

              {/* Interactive Indicator */}
              {stat.interactive && (
                <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
                  <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                </div>
              )}
            </div>
          );
        })}
      </div>

      {/* Featured Statistics */}
      {filteredStatistics.length > 4 && (
        <div className="mb-6">
          <h4 className="text-sm font-semibold text-gray-700 mb-3 flex items-center space-x-2">
            <span>🔥</span>
            <span>Trending Stats</span>
          </h4>
          <div className="grid grid-cols-1 gap-3">
            {filteredStatistics.slice(4, 6).map((stat) => {
              const categoryStyle = getCategoryStyle(stat.category);
              
              return (
                <div
                  key={stat.id}
                  onClick={() => onStatisticClick?.(stat)}
                  className="group p-4 bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl border border-blue-100 hover:border-blue-200 cursor-pointer transition-all duration-300"
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className={`w-10 h-10 rounded-xl flex items-center justify-center bg-gradient-to-r ${categoryStyle.gradient} shadow-md`}>
                        <span className="text-white">{categoryStyle.icon}</span>
                      </div>
                      <div>
                        <div className="text-lg font-bold text-gray-900">
                          {formatValue(stat.value, stat.unit)}
                        </div>
                        <div className="text-sm text-gray-700">{stat.title}</div>
                        <div className="text-xs text-gray-500">{stat.subtitle}</div>
                      </div>
                    </div>
                    {stat.trend && (
                      <div className="text-right">
                        <div className="flex items-center space-x-1 justify-end">
                          {getTrendIcon(stat.trend)}
                          <span className={`text-sm font-bold ${
                            stat.trend.direction === 'up' ? 'text-green-600' :
                            stat.trend.direction === 'down' ? 'text-red-600' : 'text-gray-500'
                          }`}>
                            {stat.trend.percentage}%
                          </span>
                        </div>
                        <div className="text-xs text-gray-500 mt-1">{stat.trend.period}</div>
                      </div>
                    )}
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      )}

      {/* Enhanced Action Buttons */}
      <div className="flex space-x-3">
        <button className="flex-1 text-center text-sm font-medium text-blue-600 hover:text-blue-700 py-3 hover:bg-blue-50 rounded-xl transition-all duration-200 flex items-center justify-center space-x-2">
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
          </svg>
          <span>View Analytics</span>
        </button>
        <button className="flex-1 text-center text-sm font-medium text-gray-600 hover:text-gray-700 py-3 hover:bg-gray-50 rounded-xl transition-all duration-200 flex items-center justify-center space-x-2">
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z" />
          </svg>
          <span>Share</span>
        </button>
      </div>
    </div>
  );
};
