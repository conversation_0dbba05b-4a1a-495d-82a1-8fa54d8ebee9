'use client';

import React, { useState, useRef, useEffect } from 'react';
import { MatchHighlightV2, VideoPlayerState, FilterOptions } from '../types';

interface MatchHighlightsV2Props {
  highlights: MatchHighlightV2[];
  isLoading?: boolean;
  onVideoPlay?: (highlight: MatchHighlightV2) => void;
  filters?: FilterOptions;
  onFiltersChange?: (filters: FilterOptions) => void;
}

export const MatchHighlightsV2: React.FC<MatchHighlightsV2Props> = ({
  highlights,
  isLoading = false,
  onVideoPlay,
  filters,
  onFiltersChange
}) => {
  const [selectedHighlight, setSelectedHighlight] = useState<MatchHighlightV2 | null>(null);
  const [hoveredHighlight, setHoveredHighlight] = useState<string | null>(null);
  const [playerState, setPlayerState] = useState<VideoPlayerState>({
    isPlaying: false,
    currentTime: 0,
    duration: 0,
    volume: 1,
    isMuted: false,
    isFullscreen: false,
    quality: 'HD',
    playbackSpeed: 1
  });
  const [activeFilter, setActiveFilter] = useState<string>('all');
  const videoRef = useRef<HTMLVideoElement>(null);

  // Format views with enhanced display
  const formatViews = (views: number) => {
    if (views >= 1000000) return `${(views / 1000000).toFixed(1)}M`;
    if (views >= 1000) return `${(views / 1000).toFixed(1)}K`;
    return views.toString();
  };

  // Format engagement metrics
  const formatEngagement = (engagement: { likes: number; shares: number; comments: number }) => {
    const total = engagement.likes + engagement.shares + engagement.comments;
    if (total >= 1000000) return `${(total / 1000000).toFixed(1)}M`;
    if (total >= 1000) return `${(total / 1000).toFixed(1)}K`;
    return total.toString();
  };

  // Get category color and icon
  const getCategoryStyle = (category: string) => {
    const styles = {
      'goal': { color: '#10B981', bg: '#ECFDF5', icon: '⚽' },
      'save': { color: '#3B82F6', bg: '#EFF6FF', icon: '🥅' },
      'skill': { color: '#8B5CF6', bg: '#F3E8FF', icon: '✨' },
      'foul': { color: '#EF4444', bg: '#FEF2F2', icon: '⚠️' },
      'celebration': { color: '#F59E0B', bg: '#FFFBEB', icon: '🎉' },
      'controversy': { color: '#6B7280', bg: '#F9FAFB', icon: '🤔' }
    };
    return styles[category as keyof typeof styles] || styles.goal;
  };

  // Get importance badge
  const getImportanceBadge = (importance: string) => {
    const badges = {
      'high': { text: 'HOT', color: '#EF4444', bg: '#FEF2F2' },
      'medium': { text: 'TRENDING', color: '#F59E0B', bg: '#FFFBEB' },
      'low': { text: 'NEW', color: '#10B981', bg: '#ECFDF5' }
    };
    return badges[importance as keyof typeof badges] || badges.low;
  };

  // Filter highlights
  const filteredHighlights = highlights.filter(highlight => {
    if (activeFilter === 'all') return true;
    return highlight.category === activeFilter;
  });

  // Handle video modal
  const openVideoModal = (highlight: MatchHighlightV2) => {
    setSelectedHighlight(highlight);
    onVideoPlay?.(highlight);
  };

  const closeVideoModal = () => {
    setSelectedHighlight(null);
    setPlayerState(prev => ({ ...prev, isPlaying: false }));
  };

  // Loading state
  if (isLoading) {
    return (
      <div className="bg-white/80 backdrop-blur-sm rounded-2xl border border-gray-200/50 p-6 shadow-lg">
        <div className="flex items-center space-x-3 mb-6">
          <div className="w-10 h-10 bg-gradient-to-r from-red-500 to-pink-500 rounded-xl animate-pulse"></div>
          <div className="h-6 bg-gray-200 rounded-lg w-40 animate-pulse"></div>
        </div>
        <div className="space-y-4">
          {[1, 2, 3, 4].map((i) => (
            <div key={i} className="flex space-x-4 p-4 bg-gray-50 rounded-xl animate-pulse">
              <div className="w-28 h-20 bg-gray-200 rounded-lg"></div>
              <div className="flex-1 space-y-2">
                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                <div className="flex space-x-2">
                  <div className="h-6 bg-gray-200 rounded-full w-16"></div>
                  <div className="h-6 bg-gray-200 rounded-full w-20"></div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white/80 backdrop-blur-sm rounded-2xl border border-gray-200/50 p-6 shadow-lg hover:shadow-xl transition-all duration-300">
      {/* Enhanced Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-gradient-to-r from-red-500 to-pink-500 rounded-xl flex items-center justify-center shadow-lg">
            <span className="text-white text-xl">🎬</span>
          </div>
          <div>
            <h3 className="text-xl font-bold text-gray-900">Match Highlights</h3>
            <p className="text-sm text-gray-600">Latest video highlights & moments</p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <span className="text-xs text-gray-500">{filteredHighlights.length} videos</span>
          <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
        </div>
      </div>

      {/* Category Filters */}
      <div className="flex flex-wrap gap-2 mb-6">
        {['all', 'goal', 'save', 'skill', 'foul', 'celebration'].map((category) => (
          <button
            key={category}
            onClick={() => setActiveFilter(category)}
            className={`px-3 py-1.5 rounded-full text-xs font-medium transition-all duration-200 ${
              activeFilter === category
                ? 'bg-blue-500 text-white shadow-md'
                : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
            }`}
          >
            {category === 'all' ? 'All' : category.charAt(0).toUpperCase() + category.slice(1)}
          </button>
        ))}
      </div>

      {/* Highlights List */}
      <div className="space-y-4 max-h-96 overflow-y-auto custom-scrollbar">
        {filteredHighlights.slice(0, 6).map((highlight) => {
          const categoryStyle = getCategoryStyle(highlight.category);
          const importanceBadge = getImportanceBadge(highlight.importance);
          
          return (
            <div
              key={highlight.id}
              onClick={() => openVideoModal(highlight)}
              onMouseEnter={() => setHoveredHighlight(highlight.id)}
              onMouseLeave={() => setHoveredHighlight(null)}
              className="group flex space-x-4 p-4 rounded-xl hover:bg-gradient-to-r hover:from-blue-50 hover:to-purple-50 cursor-pointer transition-all duration-300 border border-transparent hover:border-blue-200"
            >
              {/* Enhanced Thumbnail */}
              <div className="relative w-28 h-20 bg-gray-100 rounded-lg overflow-hidden flex-shrink-0 shadow-md">
                <img
                  src={highlight.thumbnailUrl}
                  alt={highlight.title}
                  className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                  onError={(e) => {
                    const target = e.target as HTMLImageElement;
                    target.src = `data:image/svg+xml,${encodeURIComponent(`
                      <svg width="112" height="80" xmlns="http://www.w3.org/2000/svg">
                        <rect width="100%" height="100%" fill="${categoryStyle.bg}"/>
                        <text x="50%" y="50%" text-anchor="middle" dy=".3em" font-size="24">${categoryStyle.icon}</text>
                      </svg>
                    `)}`;
                  }}
                />
                
                {/* Play Overlay */}
                <div className="absolute inset-0 bg-black/20 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                  <div className="w-8 h-8 bg-white/90 rounded-full flex items-center justify-center">
                    <svg className="w-4 h-4 text-gray-800 ml-0.5" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M6.3 2.841A1.5 1.5 0 004 4.11V15.89a1.5 1.5 0 002.3 1.269l9.344-5.89a1.5 1.5 0 000-2.538L6.3 2.84z"/>
                    </svg>
                  </div>
                </div>

                {/* Duration Badge */}
                <div className="absolute bottom-2 right-2 bg-black/70 text-white text-xs px-2 py-1 rounded">
                  {highlight.duration}
                </div>

                {/* Quality Badge */}
                <div className="absolute top-2 left-2 bg-blue-500 text-white text-xs px-2 py-0.5 rounded">
                  {highlight.quality}
                </div>

                {/* Live Indicator */}
                {highlight.isLive && (
                  <div className="absolute top-2 right-2 bg-red-500 text-white text-xs px-2 py-0.5 rounded flex items-center space-x-1">
                    <div className="w-1.5 h-1.5 bg-white rounded-full animate-pulse"></div>
                    <span>LIVE</span>
                  </div>
                )}
              </div>

              {/* Enhanced Content */}
              <div className="flex-1 min-w-0">
                <div className="flex items-start justify-between mb-2">
                  <h4 className="font-semibold text-gray-900 text-sm line-clamp-2 group-hover:text-blue-600 transition-colors">
                    {highlight.title}
                  </h4>
                  {highlight.importance === 'high' && (
                    <div 
                      className="px-2 py-0.5 rounded-full text-xs font-bold ml-2 flex-shrink-0"
                      style={{ 
                        color: importanceBadge.color, 
                        backgroundColor: importanceBadge.bg 
                      }}
                    >
                      {importanceBadge.text}
                    </div>
                  )}
                </div>

                {/* Match Info */}
                <div className="flex items-center space-x-2 mb-2">
                  <div className="flex items-center space-x-1">
                    <img 
                      src={highlight.matchInfo.homeTeamLogo} 
                      alt={highlight.matchInfo.homeTeam}
                      className="w-4 h-4 rounded"
                      onError={(e) => {
                        const target = e.target as HTMLImageElement;
                        target.style.display = 'none';
                      }}
                    />
                    <span className="text-xs text-gray-600">{highlight.matchInfo.homeTeam}</span>
                  </div>
                  <span className="text-xs text-gray-400">vs</span>
                  <div className="flex items-center space-x-1">
                    <img 
                      src={highlight.matchInfo.awayTeamLogo} 
                      alt={highlight.matchInfo.awayTeam}
                      className="w-4 h-4 rounded"
                      onError={(e) => {
                        const target = e.target as HTMLImageElement;
                        target.style.display = 'none';
                      }}
                    />
                    <span className="text-xs text-gray-600">{highlight.matchInfo.awayTeam}</span>
                  </div>
                  {highlight.matchInfo.score && (
                    <span className="text-xs font-medium text-gray-700">({highlight.matchInfo.score})</span>
                  )}
                </div>

                {/* Engagement Metrics */}
                <div className="flex items-center space-x-4 mb-2">
                  <span className="text-xs text-gray-500 flex items-center space-x-1">
                    <span>👁️</span>
                    <span>{formatViews(highlight.views)}</span>
                  </span>
                  <span className="text-xs text-gray-500 flex items-center space-x-1">
                    <span>💬</span>
                    <span>{formatEngagement(highlight.engagement)}</span>
                  </span>
                  <span className="text-xs text-gray-500">
                    {new Date(highlight.createdAt).toLocaleDateString()}
                  </span>
                </div>

                {/* Tags */}
                <div className="flex flex-wrap gap-1">
                  <span
                    className="text-xs px-2 py-0.5 rounded-full font-medium"
                    style={{ 
                      color: categoryStyle.color, 
                      backgroundColor: categoryStyle.bg 
                    }}
                  >
                    {categoryStyle.icon} {highlight.category}
                  </span>
                  {highlight.tags.slice(0, 2).map((tag) => (
                    <span
                      key={tag}
                      className="text-xs px-2 py-0.5 rounded-full bg-gray-100 text-gray-600"
                    >
                      {tag}
                    </span>
                  ))}
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {/* View More Button */}
      <div className="mt-6 pt-4 border-t border-gray-100">
        <button className="w-full text-center text-sm font-medium text-blue-600 hover:text-blue-700 py-3 hover:bg-blue-50 rounded-xl transition-all duration-200 flex items-center justify-center space-x-2">
          <span>View All Highlights</span>
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
          </svg>
        </button>
      </div>

      {/* Enhanced Video Modal */}
      {selectedHighlight && (
        <div className="fixed inset-0 bg-black/70 backdrop-blur-sm flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-2xl max-w-4xl w-full max-h-[90vh] overflow-hidden shadow-2xl">
            {/* Modal Header */}
            <div className="flex justify-between items-center p-6 border-b border-gray-200">
              <div>
                <h3 className="text-xl font-bold text-gray-900">{selectedHighlight.title}</h3>
                <p className="text-sm text-gray-600 mt-1">{selectedHighlight.description}</p>
              </div>
              <button
                onClick={closeVideoModal}
                className="text-gray-400 hover:text-gray-600 p-2 hover:bg-gray-100 rounded-lg transition-colors"
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

            {/* Video Player */}
            <div className="aspect-video bg-gray-900 relative">
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="text-white text-center">
                  <div className="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center mb-4 mx-auto">
                    <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M6.3 2.841A1.5 1.5 0 004 4.11V15.89a1.5 1.5 0 002.3 1.269l9.344-5.89a1.5 1.5 0 000-2.538L6.3 2.84z"/>
                    </svg>
                  </div>
                  <p className="text-lg font-medium">Video Player</p>
                  <p className="text-sm text-gray-300">Click to play highlight</p>
                </div>
              </div>
            </div>

            {/* Video Controls & Info */}
            <div className="p-6">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center space-x-4">
                  <span className="text-sm text-gray-600">👁️ {formatViews(selectedHighlight.views)} views</span>
                  <span className="text-sm text-gray-600">💬 {formatEngagement(selectedHighlight.engagement)}</span>
                  <span className="text-sm text-gray-600">⏱️ {selectedHighlight.duration}</span>
                </div>
                <div className="flex items-center space-x-2">
                  <button className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors text-sm">
                    Share
                  </button>
                  <button className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors text-sm">
                    Download
                  </button>
                </div>
              </div>

              {/* Match Details */}
              <div className="bg-gray-50 rounded-lg p-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <div className="flex items-center space-x-2">
                      <img 
                        src={selectedHighlight.matchInfo.homeTeamLogo} 
                        alt={selectedHighlight.matchInfo.homeTeam}
                        className="w-8 h-8 rounded"
                      />
                      <span className="font-medium">{selectedHighlight.matchInfo.homeTeam}</span>
                    </div>
                    <span className="text-gray-500">vs</span>
                    <div className="flex items-center space-x-2">
                      <img 
                        src={selectedHighlight.matchInfo.awayTeamLogo} 
                        alt={selectedHighlight.matchInfo.awayTeam}
                        className="w-8 h-8 rounded"
                      />
                      <span className="font-medium">{selectedHighlight.matchInfo.awayTeam}</span>
                    </div>
                  </div>
                  {selectedHighlight.matchInfo.score && (
                    <div className="text-xl font-bold text-gray-900">
                      {selectedHighlight.matchInfo.score}
                    </div>
                  )}
                </div>
                <div className="mt-2 text-sm text-gray-600">
                  {selectedHighlight.matchInfo.league} • {new Date(selectedHighlight.matchInfo.date).toLocaleDateString()}
                  {selectedHighlight.matchInfo.venue && ` • ${selectedHighlight.matchInfo.venue}`}
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Custom Scrollbar Styles */}
      <style jsx>{`
        .custom-scrollbar::-webkit-scrollbar {
          width: 4px;
        }
        .custom-scrollbar::-webkit-scrollbar-track {
          background: #f1f5f9;
          border-radius: 2px;
        }
        .custom-scrollbar::-webkit-scrollbar-thumb {
          background: #cbd5e1;
          border-radius: 2px;
        }
        .custom-scrollbar::-webkit-scrollbar-thumb:hover {
          background: #94a3b8;
        }
        .line-clamp-2 {
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          overflow: hidden;
        }
      `}</style>
    </div>
  );
};
