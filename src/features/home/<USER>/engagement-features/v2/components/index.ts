// Engagement Features V2 Components - Individual Exports
// This file exports all individual components for V2

export { MatchHighlightsV2 } from './MatchHighlightsV2';
export { StatisticsWidgetV2 } from './StatisticsWidgetV2';

// Component registry for dynamic imports
export const COMPONENTS_V2 = {
  MatchHighlightsV2: () => import('./MatchHighlightsV2').then(m => m.MatchHighlightsV2),
  StatisticsWidgetV2: () => import('./StatisticsWidgetV2').then(m => m.StatisticsWidgetV2),
} as const;

// Component metadata for V2
export const V2_COMPONENT_INFO = {
  MatchHighlightsV2: {
    name: 'Match Highlights V2',
    description: 'Enhanced video highlights with advanced player, filtering, and real-time features',
    features: [
      'Enhanced video player with preview',
      'Advanced filtering by category',
      'Real-time engagement metrics',
      'Interactive video modal',
      'CDN image integration',
      'Quality indicators (HD/4K)',
      'Live streaming support',
      'Social sharing capabilities'
    ],
    improvements: [
      'Glassmorphism design',
      'Micro-animations',
      'Better mobile experience',
      'Enhanced thumbnails',
      'Category-based filtering',
      'Importance badges'
    ]
  },
  StatisticsWidgetV2: {
    name: 'Statistics Widget V2',
    description: 'Interactive statistics with real-time updates, advanced analytics, and drill-down capabilities',
    features: [
      'Real-time data updates',
      'Interactive trend analysis',
      'Category-based filtering',
      'Comparative analytics',
      'Drill-down navigation',
      'Historical data visualization',
      'Confidence indicators',
      'Multi-category support'
    ],
    improvements: [
      'Enhanced visual hierarchy',
      'Animated trend indicators',
      'Better data formatting',
      'Interactive hover states',
      'Real-time pulse animations',
      'Advanced comparison metrics'
    ]
  }
} as const;

// Default props for V2 components
export const DEFAULT_PROPS_V2 = {
  MatchHighlightsV2: {
    highlights: [],
    isLoading: false,
    onVideoPlay: undefined,
    filters: undefined,
    onFiltersChange: undefined
  },
  StatisticsWidgetV2: {
    statistics: [],
    isLoading: false,
    realTimeEnabled: true,
    onStatisticClick: undefined
  }
} as const;

// Performance metrics for V2
export const PERFORMANCE_METRICS_V2 = {
  targetLoadTime: 800, // ms
  maxMemoryUsage: 50, // MB
  animationFrameRate: 60, // fps
  cacheStrategy: 'stale-while-revalidate',
  prefetchEnabled: true
} as const;
