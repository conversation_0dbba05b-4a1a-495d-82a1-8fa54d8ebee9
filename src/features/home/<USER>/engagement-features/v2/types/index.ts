// Engagement Features V2 Types - Enhanced with advanced features

// Enhanced Match Highlight with video player capabilities
export interface MatchHighlightV2 {
  id: string;
  title: string;
  description: string;
  thumbnailUrl: string;
  videoUrl: string;
  duration: string; // "2:30"
  views: number;
  matchInfo: {
    homeTeam: string;
    awayTeam: string;
    homeTeamLogo: string;
    awayTeamLogo: string;
    date: string;
    score?: string;
    league: string;
    leagueId: string;
    venue?: string;
  };
  tags: string[]; // ["goal", "save", "red-card"]
  createdAt: string;
  // V2 Enhanced features
  quality: 'HD' | '4K' | 'SD';
  isLive?: boolean;
  engagement: {
    likes: number;
    shares: number;
    comments: number;
  };
  category: 'goal' | 'save' | 'skill' | 'foul' | 'celebration' | 'controversy';
  importance: 'high' | 'medium' | 'low';
  previewGif?: string; // For hover previews
}

// Enhanced Statistic with interactive features
export interface StatisticV2 {
  id: string;
  title: string;
  value: string | number;
  subtitle?: string;
  icon: string;
  trend?: TrendAnalysis;
  category: StatisticCategory;
  color: string; // hex color for theming
  // V2 Enhanced features
  isRealTime: boolean;
  lastUpdated: string;
  historicalData?: {
    period: string;
    value: number;
  }[];
  comparison?: {
    period: string;
    value: string | number;
    change: number;
    changeType: 'increase' | 'decrease' | 'stable';
  };
  interactive: boolean;
  drillDownUrl?: string;
  unit?: string; // 'goals', 'minutes', 'percentage', etc.
}

// Enhanced trend analysis
export interface TrendAnalysis {
  direction: 'up' | 'down' | 'neutral';
  percentage: number;
  period: string; // "vs last week"
  confidence: 'high' | 'medium' | 'low';
  significance: 'major' | 'minor' | 'negligible';
}

// Statistic categories with enhanced metadata
export type StatisticCategory = 
  | 'goals' 
  | 'matches' 
  | 'players' 
  | 'leagues' 
  | 'engagement' 
  | 'performance' 
  | 'betting' 
  | 'social';

// Video player state management
export interface VideoPlayerState {
  isPlaying: boolean;
  currentTime: number;
  duration: number;
  volume: number;
  isMuted: boolean;
  isFullscreen: boolean;
  quality: 'HD' | '4K' | 'SD';
  playbackSpeed: number;
}

// Enhanced engagement features data
export interface EngagementFeaturesDataV2 {
  highlights: MatchHighlightV2[];
  statistics: StatisticV2[];
  // V2 specific metadata
  lastUpdated: string;
  realTimeEnabled: boolean;
  userPreferences?: {
    favoriteLeagues: string[];
    preferredCategories: StatisticCategory[];
    autoplay: boolean;
    notifications: boolean;
  };
}

// Filter options for V2
export interface FilterOptions {
  category?: string[];
  league?: string[];
  dateRange?: {
    start: string;
    end: string;
  };
  quality?: ('HD' | '4K' | 'SD')[];
  importance?: ('high' | 'medium' | 'low')[];
}

// Real-time update event
export interface RealTimeUpdate {
  type: 'statistic' | 'highlight' | 'engagement';
  id: string;
  data: Partial<StatisticV2> | Partial<MatchHighlightV2>;
  timestamp: string;
}

// Animation configuration
export interface AnimationConfig {
  duration: number;
  easing: 'ease' | 'ease-in' | 'ease-out' | 'ease-in-out';
  delay?: number;
  stagger?: number;
}

// Theme configuration for V2
export interface ThemeConfig {
  mode: 'light' | 'dark' | 'auto';
  primaryColor: string;
  accentColor: string;
  glassmorphism: boolean;
  animations: boolean;
}
