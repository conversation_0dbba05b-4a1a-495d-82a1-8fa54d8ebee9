'use client';

import React, { useState, useEffect } from 'react';
import { MatchHighlightsV2 } from './components/MatchHighlightsV2';
import { StatisticsWidgetV2 } from './components/StatisticsWidgetV2';
import { EngagementFeaturesDataV2, MatchHighlightV2, StatisticV2 } from './types';

interface EngagementFeaturesV2Props {
  className?: string;
  realTimeEnabled?: boolean;
  theme?: 'light' | 'dark' | 'auto';
}

export const EngagementFeaturesV2: React.FC<EngagementFeaturesV2Props> = ({
  className = '',
  realTimeEnabled = true,
  theme = 'light'
}) => {
  const [data, setData] = useState<EngagementFeaturesDataV2 | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<'highlights' | 'statistics'>('highlights');

  // Enhanced mock data for V2
  const mockDataV2: EngagementFeaturesDataV2 = {
    highlights: [
      {
        id: '1',
        title: '<PERSON><PERSON>\'s Incredible Solo Goal - El Clasico Magic',
        description: '<PERSON> dribbles past 4 defenders to score a stunning goal',
        thumbnailUrl: 'https://via.placeholder.com/112x80/FF6B6B/FFFFFF?text=Goal',
        videoUrl: '#',
        duration: '1:23',
        views: 3500000,
        matchInfo: {
          homeTeam: 'Barcelona',
          awayTeam: 'Real Madrid',
          homeTeamLogo: `${process.env.NEXT_PUBLIC_CDN_DOMAIN_PICTURE || 'http://**************'}/public/images/teams/293.png`,
          awayTeamLogo: `${process.env.NEXT_PUBLIC_CDN_DOMAIN_PICTURE || 'http://**************'}/public/images/teams/294.png`,
          date: '2024-01-20',
          score: '2-1',
          league: 'La Liga',
          leagueId: '140',
          venue: 'Camp Nou'
        },
        tags: ['goal', 'skill', 'el-clasico'],
        createdAt: '2024-01-20T21:30:00Z',
        quality: '4K',
        isLive: false,
        engagement: { likes: 125000, shares: 45000, comments: 8900 },
        category: 'goal',
        importance: 'high',
        previewGif: 'https://via.placeholder.com/112x80/FF6B6B/FFFFFF?text=Preview'
      },
      {
        id: '2',
        title: 'Goalkeeper Wonder Save - Last Minute Heroics',
        description: 'Incredible reflex save denies certain goal in Champions League',
        thumbnailUrl: 'https://via.placeholder.com/112x80/4ECDC4/FFFFFF?text=Save',
        videoUrl: '#',
        duration: '0:45',
        views: 2100000,
        matchInfo: {
          homeTeam: 'Liverpool',
          awayTeam: 'Manchester City',
          homeTeamLogo: `${process.env.NEXT_PUBLIC_CDN_DOMAIN_PICTURE || 'http://**************'}/public/images/teams/40.png`,
          awayTeamLogo: `${process.env.NEXT_PUBLIC_CDN_DOMAIN_PICTURE || 'http://**************'}/public/images/teams/50.png`,
          date: '2024-01-19',
          score: '1-0',
          league: 'Champions League',
          leagueId: '2',
          venue: 'Anfield'
        },
        tags: ['save', 'goalkeeper', 'champions-league'],
        createdAt: '2024-01-19T20:15:00Z',
        quality: 'HD',
        isLive: false,
        engagement: { likes: 89000, shares: 23000, comments: 5600 },
        category: 'save',
        importance: 'high'
      },
      {
        id: '3',
        title: 'Skill Show - Neymar\'s Rainbow Flick Compilation',
        description: 'Best skill moves from the Brazilian magician',
        thumbnailUrl: 'https://via.placeholder.com/112x80/8B5CF6/FFFFFF?text=Skill',
        videoUrl: '#',
        duration: '2:15',
        views: 1800000,
        matchInfo: {
          homeTeam: 'PSG',
          awayTeam: 'Marseille',
          homeTeamLogo: `${process.env.NEXT_PUBLIC_CDN_DOMAIN_PICTURE || 'http://**************'}/public/images/teams/85.png`,
          awayTeamLogo: `${process.env.NEXT_PUBLIC_CDN_DOMAIN_PICTURE || 'http://**************'}/public/images/teams/86.png`,
          date: '2024-01-18',
          score: '3-1',
          league: 'Ligue 1',
          leagueId: '61',
          venue: 'Parc des Princes'
        },
        tags: ['skill', 'neymar', 'rainbow-flick'],
        createdAt: '2024-01-18T19:45:00Z',
        quality: 'HD',
        isLive: false,
        engagement: { likes: 67000, shares: 18000, comments: 4200 },
        category: 'skill',
        importance: 'medium'
      },
      {
        id: '4',
        title: 'VAR Drama - Controversial Penalty Decision',
        description: 'VAR review leads to heated discussions and penalty award',
        thumbnailUrl: 'https://via.placeholder.com/112x80/EF4444/FFFFFF?text=VAR',
        videoUrl: '#',
        duration: '1:45',
        views: 1200000,
        matchInfo: {
          homeTeam: 'Chelsea',
          awayTeam: 'Arsenal',
          homeTeamLogo: `${process.env.NEXT_PUBLIC_CDN_DOMAIN_PICTURE || 'http://**************'}/public/images/teams/49.png`,
          awayTeamLogo: `${process.env.NEXT_PUBLIC_CDN_DOMAIN_PICTURE || 'http://**************'}/public/images/teams/42.png`,
          date: '2024-01-17',
          score: '2-2',
          league: 'Premier League',
          leagueId: '39',
          venue: 'Stamford Bridge'
        },
        tags: ['var', 'penalty', 'controversy'],
        createdAt: '2024-01-17T18:30:00Z',
        quality: 'HD',
        isLive: false,
        engagement: { likes: 45000, shares: 12000, comments: 8900 },
        category: 'controversy',
        importance: 'medium'
      }
    ],
    statistics: [
      {
        id: '1',
        title: 'Goals Today',
        value: 147,
        subtitle: 'Across all leagues',
        icon: '⚽',
        trend: {
          direction: 'up',
          percentage: 18,
          period: 'vs yesterday',
          confidence: 'high',
          significance: 'major'
        },
        category: 'goals',
        color: '#10B981',
        isRealTime: true,
        lastUpdated: '2024-01-20T15:30:00Z',
        historicalData: [
          { period: 'Today', value: 147 },
          { period: 'Yesterday', value: 125 },
          { period: '2 days ago', value: 132 }
        ],
        comparison: {
          period: 'Last week average',
          value: 128,
          change: 15,
          changeType: 'increase'
        },
        interactive: true,
        unit: 'goals'
      },
      {
        id: '2',
        title: 'Live Matches',
        value: 28,
        subtitle: 'Currently playing',
        icon: '🏟️',
        trend: {
          direction: 'neutral',
          percentage: 0,
          period: 'stable',
          confidence: 'high',
          significance: 'negligible'
        },
        category: 'matches',
        color: '#3B82F6',
        isRealTime: true,
        lastUpdated: '2024-01-20T15:30:00Z',
        interactive: true,
        unit: 'matches'
      },
      {
        id: '3',
        title: 'Top Scorer',
        value: 'Haaland',
        subtitle: '32 goals this season',
        icon: '👤',
        trend: {
          direction: 'up',
          percentage: 12,
          period: 'this month',
          confidence: 'high',
          significance: 'major'
        },
        category: 'players',
        color: '#8B5CF6',
        isRealTime: false,
        lastUpdated: '2024-01-20T12:00:00Z',
        interactive: true,
        drillDownUrl: '/players/haaland'
      },
      {
        id: '4',
        title: 'Active Leagues',
        value: 168,
        subtitle: 'Worldwide coverage',
        icon: '🏆',
        trend: {
          direction: 'up',
          percentage: 5,
          period: 'vs last month',
          confidence: 'medium',
          significance: 'minor'
        },
        category: 'leagues',
        color: '#F59E0B',
        isRealTime: false,
        lastUpdated: '2024-01-20T10:00:00Z',
        interactive: true,
        unit: 'leagues'
      },
      {
        id: '5',
        title: 'Fan Engagement',
        value: '3.2M',
        subtitle: 'Social interactions today',
        icon: '📱',
        trend: {
          direction: 'up',
          percentage: 28,
          period: 'vs yesterday',
          confidence: 'high',
          significance: 'major'
        },
        category: 'engagement',
        color: '#EF4444',
        isRealTime: true,
        lastUpdated: '2024-01-20T15:30:00Z',
        comparison: {
          period: 'Yesterday',
          value: '2.5M',
          change: 28,
          changeType: 'increase'
        },
        interactive: true,
        unit: 'interactions'
      },
      {
        id: '6',
        title: 'Match Performance',
        value: '94.2%',
        subtitle: 'Average match completion',
        icon: '📊',
        trend: {
          direction: 'up',
          percentage: 2,
          period: 'vs last week',
          confidence: 'medium',
          significance: 'minor'
        },
        category: 'performance',
        color: '#06B6D4',
        isRealTime: true,
        lastUpdated: '2024-01-20T15:30:00Z',
        interactive: true,
        unit: '%'
      }
    ],
    lastUpdated: '2024-01-20T15:30:00Z',
    realTimeEnabled: realTimeEnabled,
    userPreferences: {
      favoriteLeagues: ['39', '140', '61'],
      preferredCategories: ['goals', 'matches', 'players'],
      autoplay: false,
      notifications: true
    }
  };

  useEffect(() => {
    // Simulate API call with enhanced loading
    const loadData = async () => {
      setIsLoading(true);
      // Simulate network delay
      await new Promise(resolve => setTimeout(resolve, 1200));
      setData(mockDataV2);
      setIsLoading(false);
    };

    loadData();
  }, []);

  // Handle video play
  const handleVideoPlay = (highlight: MatchHighlightV2) => {
    console.log('Playing video:', highlight.title);
    // Here you would integrate with your video player
  };

  // Handle statistic click
  const handleStatisticClick = (statistic: StatisticV2) => {
    console.log('Statistic clicked:', statistic.title);
    if (statistic.drillDownUrl) {
      // Navigate to detailed view
      console.log('Navigate to:', statistic.drillDownUrl);
    }
  };

  return (
    <section className={`py-16 bg-gradient-to-br from-gray-50 via-blue-50 to-purple-50 ${className}`}>
      <div className="container mx-auto px-4 max-w-7xl">
        {/* Enhanced Section Header */}
        <div className="text-center mb-12">
          <div className="inline-flex items-center space-x-2 bg-white/80 backdrop-blur-sm px-4 py-2 rounded-full border border-gray-200/50 mb-4">
            <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
            <span className="text-sm font-medium text-gray-600">Live Updates</span>
          </div>
          <h2 className="text-4xl font-bold text-gray-900 mb-4">
            Engagement Hub
            <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-purple-600"> V2</span>
          </h2>
          <p className="text-lg text-gray-600 max-w-3xl mx-auto">
            Experience the next generation of football engagement with real-time highlights,
            interactive statistics, and immersive content designed for the modern fan
          </p>
        </div>

        {/* Mobile Tab Navigation */}
        <div className="lg:hidden mb-8">
          <div className="flex bg-white/80 backdrop-blur-sm rounded-xl border border-gray-200/50 p-1">
            <button
              onClick={() => setActiveTab('highlights')}
              className={`flex-1 py-3 px-4 rounded-lg text-sm font-medium transition-all duration-200 ${activeTab === 'highlights'
                  ? 'bg-blue-500 text-white shadow-md'
                  : 'text-gray-600 hover:text-gray-900'
                }`}
            >
              🎬 Highlights
            </button>
            <button
              onClick={() => setActiveTab('statistics')}
              className={`flex-1 py-3 px-4 rounded-lg text-sm font-medium transition-all duration-200 ${activeTab === 'statistics'
                  ? 'bg-blue-500 text-white shadow-md'
                  : 'text-gray-600 hover:text-gray-900'
                }`}
            >
              📊 Statistics
            </button>
          </div>
        </div>

        {/* Enhanced Features Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Match Highlights V2 */}
          <div className={`${activeTab === 'highlights' ? 'block' : 'hidden'} lg:block`}>
            <MatchHighlightsV2
              highlights={data?.highlights || []}
              isLoading={isLoading}
              onVideoPlay={handleVideoPlay}
            />
          </div>

          {/* Statistics Widget V2 */}
          <div className={`${activeTab === 'statistics' ? 'block' : 'hidden'} lg:block`}>
            <StatisticsWidgetV2
              statistics={data?.statistics || []}
              isLoading={isLoading}
              realTimeEnabled={realTimeEnabled}
              onStatisticClick={handleStatisticClick}
            />
          </div>
        </div>



        {/* Real-time Status Indicator */}
        {realTimeEnabled && (
          <div className="fixed bottom-6 right-6 bg-white/90 backdrop-blur-sm rounded-full px-4 py-2 border border-gray-200/50 shadow-lg">
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
              <span className="text-xs font-medium text-gray-700">Live Updates Active</span>
            </div>
          </div>
        )}
      </div>
    </section>
  );
};
