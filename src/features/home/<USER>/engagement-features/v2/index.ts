// Engagement Features V2 - Component Exports
// This file exports all V2 components and types

// Main component
export { EngagementFeaturesV2 } from './EngagementFeaturesV2';

// Individual components
export { MatchHighlightsV2 } from './components/MatchHighlightsV2';
export { StatisticsWidgetV2 } from './components/StatisticsWidgetV2';

// Types
export type {
  MatchHighlightV2,
  StatisticV2,
  EngagementFeaturesDataV2,
  VideoPlayerState,
  StatisticCategory,
  TrendAnalysis,
  FilterOptions,
  RealTimeUpdate,
  AnimationConfig,
  ThemeConfig
} from './types';

// Component metadata
export const V2_COMPONENTS = {
  MatchHighlightsV2: {
    name: 'Match Highlights V2',
    description: 'Enhanced video highlights with advanced features and real-time capabilities',
    features: [
      'Enhanced video player',
      'Real-time engagement metrics',
      'Advanced filtering',
      'Interactive modal',
      'Quality indicators',
      'Live streaming support',
      'Social sharing',
      'CDN integration'
    ],
    version: '2.0.0',
    status: 'stable'
  },
  StatisticsWidgetV2: {
    name: 'Statistics Widget V2',
    description: 'Interactive statistics with real-time updates and advanced analytics',
    features: [
      'Real-time updates',
      'Interactive trends',
      'Category filtering',
      'Comparative analytics',
      'Drill-down navigation',
      'Historical data',
      'Confidence indicators',
      'Multi-category support'
    ],
    version: '2.0.0',
    status: 'stable'
  }
} as const;

// Default props for components
export const DEFAULT_PROPS = {
  EngagementFeaturesV2: {
    className: '',
    realTimeEnabled: true,
    theme: 'light' as const
  },
  MatchHighlightsV2: {
    highlights: [],
    isLoading: false
  },
  StatisticsWidgetV2: {
    statistics: [],
    isLoading: false,
    realTimeEnabled: true
  }
} as const;

// V2 Feature flags
export const V2_FEATURES = {
  REAL_TIME_UPDATES: true,
  ADVANCED_FILTERING: true,
  INTERACTIVE_ANALYTICS: true,
  VIDEO_PREVIEW: true,
  SOCIAL_SHARING: true,
  GLASSMORPHISM_UI: true,
  MICRO_ANIMATIONS: true,
  DARK_MODE: false, // Coming in V3
  VOICE_CONTROL: false, // Coming in V3
  AI_RECOMMENDATIONS: false // Coming in V3
} as const;

// Performance configuration
export const PERFORMANCE_CONFIG = {
  LAZY_LOADING: true,
  IMAGE_OPTIMIZATION: true,
  ANIMATION_REDUCED_MOTION: true,
  CACHE_STRATEGY: 'stale-while-revalidate',
  PREFETCH_ENABLED: true,
  VIRTUAL_SCROLLING: false // For large datasets
} as const;

// Theme configuration
export const THEME_CONFIG = {
  light: {
    primary: '#3B82F6',
    secondary: '#8B5CF6',
    accent: '#10B981',
    background: 'rgba(255, 255, 255, 0.8)',
    surface: 'rgba(255, 255, 255, 0.9)',
    text: '#1F2937',
    textSecondary: '#6B7280'
  },
  dark: {
    primary: '#60A5FA',
    secondary: '#A78BFA',
    accent: '#34D399',
    background: 'rgba(17, 24, 39, 0.8)',
    surface: 'rgba(31, 41, 55, 0.9)',
    text: '#F9FAFB',
    textSecondary: '#D1D5DB'
  }
} as const;

// Animation presets
export const ANIMATION_PRESETS = {
  fadeIn: {
    duration: 300,
    easing: 'ease-out' as const
  },
  slideUp: {
    duration: 400,
    easing: 'ease-out' as const
  },
  scaleIn: {
    duration: 200,
    easing: 'ease-out' as const
  },
  pulse: {
    duration: 1000,
    easing: 'ease-in-out' as const
  }
} as const;

// API endpoints for V2
export const API_ENDPOINTS = {
  HIGHLIGHTS: '/api/football/highlights',
  STATISTICS: '/api/football/statistics',
  REAL_TIME_UPDATES: '/api/football/live-updates',
  USER_PREFERENCES: '/api/user/preferences'
} as const;

// Error messages
export const ERROR_MESSAGES = {
  LOAD_FAILED: 'Failed to load engagement features',
  VIDEO_UNAVAILABLE: 'Video is currently unavailable',
  STATISTICS_ERROR: 'Unable to fetch latest statistics',
  REAL_TIME_DISCONNECTED: 'Real-time updates disconnected',
  NETWORK_ERROR: 'Network connection error'
} as const;

// Success messages
export const SUCCESS_MESSAGES = {
  DATA_LOADED: 'Engagement features loaded successfully',
  VIDEO_SHARED: 'Video shared successfully',
  PREFERENCES_SAVED: 'Preferences saved',
  REAL_TIME_CONNECTED: 'Real-time updates connected'
} as const;
