// Engagement Features - Version Management
// This file manages all versions of Engagement Features components

// Version 1 - Initial implementation with Match Highlights, Statistics Widget, and Social Feed
export { EngagementFeaturesV1 } from './v1/EngagementFeaturesV1';

// Types - Export all types for external use
export type { 
  MatchHighlight,
  Statistic, 
  SocialPost,
  EngagementFeaturesData 
} from './v1/types';

// Default export - Current active version
// Change this to switch the default version used throughout the app
export { EngagementFeaturesV1 as EngagementFeatures } from './v1/EngagementFeaturesV1';

// Version mapping for dynamic imports
export const ENGAGEMENT_FEATURES_VERSIONS = {
  v1: () => import('./v1/EngagementFeaturesV1').then(m => m.EngagementFeaturesV1),
  // v2: () => import('./v2/EngagementFeaturesV2').then(m => m.EngagementFeaturesV2),
  // v3: () => import('./v3/EngagementFeaturesV3').then(m => m.EngagementFeaturesV3),
} as const;

// Version metadata
export const VERSION_INFO = {
  v1: {
    name: 'Engagement Features V1',
    description: 'Initial implementation with Match Highlights, Statistics Widget, and Social Feed',
    features: ['Match Highlights', 'Statistics Widget', 'Social Feed'],
    status: 'stable',
    releaseDate: '2024-01-15'
  },
  // v2: {
  //   name: 'Engagement Features V2',
  //   description: 'Enhanced version with improved UI/UX and additional features',
  //   features: ['Enhanced Match Highlights', 'Advanced Statistics', 'Improved Social Feed', 'New Feature'],
  //   status: 'development',
  //   releaseDate: 'TBD'
  // }
} as const;

// Current version constant
export const CURRENT_VERSION = 'v1' as const;

// Helper function to get version component
export const getEngagementFeaturesVersion = (version: keyof typeof ENGAGEMENT_FEATURES_VERSIONS = CURRENT_VERSION) => {
  return ENGAGEMENT_FEATURES_VERSIONS[version];
};

// Helper function to get version info
export const getVersionInfo = (version: keyof typeof VERSION_INFO = CURRENT_VERSION) => {
  return VERSION_INFO[version];
};
