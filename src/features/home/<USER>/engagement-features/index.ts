// Engagement Features - Version Management
// This file manages all versions of Engagement Features components

// Version 1 - Initial implementation with Match Highlights, Statistics Widget, and Social Feed
export { EngagementFeaturesV1 } from './v1/EngagementFeaturesV1';

// Version 2 - Enhanced UI/UX with advanced features and real-time capabilities
export { EngagementFeaturesV2 } from './v2/EngagementFeaturesV2';

// Types - Export all types for external use
export type {
  MatchHighlight,
  Statistic,
  SocialPost,
  EngagementFeaturesData
} from './v1/types';

// V2 Enhanced Types
export type {
  MatchHighlightV2,
  StatisticV2,
  EngagementFeaturesDataV2,
  VideoPlayerState,
  StatisticCategory,
  TrendAnalysis
} from './v2/types';

// Default export - Current active version
// Change this to switch the default version used throughout the app
export { EngagementFeaturesV2 as EngagementFeatures } from './v2/EngagementFeaturesV2';

// Version mapping for dynamic imports
export const ENGAGEMENT_FEATURES_VERSIONS = {
  v1: () => import('./v1/EngagementFeaturesV1').then(m => m.EngagementFeaturesV1),
  v2: () => import('./v2/EngagementFeaturesV2').then(m => m.EngagementFeaturesV2),
  // v3: () => import('./v3/EngagementFeaturesV3').then(m => m.EngagementFeaturesV3),
} as const;

// Version metadata
export const VERSION_INFO = {
  v1: {
    name: 'Engagement Features V1',
    description: 'Initial implementation with Match Highlights, Statistics Widget, and Social Feed',
    features: ['Match Highlights', 'Statistics Widget', 'Social Feed'],
    status: 'stable',
    releaseDate: '2024-01-15'
  },
  v2: {
    name: 'Engagement Features V2',
    description: 'Enhanced version with breakthrough UI/UX, real-time capabilities, and advanced interactions',
    features: ['Enhanced Video Highlights', 'Interactive Statistics', 'Real-time Updates', 'Advanced Filtering', 'Micro-animations'],
    status: 'stable',
    releaseDate: '2024-01-20'
  }
} as const;

// Current version constant
export const CURRENT_VERSION = 'v2' as const;

// Helper function to get version component
export const getEngagementFeaturesVersion = (version: keyof typeof ENGAGEMENT_FEATURES_VERSIONS = CURRENT_VERSION) => {
  return ENGAGEMENT_FEATURES_VERSIONS[version];
};

// Helper function to get version info
export const getVersionInfo = (version: keyof typeof VERSION_INFO = CURRENT_VERSION) => {
  return VERSION_INFO[version];
};
