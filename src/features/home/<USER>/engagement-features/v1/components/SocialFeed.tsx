'use client';

import React, { useState } from 'react';
import { SocialPost } from '../types';

interface SocialFeedProps {
  posts: SocialPost[];
  isLoading?: boolean;
}

export const SocialFeed: React.FC<SocialFeedProps> = ({
  posts,
  isLoading = false
}) => {
  const [selectedPlatform, setSelectedPlatform] = useState<string>('all');

  const getPlatformIcon = (platform: string) => {
    const icons: Record<string, { icon: string; color: string }> = {
      'twitter': { icon: '🐦', color: 'text-blue-400' },
      'instagram': { icon: '📷', color: 'text-pink-500' },
      'facebook': { icon: '📘', color: 'text-blue-600' },
      'youtube': { icon: '📺', color: 'text-red-500' }
    };
    return icons[platform] || { icon: '📱', color: 'text-gray-500' };
  };

  const formatEngagement = (count: number) => {
    if (count >= 1000000) return `${(count / 1000000).toFixed(1)}M`;
    if (count >= 1000) return `${(count / 1000).toFixed(1)}K`;
    return count.toString();
  };

  const getTimeAgo = (timestamp: string) => {
    const now = new Date();
    const postTime = new Date(timestamp);
    const diffInHours = Math.floor((now.getTime() - postTime.getTime()) / (1000 * 60 * 60));
    
    if (diffInHours < 1) return 'Just now';
    if (diffInHours < 24) return `${diffInHours}h ago`;
    const diffInDays = Math.floor(diffInHours / 24);
    if (diffInDays < 7) return `${diffInDays}d ago`;
    return postTime.toLocaleDateString();
  };

  const filteredPosts = selectedPlatform === 'all' 
    ? posts 
    : posts.filter(post => post.platform === selectedPlatform);

  if (isLoading) {
    return (
      <div className="bg-white rounded-xl border border-gray-200 p-6">
        <div className="flex items-center space-x-3 mb-6">
          <div className="w-8 h-8 bg-gray-200 rounded-lg animate-pulse"></div>
          <div className="h-6 bg-gray-200 rounded w-32 animate-pulse"></div>
        </div>
        <div className="space-y-4">
          {[1, 2, 3].map((i) => (
            <div key={i} className="flex space-x-3 p-4 border border-gray-100 rounded-lg">
              <div className="w-10 h-10 bg-gray-200 rounded-full animate-pulse"></div>
              <div className="flex-1 space-y-2">
                <div className="h-4 bg-gray-200 rounded w-1/4 animate-pulse"></div>
                <div className="h-4 bg-gray-200 rounded w-3/4 animate-pulse"></div>
                <div className="h-3 bg-gray-200 rounded w-1/2 animate-pulse"></div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-xl border border-gray-200 p-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          <div className="w-8 h-8 bg-gradient-to-r from-green-500 to-blue-500 rounded-lg flex items-center justify-center">
            <span className="text-white text-lg">📱</span>
          </div>
          <div>
            <h3 className="text-lg font-bold text-gray-900">Social Feed</h3>
            <p className="text-sm text-gray-600">Latest from social media</p>
          </div>
        </div>
      </div>

      {/* Platform Filter */}
      <div className="flex space-x-2 mb-6 overflow-x-auto">
        <button
          onClick={() => setSelectedPlatform('all')}
          className={`px-3 py-1.5 rounded-full text-sm font-medium whitespace-nowrap transition-colors ${
            selectedPlatform === 'all'
              ? 'bg-blue-100 text-blue-700'
              : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
          }`}
        >
          All
        </button>
        {['twitter', 'instagram', 'facebook', 'youtube'].map((platform) => {
          const { icon, color } = getPlatformIcon(platform);
          return (
            <button
              key={platform}
              onClick={() => setSelectedPlatform(platform)}
              className={`flex items-center space-x-1 px-3 py-1.5 rounded-full text-sm font-medium whitespace-nowrap transition-colors ${
                selectedPlatform === platform
                  ? 'bg-blue-100 text-blue-700'
                  : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
              }`}
            >
              <span>{icon}</span>
              <span className="capitalize">{platform}</span>
            </button>
          );
        })}
      </div>

      {/* Posts Feed */}
      <div className="space-y-4 max-h-96 overflow-y-auto">
        {filteredPosts.slice(0, 5).map((post) => {
          const { icon, color } = getPlatformIcon(post.platform);
          return (
            <div key={post.id} className="border border-gray-100 rounded-lg p-4 hover:border-gray-200 transition-colors">
              {/* Post Header */}
              <div className="flex items-start space-x-3 mb-3">
                <img
                  src={post.author.avatar}
                  alt={post.author.name}
                  className="w-10 h-10 rounded-full"
                  onError={(e) => {
                    e.currentTarget.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMjAiIGN5PSIyMCIgcj0iMjAiIGZpbGw9IiNGM0Y0RjYiLz4KPGNpcmNsZSBjeD0iMjAiIGN5PSIxNiIgcj0iNiIgZmlsbD0iIzlDQTNBRiIvPgo8cGF0aCBkPSJNMzAgMzJDMzAgMjYuNDc3MSAyNS41MjI5IDIyIDIwIDIyQzE0LjQ3NzEgMjIgMTAgMjYuNDc3MSAxMCAzMkgzMFoiIGZpbGw9IiM5Q0EzQUYiLz4KPC9zdmc+';
                  }}
                />
                <div className="flex-1 min-w-0">
                  <div className="flex items-center space-x-2">
                    <span className="font-medium text-gray-900 text-sm">
                      {post.author.name}
                    </span>
                    {post.author.verified && (
                      <span className="text-blue-500">✓</span>
                    )}
                    <span className={`${color}`}>{icon}</span>
                  </div>
                  <div className="flex items-center space-x-2 text-xs text-gray-500">
                    <span>@{post.author.username}</span>
                    <span>•</span>
                    <span>{getTimeAgo(post.timestamp)}</span>
                  </div>
                </div>
              </div>

              {/* Post Content */}
              <div className="mb-3">
                <p className="text-sm text-gray-800 line-clamp-3">
                  {post.content}
                </p>
                
                {/* Hashtags */}
                {post.hashtags.length > 0 && (
                  <div className="flex flex-wrap gap-1 mt-2">
                    {post.hashtags.slice(0, 3).map((hashtag) => (
                      <span key={hashtag} className="text-xs text-blue-600 hover:text-blue-700 cursor-pointer">
                        #{hashtag}
                      </span>
                    ))}
                  </div>
                )}
              </div>

              {/* Media */}
              {post.media && (
                <div className="mb-3">
                  <div className="w-full h-32 bg-gray-100 rounded-lg overflow-hidden">
                    <img
                      src={post.media.thumbnail || post.media.url}
                      alt="Post media"
                      className="w-full h-full object-cover"
                      onError={(e) => {
                        e.currentTarget.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIwIiBoZWlnaHQ9IjEyOCIgdmlld0JveD0iMCAwIDMyMCAxMjgiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMjAiIGhlaWdodD0iMTI4IiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0xNjAgNjRMMTQ0IDUyVjc2TDE2MCA2NFoiIGZpbGw9IiM5Q0EzQUYiLz4KPC9zdmc+';
                      }}
                    />
                  </div>
                </div>
              )}

              {/* Engagement */}
              <div className="flex items-center space-x-4 text-xs text-gray-500">
                <span className="flex items-center space-x-1">
                  <span>❤️</span>
                  <span>{formatEngagement(post.engagement.likes)}</span>
                </span>
                <span className="flex items-center space-x-1">
                  <span>💬</span>
                  <span>{formatEngagement(post.engagement.comments)}</span>
                </span>
                <span className="flex items-center space-x-1">
                  <span>🔄</span>
                  <span>{formatEngagement(post.engagement.shares)}</span>
                </span>
              </div>
            </div>
          );
        })}
      </div>

      {/* View More Button */}
      <div className="mt-6 pt-4 border-t border-gray-100">
        <button className="w-full text-center text-sm font-medium text-blue-600 hover:text-blue-700 py-2 hover:bg-blue-50 rounded-lg transition-colors">
          View More Posts →
        </button>
      </div>
    </div>
  );
};
