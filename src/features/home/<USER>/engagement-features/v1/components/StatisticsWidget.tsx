'use client';

import React from 'react';
import { Statistic } from '../types';

interface StatisticsWidgetProps {
  statistics: Statistic[];
  isLoading?: boolean;
}

export const StatisticsWidget: React.FC<StatisticsWidgetProps> = ({
  statistics,
  isLoading = false
}) => {
  const formatValue = (value: string | number) => {
    if (typeof value === 'number') {
      if (value >= 1000000) return `${(value / 1000000).toFixed(1)}M`;
      if (value >= 1000) return `${(value / 1000).toFixed(1)}K`;
      return value.toLocaleString();
    }
    return value;
  };

  const getTrendIcon = (direction: 'up' | 'down' | 'neutral') => {
    switch (direction) {
      case 'up':
        return (
          <svg className="w-4 h-4 text-green-500" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M5.293 9.707a1 1 0 010-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 01-1.414 1.414L11 7.414V15a1 1 0 11-2 0V7.414L6.707 9.707a1 1 0 01-1.414 0z" clipRule="evenodd" />
          </svg>
        );
      case 'down':
        return (
          <svg className="w-4 h-4 text-red-500" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M14.707 10.293a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 111.414-1.414L9 12.586V5a1 1 0 012 0v7.586l2.293-2.293a1 1 0 011.414 0z" clipRule="evenodd" />
          </svg>
        );
      default:
        return (
          <svg className="w-4 h-4 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M3 10a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clipRule="evenodd" />
          </svg>
        );
    }
  };

  const getCategoryIcon = (category: string) => {
    const icons: Record<string, string> = {
      'goals': '⚽',
      'matches': '🏟️',
      'players': '👤',
      'leagues': '🏆'
    };
    return icons[category] || '📊';
  };

  if (isLoading) {
    return (
      <div className="bg-white rounded-xl border border-gray-200 p-6">
        <div className="flex items-center space-x-3 mb-6">
          <div className="w-8 h-8 bg-gray-200 rounded-lg animate-pulse"></div>
          <div className="h-6 bg-gray-200 rounded w-32 animate-pulse"></div>
        </div>
        <div className="grid grid-cols-2 gap-4">
          {[1, 2, 3, 4].map((i) => (
            <div key={i} className="p-4 bg-gray-50 rounded-lg">
              <div className="h-4 bg-gray-200 rounded w-16 animate-pulse mb-2"></div>
              <div className="h-8 bg-gray-200 rounded w-20 animate-pulse mb-1"></div>
              <div className="h-3 bg-gray-200 rounded w-24 animate-pulse"></div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-xl border border-gray-200 p-6">
      {/* Header */}
      <div className="flex items-center space-x-3 mb-6">
        <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-500 rounded-lg flex items-center justify-center">
          <span className="text-white text-lg">📊</span>
        </div>
        <div>
          <h3 className="text-lg font-bold text-gray-900">Live Statistics</h3>
          <p className="text-sm text-gray-600">Real-time football data</p>
        </div>
      </div>

      {/* Statistics Grid */}
      <div className="grid grid-cols-2 gap-4">
        {statistics.slice(0, 4).map((stat) => (
          <div
            key={stat.id}
            className="p-4 rounded-lg border border-gray-100 hover:border-gray-200 transition-colors"
            style={{ backgroundColor: `${stat.color}08` }}
          >
            {/* Category Icon */}
            <div className="flex items-center justify-between mb-2">
              <span className="text-lg">{getCategoryIcon(stat.category)}</span>
              {stat.trend && (
                <div className="flex items-center space-x-1">
                  {getTrendIcon(stat.trend.direction)}
                  <span className={`text-xs font-medium ${
                    stat.trend.direction === 'up' ? 'text-green-600' :
                    stat.trend.direction === 'down' ? 'text-red-600' : 'text-gray-500'
                  }`}>
                    {stat.trend.percentage}%
                  </span>
                </div>
              )}
            </div>

            {/* Value */}
            <div className="mb-1">
              <div className="text-2xl font-bold text-gray-900">
                {formatValue(stat.value)}
              </div>
              <div className="text-sm font-medium text-gray-700">
                {stat.title}
              </div>
            </div>

            {/* Subtitle & Trend */}
            <div className="text-xs text-gray-500">
              {stat.subtitle}
              {stat.trend && (
                <span className="ml-1">
                  {stat.trend.period}
                </span>
              )}
            </div>
          </div>
        ))}
      </div>

      {/* Featured Stat */}
      {statistics.length > 4 && (
        <div className="mt-4 p-4 bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg border border-blue-100">
          <div className="flex items-center justify-between">
            <div>
              <div className="text-sm font-medium text-gray-600 mb-1">
                🔥 Hot Stat
              </div>
              <div className="text-xl font-bold text-gray-900">
                {formatValue(statistics[4].value)}
              </div>
              <div className="text-sm text-gray-700">
                {statistics[4].title}
              </div>
            </div>
            <div className="text-3xl opacity-50">
              {getCategoryIcon(statistics[4].category)}
            </div>
          </div>
        </div>
      )}

      {/* View More Button */}
      <div className="mt-6 pt-4 border-t border-gray-100">
        <button className="w-full text-center text-sm font-medium text-blue-600 hover:text-blue-700 py-2 hover:bg-blue-50 rounded-lg transition-colors">
          View All Statistics →
        </button>
      </div>
    </div>
  );
};
