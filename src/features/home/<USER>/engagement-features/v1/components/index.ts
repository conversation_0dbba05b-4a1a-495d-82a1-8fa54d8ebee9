// Engagement Features V1 Components - Individual Exports
// This file exports all individual components for V1

export { MatchHighlights } from './MatchHighlights';
export { StatisticsWidget } from './StatisticsWidget';
export { SocialFeed } from './SocialFeed';

// Component registry for dynamic imports
export const COMPONENTS = {
  MatchHighlights: () => import('./MatchHighlights').then(m => m.MatchHighlights),
  StatisticsWidget: () => import('./StatisticsWidget').then(m => m.StatisticsWidget),
  SocialFeed: () => import('./SocialFeed').then(m => m.SocialFeed),
} as const;

// Component names for iteration
export const COMPONENT_NAMES = ['MatchHighlights', 'StatisticsWidget', 'SocialFeed'] as const;

// Component types
export type ComponentName = keyof typeof COMPONENTS;
