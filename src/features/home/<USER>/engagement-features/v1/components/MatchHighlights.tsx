'use client';

import React, { useState } from 'react';
import { MatchHighlight } from '../types';

interface MatchHighlightsProps {
  highlights: MatchHighlight[];
  isLoading?: boolean;
}

export const MatchHighlights: React.FC<MatchHighlightsProps> = ({
  highlights,
  isLoading = false
}) => {
  const [selectedHighlight, setSelectedHighlight] = useState<MatchHighlight | null>(null);

  const formatViews = (views: number) => {
    if (views >= 1000000) return `${(views / 1000000).toFixed(1)}M`;
    if (views >= 1000) return `${(views / 1000).toFixed(1)}K`;
    return views.toString();
  };

  const getTagColor = (tag: string) => {
    const colors: Record<string, string> = {
      'goal': 'bg-green-100 text-green-800',
      'save': 'bg-blue-100 text-blue-800',
      'red-card': 'bg-red-100 text-red-800',
      'yellow-card': 'bg-yellow-100 text-yellow-800',
      'assist': 'bg-purple-100 text-purple-800',
      'penalty': 'bg-orange-100 text-orange-800'
    };
    return colors[tag] || 'bg-gray-100 text-gray-800';
  };

  if (isLoading) {
    return (
      <div className="bg-white rounded-xl border border-gray-200 p-6">
        <div className="flex items-center space-x-3 mb-6">
          <div className="w-8 h-8 bg-gray-200 rounded-lg animate-pulse"></div>
          <div className="h-6 bg-gray-200 rounded w-32 animate-pulse"></div>
        </div>
        <div className="space-y-4">
          {[1, 2, 3].map((i) => (
            <div key={i} className="flex space-x-4">
              <div className="w-24 h-16 bg-gray-200 rounded-lg animate-pulse"></div>
              <div className="flex-1 space-y-2">
                <div className="h-4 bg-gray-200 rounded w-3/4 animate-pulse"></div>
                <div className="h-3 bg-gray-200 rounded w-1/2 animate-pulse"></div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-xl border border-gray-200 p-6">
      {/* Header */}
      <div className="flex items-center space-x-3 mb-6">
        <div className="w-8 h-8 bg-gradient-to-r from-red-500 to-pink-500 rounded-lg flex items-center justify-center">
          <span className="text-white text-lg">🎬</span>
        </div>
        <div>
          <h3 className="text-lg font-bold text-gray-900">Match Highlights</h3>
          <p className="text-sm text-gray-600">Latest video highlights</p>
        </div>
      </div>

      {/* Highlights List */}
      <div className="space-y-4">
        {highlights.slice(0, 4).map((highlight) => (
          <div
            key={highlight.id}
            onClick={() => setSelectedHighlight(highlight)}
            className="flex space-x-4 p-3 rounded-lg hover:bg-gray-50 cursor-pointer transition-colors group"
          >
            {/* Thumbnail */}
            <div className="relative w-24 h-16 bg-gray-100 rounded-lg overflow-hidden flex-shrink-0">
              <img
                src={highlight.thumbnailUrl}
                alt={highlight.title}
                className="w-full h-full object-cover"
                onError={(e) => {
                  e.currentTarget.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iOTYiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA5NiA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9Ijk2IiBoZWlnaHQ9IjY0IiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik00OCAzMkw0MCAyNlY0MEw0OCAzMloiIGZpbGw9IiM5Q0EzQUYiLz4KPC9zdmc+';
                }}
              />
              <div className="absolute inset-0 bg-black bg-opacity-20 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity">
                <div className="w-8 h-8 bg-white bg-opacity-90 rounded-full flex items-center justify-center">
                  <svg className="w-4 h-4 text-gray-700 ml-0.5" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M8 5v10l8-5-8-5z"/>
                  </svg>
                </div>
              </div>
              <div className="absolute bottom-1 right-1 bg-black bg-opacity-75 text-white text-xs px-1 rounded">
                {highlight.duration}
              </div>
            </div>

            {/* Content */}
            <div className="flex-1 min-w-0">
              <h4 className="font-medium text-gray-900 text-sm line-clamp-2 group-hover:text-blue-600 transition-colors">
                {highlight.title}
              </h4>
              <div className="flex items-center space-x-2 mt-1">
                <span className="text-xs text-gray-500">{formatViews(highlight.views)} views</span>
                <span className="text-xs text-gray-400">•</span>
                <span className="text-xs text-gray-500">
                  {new Date(highlight.createdAt).toLocaleDateString()}
                </span>
              </div>
              
              {/* Tags */}
              <div className="flex flex-wrap gap-1 mt-2">
                {highlight.tags.slice(0, 2).map((tag) => (
                  <span
                    key={tag}
                    className={`text-xs px-2 py-0.5 rounded-full ${getTagColor(tag)}`}
                  >
                    {tag}
                  </span>
                ))}
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* View All Button */}
      <div className="mt-6 pt-4 border-t border-gray-100">
        <button className="w-full text-center text-sm font-medium text-blue-600 hover:text-blue-700 py-2 hover:bg-blue-50 rounded-lg transition-colors">
          View All Highlights →
        </button>
      </div>

      {/* Video Modal - Simple placeholder */}
      {selectedHighlight && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-xl max-w-2xl w-full p-6">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-bold">{selectedHighlight.title}</h3>
              <button
                onClick={() => setSelectedHighlight(null)}
                className="text-gray-400 hover:text-gray-600"
              >
                ✕
              </button>
            </div>
            <div className="aspect-video bg-gray-100 rounded-lg flex items-center justify-center">
              <p className="text-gray-500">Video Player Placeholder</p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
