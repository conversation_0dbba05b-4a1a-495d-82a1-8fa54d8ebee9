// Engagement Features V1 Types

export interface MatchHighlight {
  id: string;
  title: string;
  description: string;
  thumbnailUrl: string;
  videoUrl: string;
  duration: string; // "2:30"
  views: number;
  matchInfo: {
    homeTeam: string;
    awayTeam: string;
    homeTeamLogo: string;
    awayTeamLogo: string;
    date: string;
    score?: string;
  };
  tags: string[]; // ["goal", "save", "red-card"]
  createdAt: string;
}

export interface Statistic {
  id: string;
  title: string;
  value: string | number;
  subtitle?: string;
  icon: string;
  trend?: {
    direction: 'up' | 'down' | 'neutral';
    percentage: number;
    period: string; // "vs last week"
  };
  category: 'goals' | 'matches' | 'players' | 'leagues';
  color: string; // hex color for theming
}

export interface SocialPost {
  id: string;
  platform: 'twitter' | 'instagram' | 'facebook' | 'youtube';
  author: {
    name: string;
    username: string;
    avatar: string;
    verified: boolean;
  };
  content: string;
  media?: {
    type: 'image' | 'video';
    url: string;
    thumbnail?: string;
  };
  engagement: {
    likes: number;
    comments: number;
    shares: number;
  };
  timestamp: string;
  url: string;
  hashtags: string[];
}

export interface EngagementFeaturesData {
  highlights: MatchHighlight[];
  statistics: Statistic[];
  socialPosts: SocialPost[];
}
