// Engagement Features V1 - Component Exports
// This file exports all V1 components and types

// Main component
export { EngagementFeaturesV1 } from './EngagementFeaturesV1';

// Individual components
export { MatchHighlights } from './components/MatchHighlights';
export { StatisticsWidget } from './components/StatisticsWidget';
export { SocialFeed } from './components/SocialFeed';

// Types
export type {
  MatchHighlight,
  Statistic,
  SocialPost,
  EngagementFeaturesData
} from './types';

// Component metadata
export const V1_COMPONENTS = {
  MatchHighlights: {
    name: 'Match Highlights',
    description: 'Video highlights with thumbnails and match information',
    features: ['Video thumbnails', 'Play overlay', 'Match details', 'Tag system', 'View counts']
  },
  StatisticsWidget: {
    name: 'Statistics Widget',
    description: 'Live football statistics with trends and categories',
    features: ['Live metrics', 'Trend indicators', 'Category icons', 'Color coding', 'Featured stats']
  },
  SocialFeed: {
    name: 'Social Feed',
    description: 'Multi-platform social media posts with filtering',
    features: ['Multi-platform', 'Platform filters', 'Rich content', 'User profiles', 'Engagement metrics']
  }
} as const;

// Default props for components
export const DEFAULT_PROPS = {
  MatchHighlights: {
    highlights: [],
    isLoading: false
  },
  StatisticsWidget: {
    statistics: [],
    isLoading: false
  },
  SocialFeed: {
    posts: [],
    isLoading: false
  }
} as const;
