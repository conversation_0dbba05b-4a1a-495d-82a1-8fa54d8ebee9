'use client';

import React, { useState, useEffect } from 'react';
import { MatchHighlights } from './components/MatchHighlights';
import { StatisticsWidget } from './components/StatisticsWidget';
import { SocialFeed } from './components/SocialFeed';
import { EngagementFeaturesData } from './types';

interface EngagementFeaturesV1Props {
  className?: string;
}

export const EngagementFeaturesV1: React.FC<EngagementFeaturesV1Props> = ({
  className = ''
}) => {
  const [data, setData] = useState<EngagementFeaturesData | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Mock data for development
  const mockData: EngagementFeaturesData = {
    highlights: [
      {
        id: '1',
        title: 'Amazing Goal by Messi - Barcelona vs Real Madrid',
        description: 'Incredible solo run and finish',
        thumbnailUrl: 'https://via.placeholder.com/96x64/FF6B6B/FFFFFF?text=Goal',
        videoUrl: '#',
        duration: '0:45',
        views: 2500000,
        matchInfo: {
          homeTeam: 'Barcelona',
          awayTeam: 'Real Madrid',
          homeTeamLogo: 'https://via.placeholder.com/32x32/FF6B6B/FFFFFF?text=BAR',
          awayTeamLogo: 'https://via.placeholder.com/32x32/4ECDC4/FFFFFF?text=RM',
          date: '2024-01-15',
          score: '2-1'
        },
        tags: ['goal', 'skill'],
        createdAt: '2024-01-15T20:30:00Z'
      },
      {
        id: '2',
        title: 'Incredible Save - Goalkeeper of the Year',
        description: 'Last minute penalty save',
        thumbnailUrl: 'https://via.placeholder.com/96x64/4ECDC4/FFFFFF?text=Save',
        videoUrl: '#',
        duration: '0:30',
        views: 1800000,
        matchInfo: {
          homeTeam: 'Liverpool',
          awayTeam: 'Manchester City',
          homeTeamLogo: 'https://via.placeholder.com/32x32/FF6B6B/FFFFFF?text=LIV',
          awayTeamLogo: 'https://via.placeholder.com/32x32/45B7D1/FFFFFF?text=MCI',
          date: '2024-01-14',
          score: '1-0'
        },
        tags: ['save', 'penalty'],
        createdAt: '2024-01-14T18:45:00Z'
      },
      {
        id: '3',
        title: 'Red Card Drama - Controversial Decision',
        description: 'VAR review leads to red card',
        thumbnailUrl: 'https://via.placeholder.com/96x64/FF4757/FFFFFF?text=Card',
        videoUrl: '#',
        duration: '1:15',
        views: 950000,
        matchInfo: {
          homeTeam: 'Chelsea',
          awayTeam: 'Arsenal',
          homeTeamLogo: 'https://via.placeholder.com/32x32/45B7D1/FFFFFF?text=CHE',
          awayTeamLogo: 'https://via.placeholder.com/32x32/FF6B6B/FFFFFF?text=ARS',
          date: '2024-01-13',
          score: '0-0'
        },
        tags: ['red-card', 'var'],
        createdAt: '2024-01-13T16:20:00Z'
      }
    ],
    statistics: [
      {
        id: '1',
        title: 'Goals Today',
        value: 127,
        subtitle: 'Across all leagues',
        icon: '⚽',
        trend: { direction: 'up', percentage: 15, period: 'vs yesterday' },
        category: 'goals',
        color: '#10B981'
      },
      {
        id: '2',
        title: 'Live Matches',
        value: 24,
        subtitle: 'Currently playing',
        icon: '🏟️',
        trend: { direction: 'neutral', percentage: 0, period: 'stable' },
        category: 'matches',
        color: '#3B82F6'
      },
      {
        id: '3',
        title: 'Top Scorer',
        value: 'Haaland',
        subtitle: '28 goals this season',
        icon: '👤',
        trend: { direction: 'up', percentage: 8, period: 'this month' },
        category: 'players',
        color: '#8B5CF6'
      },
      {
        id: '4',
        title: 'Active Leagues',
        value: 156,
        subtitle: 'Worldwide coverage',
        icon: '🏆',
        trend: { direction: 'up', percentage: 3, period: 'vs last month' },
        category: 'leagues',
        color: '#F59E0B'
      },
      {
        id: '5',
        title: 'Fan Engagement',
        value: '2.4M',
        subtitle: 'Social interactions today',
        icon: '📱',
        trend: { direction: 'up', percentage: 22, period: 'vs yesterday' },
        category: 'engagement',
        color: '#EF4444'
      }
    ],
    socialPosts: [
      {
        id: '1',
        platform: 'twitter',
        author: {
          name: 'ESPN FC',
          username: 'espnfc',
          avatar: 'https://via.placeholder.com/40x40/1DA1F2/FFFFFF?text=ESPN',
          verified: true
        },
        content: '🚨 BREAKING: Messi scores his 800th career goal! What a moment in football history! #Messi800 #Football',
        engagement: { likes: 45000, comments: 2300, shares: 8900 },
        timestamp: '2024-01-15T21:00:00Z',
        url: '#',
        hashtags: ['Messi800', 'Football', 'GOAT']
      },
      {
        id: '2',
        platform: 'instagram',
        author: {
          name: 'UEFA',
          username: 'uefa',
          avatar: 'https://via.placeholder.com/40x40/E4405F/FFFFFF?text=UEFA',
          verified: true
        },
        content: 'The Champions League quarter-finals are set! 🏆 Which match are you most excited for?',
        media: {
          type: 'image',
          url: 'https://via.placeholder.com/320x128/4ECDC4/FFFFFF?text=UCL',
          thumbnail: 'https://via.placeholder.com/320x128/4ECDC4/FFFFFF?text=UCL'
        },
        engagement: { likes: 89000, comments: 4500, shares: 12000 },
        timestamp: '2024-01-15T19:30:00Z',
        url: '#',
        hashtags: ['ChampionsLeague', 'UCL', 'Football']
      },
      {
        id: '3',
        platform: 'youtube',
        author: {
          name: 'FIFA',
          username: 'fifa',
          avatar: 'https://via.placeholder.com/40x40/FF0000/FFFFFF?text=FIFA',
          verified: true
        },
        content: 'Watch the best goals from this weekend\'s matches! Subscribe for more highlights 🔥',
        media: {
          type: 'video',
          url: '#',
          thumbnail: 'https://via.placeholder.com/320x128/FF6B6B/FFFFFF?text=Goals'
        },
        engagement: { likes: 156000, comments: 8900, shares: 23000 },
        timestamp: '2024-01-15T17:15:00Z',
        url: '#',
        hashtags: ['Goals', 'Highlights', 'Football']
      }
    ]
  };

  useEffect(() => {
    // Simulate API call
    const loadData = async () => {
      setIsLoading(true);
      // Simulate network delay
      await new Promise(resolve => setTimeout(resolve, 1500));
      setData(mockData);
      setIsLoading(false);
    };

    loadData();
  }, []);

  return (
    <section className={`py-12 bg-gray-50 ${className}`}>
      <div className="container mx-auto px-4 max-w-7xl">
        {/* Section Header */}
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">
            Engagement Hub
          </h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Stay connected with the latest highlights, statistics, and social buzz from the football world
          </p>
        </div>

        {/* Features Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Match Highlights */}
          <div className="lg:col-span-1">
            <MatchHighlights 
              highlights={data?.highlights || []} 
              isLoading={isLoading}
            />
          </div>

          {/* Statistics Widget */}
          <div className="lg:col-span-1">
            <StatisticsWidget 
              statistics={data?.statistics || []} 
              isLoading={isLoading}
            />
          </div>

          {/* Social Feed */}
          <div className="lg:col-span-1">
            <SocialFeed 
              posts={data?.socialPosts || []} 
              isLoading={isLoading}
            />
          </div>
        </div>

        {/* Call to Action */}
        <div className="mt-12 text-center">
          <div className="bg-white rounded-xl border border-gray-200 p-8 max-w-2xl mx-auto">
            <h3 className="text-xl font-bold text-gray-900 mb-4">
              Want More Engagement?
            </h3>
            <p className="text-gray-600 mb-6">
              Join our community for exclusive content, live discussions, and personalized recommendations
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button className="px-6 py-3 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 transition-colors">
                Join Community
              </button>
              <button className="px-6 py-3 border border-gray-300 text-gray-700 font-medium rounded-lg hover:bg-gray-50 transition-colors">
                Learn More
              </button>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};
