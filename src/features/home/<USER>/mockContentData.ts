import type { NewsItem, League, Fixture, Team } from '../types';

// Helper function to generate SVG team logos
const generateTeamLogo = (teamName: string, color: string = '#3b82f6'): string => {
        const initial = teamName.charAt(0).toUpperCase();
        return `data:image/svg+xml,${encodeURIComponent(`
                <svg width="40" height="40" xmlns="http://www.w3.org/2000/svg">
                        <circle cx="20" cy="20" r="18" fill="${color}" stroke="#ffffff" stroke-width="2"/>
                        <text x="20" y="25" text-anchor="middle" fill="white" font-family="Arial" font-size="14" font-weight="bold">
                                ${initial}
                        </text>
                </svg>
        `)}`;
};

// Helper function to generate news placeholder images
const generateNewsImage = (title: string, category: string): string => {
        const colors = {
                transfer: '#10b981',
                league: '#3b82f6',
                general: '#6b7280'
        };
        const color = colors[category as keyof typeof colors] || '#6b7280';
        const icon = category === 'transfer' ? '⚽' : category === 'league' ? '🏆' : '📰';

        return `data:image/svg+xml,${encodeURIComponent(`
                <svg width="400" height="240" xmlns="http://www.w3.org/2000/svg">
                        <rect width="400" height="240" fill="${color}"/>
                        <rect x="20" y="20" width="360" height="200" fill="rgba(255,255,255,0.1)" rx="8"/>
                        <text x="200" y="100" text-anchor="middle" fill="white" font-family="Arial" font-size="48">${icon}</text>
                        <text x="200" y="140" text-anchor="middle" fill="white" font-family="Arial" font-size="16" font-weight="bold">${category.toUpperCase()}</text>
                        <text x="200" y="170" text-anchor="middle" fill="rgba(255,255,255,0.8)" font-family="Arial" font-size="12">${title.substring(0, 40)}${title.length > 40 ? '...' : ''}</text>
                </svg>
        `)}`;
};

// Helper function to generate league logos
const generateLeagueLogo = (leagueName: string, size: number = 60): string => {
        const colors = {
                'Premier League': '#3d195b',
                'La Liga': '#ff6900',
                'Bundesliga': '#d20515',
                'Serie A': '#0066cc',
                'Ligue 1': '#ddd700',
                'UEFA Champions League': '#00336a'
        };
        const color = colors[leagueName as keyof typeof colors] || '#3b82f6';
        const initial = leagueName.charAt(0).toUpperCase();

        return `data:image/svg+xml,${encodeURIComponent(`
                <svg width="${size}" height="${size}" xmlns="http://www.w3.org/2000/svg">
                        <circle cx="${size / 2}" cy="${size / 2}" r="${size / 2 - 2}" fill="${color}" stroke="#ffffff" stroke-width="2"/>
                        <text x="${size / 2}" y="${size / 2 + 5}" text-anchor="middle" fill="white" font-family="Arial" font-size="${size / 3}" font-weight="bold">
                                ${initial}
                        </text>
                </svg>
        `)}`;
};

// Mock Teams
const mockTeams: Record<string, Team> = {
        manchester_united: {
                id: 'mu',
                name: 'Manchester United',
                abbreviation: 'MUN',
                shortName: 'Man United',
                logo: generateTeamLogo('Manchester United', '#e60026')
        },
        liverpool: {
                id: 'liv',
                name: 'Liverpool',
                abbreviation: 'LIV',
                shortName: 'Liverpool',
                logo: generateTeamLogo('Liverpool', '#c8102e')
        },
        chelsea: {
                id: 'che',
                name: 'Chelsea',
                abbreviation: 'CHE',
                shortName: 'Chelsea',
                logo: generateTeamLogo('Chelsea', '#034694')
        },
        arsenal: {
                id: 'ars',
                name: 'Arsenal',
                abbreviation: 'ARS',
                shortName: 'Arsenal',
                logo: generateTeamLogo('Arsenal', '#ef0107')
        },
        manchester_city: {
                id: 'mci',
                name: 'Manchester City',
                abbreviation: 'MCI',
                shortName: 'Man City',
                logo: generateTeamLogo('Manchester City', '#6cabdd')
        },
        tottenham: {
                id: 'tot',
                name: 'Tottenham',
                abbreviation: 'TOT',
                shortName: 'Spurs',
                logo: generateTeamLogo('Tottenham', '#132257')
        },
        barcelona: {
                id: 'bar',
                name: 'Barcelona',
                abbreviation: 'BAR',
                shortName: 'Barca',
                logo: generateTeamLogo('Barcelona', '#a50044')
        },
        real_madrid: {
                id: 'rm',
                name: 'Real Madrid',
                abbreviation: 'RMA',
                shortName: 'Real Madrid',
                logo: generateTeamLogo('Real Madrid', '#febe10')
        },
        bayern_munich: {
                id: 'bay',
                name: 'Bayern Munich',
                abbreviation: 'BAY',
                logo: generateTeamLogo('Bayern Munich', '#dc052d')
        },
        psg: {
                id: 'psg',
                name: 'Paris Saint-Germain',
                abbreviation: 'PSG',
                logo: generateTeamLogo('PSG', '#004170')
        }
};

// Mock News Data
export const mockNewsData: NewsItem[] = [
        {
                id: 'news-1',
                title: 'Manchester United Signs New Star Player in Record-Breaking Deal',
                summary: 'The Red Devils complete a sensational transfer that could reshape their season prospects.',
                content: 'Manchester United has officially announced the signing of...',
                publishedAt: '2025-05-30T10:30:00Z',
                author: 'Sports Reporter',
                category: 'transfer',
                imageUrl: generateNewsImage('Manchester United Signs New Star Player in Record-Breaking Deal', 'transfer'),
                tags: ['Manchester United', 'Transfer', 'Premier League'],
                readTime: 3,
                slug: 'manchester-united-record-signing',
                isFeatured: true,
                priority: 'high'
        },
        {
                id: 'news-2',
                title: 'Champions League Quarter-Finals Draw Reveals Exciting Matchups',
                summary: 'European football elite face off in what promises to be thrilling encounters.',
                content: 'The UEFA Champions League quarter-final draw has produced...',
                publishedAt: '2025-05-30T08:15:00Z',
                author: 'UEFA Correspondent',
                category: 'league',
                imageUrl: generateNewsImage('Champions League Quarter-Finals Draw Revealed', 'league'),
                tags: ['Champions League', 'Draw', 'UEFA'],
                readTime: 4,
                slug: 'champions-league-quarter-finals-draw',
                isFeatured: true,
                priority: 'high'
        },
        {
                id: 'news-3',
                title: 'Premier League Title Race Heats Up as Season Enters Final Stretch',
                summary: 'With just weeks remaining, multiple teams still have a chance at glory.',
                content: 'The Premier League title race remains wide open...',
                publishedAt: '2025-05-30T06:45:00Z',
                author: 'League Analyst',
                category: 'league',
                imageUrl: generateNewsImage('Premier League Title Race Intensifies', 'league'),
                tags: ['Premier League', 'Title Race', 'Analysis'],
                readTime: 5,
                slug: 'premier-league-title-race-analysis',
                isFeatured: false,
                priority: 'medium'
        },
        {
                id: 'news-4',
                title: 'Rising Young Talent Catches Eye of Top European Clubs',
                summary: 'The 19-year-old midfielder has impressed scouts with exceptional performances.',
                content: 'A young talent from the academy has been making waves...',
                publishedAt: '2025-05-29T20:30:00Z',
                author: 'Youth Football Expert',
                category: 'transfer',
                imageUrl: generateNewsImage('Young Talent Attracts European Clubs Interest', 'transfer'),
                tags: ['Youth', 'Transfer', 'European Football'],
                readTime: 2,
                slug: 'young-talent-european-clubs-interest',
                isFeatured: false,
                priority: 'medium'
        },
        {
                id: 'news-5',
                title: 'VAR Controversy Sparks Debate After Weekend Matches',
                summary: 'Several controversial decisions have reignited discussions about technology in football.',
                content: 'The Video Assistant Referee system came under scrutiny...',
                publishedAt: '2025-05-29T18:00:00Z',
                author: 'Rules Expert',
                category: 'general',
                imageUrl: generateNewsImage('VAR Controversy Dominates Weekend Matches', 'general'),
                tags: ['VAR', 'Controversy', 'Rules'],
                readTime: 3,
                slug: 'var-controversy-weekend-matches',
                isFeatured: false,
                priority: 'low'
        },
        {
                id: 'news-6',
                title: 'International Break Brings Injury Concerns for Club Managers',
                summary: 'Several key players picked up knocks while on international duty.',
                content: 'The latest international break has left club managers...',
                publishedAt: '2025-05-29T15:20:00Z',
                author: 'Medical Correspondent',
                category: 'general',
                imageUrl: generateNewsImage('International Break Brings Injury Concerns', 'general'),
                tags: ['International Break', 'Injuries', 'Clubs'],
                readTime: 4,
                slug: 'international-break-injury-concerns',
                isFeatured: false,
                priority: 'medium'
        }
];

// Mock Leagues Data
export const mockLeaguesData: League[] = [
        {
                id: 'premier-league',
                name: 'Premier League',
                logo: generateLeagueLogo('Premier League', 60),
                country: 'England',
                season: '2024-25',
                description: 'The top tier of English football',
                teams: 20,
                currentMatchday: 35,
                totalMatchdays: 38,
                status: 'active',
                startDate: '2024-08-17',
                endDate: '2025-05-25'
        },
        {
                id: 'la-liga',
                name: 'La Liga',
                logo: generateLeagueLogo('La Liga', 60),
                country: 'Spain',
                season: '2024-25',
                description: 'Primera División - Spanish top flight',
                teams: 20,
                currentMatchday: 36,
                totalMatchdays: 38,
                status: 'active',
                startDate: '2024-08-18',
                endDate: '2025-05-25'
        },
        {
                id: 'bundesliga',
                name: 'Bundesliga',
                logo: generateLeagueLogo('Bundesliga', 60),
                country: 'Germany',
                season: '2024-25',
                description: 'German top division football',
                teams: 18,
                currentMatchday: 32,
                totalMatchdays: 34,
                status: 'active',
                startDate: '2024-08-23',
                endDate: '2025-05-24'
        },
        {
                id: 'serie-a',
                name: 'Serie A',
                logo: generateLeagueLogo('Serie A', 60),
                country: 'Italy',
                season: '2024-25',
                description: 'Italian Serie A championship',
                teams: 20,
                currentMatchday: 36,
                totalMatchdays: 38,
                status: 'active',
                startDate: '2024-08-18',
                endDate: '2025-05-25'
        },
        {
                id: 'ligue-1',
                name: 'Ligue 1',
                logo: generateLeagueLogo('Ligue 1', 60),
                country: 'France',
                season: '2024-25',
                description: 'French top tier football',
                teams: 18,
                currentMatchday: 33,
                totalMatchdays: 34,
                status: 'active',
                startDate: '2024-08-16',
                endDate: '2025-05-24'
        },
        {
                id: 'champions-league',
                name: 'UEFA Champions League',
                logo: generateLeagueLogo('UEFA Champions League', 60),
                country: 'Europe',
                season: '2024-25',
                description: 'Premier European club competition',
                teams: 32,
                currentMatchday: 8,
                totalMatchdays: 13,
                status: 'active',
                startDate: '2024-09-17',
                endDate: '2025-06-01'
        }
];

// Mock Fixtures Data
export const mockFixturesData: Fixture[] = [
        {
                id: 'fixture-1',
                homeTeam: mockTeams.manchester_united,
                awayTeam: mockTeams.liverpool,
                scheduledTime: '2025-05-30T15:00:00Z',
                date: '2025-05-30T15:00:00Z',
                venue: 'Old Trafford',
                league: {
                        id: 'premier-league',
                        name: 'Premier League',
                        logo: generateLeagueLogo('Premier League', 24)
                },
                status: 'scheduled',
                round: 'Matchday 35',
                importance: 'high'
        },
        {
                id: 'fixture-2',
                homeTeam: mockTeams.chelsea,
                awayTeam: mockTeams.arsenal,
                scheduledTime: '2025-05-30T17:30:00Z',
                date: '2025-05-30T17:30:00Z',
                venue: 'Stamford Bridge',
                league: {
                        id: 'premier-league',
                        name: 'Premier League',
                        logo: generateLeagueLogo('Premier League', 24)
                },
                status: 'scheduled',
                round: 'Matchday 35',
                importance: 'high'
        },
        {
                id: 'fixture-3',
                homeTeam: mockTeams.manchester_city,
                awayTeam: mockTeams.tottenham,
                scheduledTime: '2025-05-30T12:30:00Z',
                date: '2025-05-30T12:30:00Z',
                venue: 'Etihad Stadium',
                league: {
                        id: 'premier-league',
                        name: 'Premier League',
                        logo: generateLeagueLogo('Premier League', 24)
                },
                status: 'live',
                homeScore: 2,
                awayScore: 1,
                matchTime: '67\'',
                minute: 67,
                round: 'Matchday 35',
                importance: 'high'
        },
        {
                id: 'fixture-4',
                homeTeam: mockTeams.barcelona,
                awayTeam: mockTeams.real_madrid,
                scheduledTime: '2025-05-31T20:00:00Z',
                date: '2025-05-31T20:00:00Z',
                venue: 'Camp Nou',
                league: {
                        id: 'la-liga',
                        name: 'La Liga',
                        logo: generateLeagueLogo('La Liga', 24)
                },
                status: 'scheduled',
                round: 'Matchday 36',
                importance: 'high'
        },
        {
                id: 'fixture-5',
                homeTeam: mockTeams.bayern_munich,
                awayTeam: mockTeams.psg,
                scheduledTime: '2025-06-01T19:45:00Z',
                date: '2025-06-01T19:45:00Z',
                venue: 'Allianz Arena',
                league: {
                        id: 'champions-league',
                        name: 'UEFA Champions League',
                        logo: generateLeagueLogo('UEFA Champions League', 24)
                },
                status: 'scheduled',
                round: 'Quarter-Final',
                importance: 'high'
        },
        {
                id: 'fixture-6',
                homeTeam: mockTeams.arsenal,
                awayTeam: mockTeams.manchester_united,
                scheduledTime: '2025-05-28T20:00:00Z',
                date: '2025-05-28T20:00:00Z',
                venue: 'Emirates Stadium',
                league: {
                        id: 'premier-league',
                        name: 'Premier League',
                        logo: generateLeagueLogo('Premier League', 24)
                },
                status: 'finished',
                homeScore: 3,
                awayScore: 1,
                round: 'Matchday 34',
                importance: 'medium'
        }
];
