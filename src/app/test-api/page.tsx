'use client';

import { useWorkingFixtures } from '@/features/home/<USER>/hero/v5/hooks/useWorkingFixtures';

export default function TestApiPage() {
  console.log('🧪 TestApiPage component rendering...');

  const { fixtures, isLoading, error } = useWorkingFixtures();

  console.log('🎯 Test Working Hook result:', { fixtures: fixtures.length, isLoading, error });

  return (
    <div className="p-8">
      <h1 className="text-2xl font-bold mb-4">API Test Page</h1>

      <div className="bg-gray-100 p-4 rounded">
        <h2 className="font-bold">Hook Status:</h2>
        <p>Loading: {isLoading ? 'Yes' : 'No'}</p>
        <p>Error: {error || 'None'}</p>
        <p>Fixtures Count: {fixtures.length}</p>

        {fixtures.length > 0 && (
          <div className="mt-4">
            <h3 className="font-bold">Sample Fixture:</h3>
            <pre className="text-xs bg-white p-2 rounded mt-2">
              {JSON.stringify(fixtures[0], null, 2)}
            </pre>
          </div>
        )}
      </div>
    </div>
  );
}
