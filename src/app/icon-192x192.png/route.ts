import { NextResponse } from 'next/server';

export async function GET() {
  // Generate SVG icon
  const svg = `
    <svg width="192" height="192" xmlns="http://www.w3.org/2000/svg">
      <rect width="192" height="192" fill="#3b82f6"/>
      <circle cx="96" cy="96" r="60" fill="white"/>
      <text x="96" y="110" text-anchor="middle" fill="#3b82f6" font-family="Arial" font-size="48" font-weight="bold">⚽</text>
    </svg>
  `;

  return new NextResponse(svg, {
    headers: {
      'Content-Type': 'image/svg+xml',
      'Cache-Control': 'public, max-age=31536000, immutable',
    },
  });
}
