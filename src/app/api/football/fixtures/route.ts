import { NextRequest, NextResponse } from 'next/server';

/**
 * Next.js API Proxy for Football Fixtures
 * Protects the main API by proxying requests through Next.js
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const page = searchParams.get('page') || '1';
    const limit = searchParams.get('limit') || '100';
    const league = searchParams.get('league');
    const date = searchParams.get('date');
    const leagueId = searchParams.get('leagueId');

    // Use localhost:3000 for real API data
    const apiBaseUrl = 'http://localhost:3000';

    // Build API URL with backend filtering support
    const apiParams = new URLSearchParams();
    apiParams.append('page', page);
    apiParams.append('limit', limit);

    // Add backend filtering parameters
    if (date) {
      apiParams.append('date', date);
      console.log('🗓️ Backend filtering by date:', date);
    }

    if (leagueId) {
      apiParams.append('league', leagueId);
      console.log('🏆 Backend filtering by leagueId:', leagueId);
    } else if (league) {
      // Legacy support: if league name is provided, we'll need client-side filtering
      console.log('⚠️ Using league name for client-side filtering:', league);
    }

    // Choose endpoint based on filtering
    const endpoint = (date || leagueId) ? 'football/fixtures' : 'football/fixtures/upcoming-and-live';
    const apiUrl = `${apiBaseUrl}/${endpoint}?${apiParams.toString()}`;

    console.log('🔌 Proxying API request to:', apiUrl);

    // Fetch data from the main API
    const response = await fetch(apiUrl, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'NextJS-Proxy/1.0',
      },
      // Add timeout
      signal: AbortSignal.timeout(10000), // 10 seconds timeout
    });

    if (!response.ok) {
      console.error('❌ API request failed:', response.status, response.statusText);
      return NextResponse.json(
        {
          error: 'Failed to fetch fixtures',
          status: response.status,
          statusText: response.statusText
        },
        { status: response.status }
      );
    }

    const data = await response.json();

    console.log('✅ API request successful, fixtures count:', data.data?.length || 0);

    // Apply client-side filtering only for legacy league name support
    let filteredData = data;
    if (league && !leagueId && data.data) {
      const filtered = data.data.filter((fixture: any) =>
        fixture.leagueName.toLowerCase() === league.toLowerCase()
      );
      filteredData = {
        ...data,
        data: filtered,
        meta: {
          ...data.meta,
          totalItems: filtered.length,
          totalPages: Math.ceil(filtered.length / parseInt(limit))
        }
      };
      console.log('🔍 Client-side filtered by league name:', league, '→', filtered.length, 'fixtures');
    }

    // Return the data with proper CORS headers
    return NextResponse.json(filteredData, {
      status: 200,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        'Cache-Control': 'public, s-maxage=30, stale-while-revalidate=60', // Cache for 30 seconds
      },
    });

  } catch (error) {
    console.error('❌ API Proxy Error:', error);

    // Handle timeout errors
    if (error instanceof Error && error.name === 'TimeoutError') {
      return NextResponse.json(
        { error: 'API request timeout' },
        { status: 504 }
      );
    }

    return NextResponse.json(
      {
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// Handle OPTIONS requests for CORS
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
