"use client";

import React, { useState } from 'react';

export const SearchBox: React.FC = () => {
        const [searchTerm, setSearchTerm] = useState('');

        const handleSearch = (e: React.FormEvent) => {
                e.preventDefault();
                if (searchTerm.trim()) {
                        // Implement search functionality
                        console.log('Searching for:', searchTerm);
                        // TODO: Navigate to search results page
                        // router.push(`/search?q=${encodeURIComponent(searchTerm)}`);
                }
        };

        return (
                <form onSubmit={handleSearch} className="relative w-full md:w-auto">
                        <input
                                type="text"
                                value={searchTerm}
                                onChange={(e) => setSearchTerm(e.target.value)}
                                placeholder="Tìm kiếm..."
                                className="w-full md:w-64 px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
                        />
                        <button
                                type="submit"
                                className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-blue-600"
                        >
                                <svg
                                        className="w-5 h-5"
                                        fill="none"
                                        stroke="currentColor"
                                        viewBox="0 0 24 24"
                                >
                                        <path
                                                strokeLinecap="round"
                                                strokeLinejoin="round"
                                                strokeWidth={2}
                                                d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                                        />
                                </svg>
                        </button>
                </form>
        );
};
