import React from 'react';
import { Logo } from './Logo';
import { Navigation } from './Navigation';
import { SearchBox } from './SearchBox';

export const Header: React.FC = () => (
        <header className="bg-white shadow-md border-b border-gray-200 relative z-30">
                <div className="container mx-auto px-4 py-3">
                        <div className="flex items-center justify-between">
                                <Logo />

                                {/* Desktop: Show both Navigation and SearchBox */}
                                <div className="hidden md:flex items-center space-x-8">
                                        <Navigation />
                                        <SearchBox />
                                </div>

                                {/* Mobile: Show Navigation button only */}
                                <div className="md:hidden">
                                        <Navigation />
                                </div>
                        </div>

                        {/* Mobile SearchBox - Below header on mobile */}
                        <div className="mt-3 md:hidden">
                                <SearchBox />
                        </div>
                </div>
        </header>
);
