"use client";

import React, { useState } from 'react';
import Link from 'next/link';

const menuItems = [
        { href: '/', label: 'Home' },
        { href: '/news', label: 'News' },
        { href: '/fixtures', label: 'Fixtures' },
        { href: '/leagues', label: 'League' },
        { href: '/highlights', label: 'Highlight Video' },
        { href: '/videos', label: 'Video' },
];

export const Navigation: React.FC = () => {
        const [isMenuOpen, setIsMenuOpen] = useState(false);

        return (
                <>
                        {/* Desktop Navigation */}
                        <nav className="hidden md:flex space-x-8">
                                {menuItems.map((item) => (
                                        <Link
                                                key={item.href}
                                                href={item.href}
                                                className="text-gray-700 hover:text-blue-600 font-medium transition-colors"
                                        >
                                                {item.label}
                                        </Link>
                                ))}
                        </nav>

                        {/* Mobile Menu Button */}
                        <button
                                className="md:hidden flex flex-col justify-center items-center w-8 h-8 space-y-1"
                                onClick={() => setIsMenuOpen(!isMenuOpen)}
                                aria-label="Toggle menu"
                        >
                                <span className={`w-6 h-0.5 bg-gray-700 transition-all duration-300 ${isMenuOpen ? 'rotate-45 translate-y-1.5' : ''}`} />
                                <span className={`w-6 h-0.5 bg-gray-700 transition-all duration-300 ${isMenuOpen ? 'opacity-0' : ''}`} />
                                <span className={`w-6 h-0.5 bg-gray-700 transition-all duration-300 ${isMenuOpen ? '-rotate-45 -translate-y-1.5' : ''}`} />
                        </button>

                        {/* Mobile Menu Overlay */}
                        {isMenuOpen && (
                                <div
                                        className="fixed inset-0 bg-black bg-opacity-50 z-40 md:hidden"
                                        onClick={() => setIsMenuOpen(false)}
                                />
                        )}

                        {/* Mobile Menu */}
                        <nav className={`fixed top-0 right-0 h-full w-64 bg-white shadow-lg transform transition-transform duration-300 z-50 md:hidden ${isMenuOpen ? 'translate-x-0' : 'translate-x-full'}`}>
                                <div className="flex flex-col p-6 space-y-6">
                                        <div className="flex justify-between items-center mb-4">
                                                <h2 className="text-lg font-bold text-gray-800">Menu</h2>
                                                <button
                                                        onClick={() => setIsMenuOpen(false)}
                                                        className="w-8 h-8 flex items-center justify-center text-gray-600 hover:text-gray-800"
                                                        aria-label="Close menu"
                                                >
                                                        <span className="text-2xl">&times;</span>
                                                </button>
                                        </div>
                                        {menuItems.map((item) => (
                                                <Link
                                                        key={item.href}
                                                        href={item.href}
                                                        className="text-gray-700 hover:text-blue-600 font-medium transition-colors py-2 border-b border-gray-100 last:border-b-0"
                                                        onClick={() => setIsMenuOpen(false)}
                                                >
                                                        {item.label}
                                                </Link>
                                        ))}
                                </div>
                        </nav>
                </>
        );
};
