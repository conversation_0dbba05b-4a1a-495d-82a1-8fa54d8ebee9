/**
 * Date utility functions for formatting dates and times
 */

/**
 * Format date to readable string (e.g., "Dec 25")
 */
export const formatDate = (dateInput: Date | string): string => {
        const date = typeof dateInput === 'string' ? new Date(dateInput) : dateInput;
        const today = new Date();
        const tomorrow = new Date(today);
        tomorrow.setDate(tomorrow.getDate() + 1);
        const yesterday = new Date(today);
        yesterday.setDate(yesterday.getDate() - 1);

        // Check if date is today, tomorrow, or yesterday
        if (date.toDateString() === today.toDateString()) {
                return 'Today';
        } else if (date.toDateString() === tomorrow.toDateString()) {
                return 'Tomorrow';
        } else if (date.toDateString() === yesterday.toDateString()) {
                return 'Yesterday';
        } else {
                return date.toLocaleDateString('en-US', {
                        month: 'short',
                        day: 'numeric'
                });
        }
};

/**
 * Format time to readable string (e.g., "15:30")
 */
export const formatTime = (dateInput: Date | string): string => {
        const date = typeof dateInput === 'string' ? new Date(dateInput) : dateInput;
        return date.toLocaleTimeString('en-US', {
                hour: '2-digit',
                minute: '2-digit',
                hour12: false
        });
};

/**
 * Format full date and time (e.g., "Dec 25, 2024 at 15:30")
 */
export const formatDateTime = (date: Date): string => {
        const dateStr = date.toLocaleDateString('en-US', {
                month: 'short',
                day: 'numeric',
                year: 'numeric'
        });
        const timeStr = formatTime(date);
        return `${dateStr} at ${timeStr}`;
};

/**
 * Check if date is today
 */
export const isToday = (date: Date): boolean => {
        const today = new Date();
        return date.toDateString() === today.toDateString();
};

/**
 * Get relative time string (e.g., "in 2 hours", "3 days ago")
 */
export const getRelativeTime = (date: Date): string => {
        const now = new Date();
        const diffMs = date.getTime() - now.getTime();
        const diffMins = Math.floor(diffMs / (1000 * 60));
        const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
        const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

        if (Math.abs(diffMins) < 60) {
                return diffMins > 0 ? `in ${diffMins} min` : `${Math.abs(diffMins)} min ago`;
        } else if (Math.abs(diffHours) < 24) {
                return diffHours > 0 ? `in ${diffHours}h` : `${Math.abs(diffHours)}h ago`;
        } else {
                return diffDays > 0 ? `in ${diffDays} days` : `${Math.abs(diffDays)} days ago`;
        }
};

/**
 * Format a date to show relative time (e.g., "2 hours ago")
 */
export function formatDistanceToNow(dateInput: string | Date): string {
        const date = typeof dateInput === 'string' ? new Date(dateInput) : dateInput;
        const now = new Date();
        const diffInMs = now.getTime() - date.getTime();
        const diffInMinutes = Math.floor(diffInMs / (1000 * 60));
        const diffInHours = Math.floor(diffInMs / (1000 * 60 * 60));
        const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24));

        if (diffInMinutes < 1) {
                return 'Just now';
        } else if (diffInMinutes < 60) {
                return `${diffInMinutes} minute${diffInMinutes !== 1 ? 's' : ''} ago`;
        } else if (diffInHours < 24) {
                return `${diffInHours} hour${diffInHours !== 1 ? 's' : ''} ago`;
        } else if (diffInDays < 7) {
                return `${diffInDays} day${diffInDays !== 1 ? 's' : ''} ago`;
        } else {
                return date.toLocaleDateString('en-US', {
                        year: 'numeric',
                        month: 'short',
                        day: 'numeric'
                });
        }
}


