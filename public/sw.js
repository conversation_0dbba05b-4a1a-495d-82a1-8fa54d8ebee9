// Enhanced Service Worker for Sports Arena V4
const CACHE_NAME = 'sports-arena-v4-enhanced-v1.0.0';
const STATIC_CACHE_NAME = 'sports-arena-static-v1.0.0';
const DYNAMIC_CACHE_NAME = 'sports-arena-dynamic-v1.0.0';
const API_CACHE_NAME = 'sports-arena-api-v1.0.0';

// Cache strategies
const CACHE_STRATEGIES = {
        CACHE_FIRST: 'cache-first',
        NETWORK_FIRST: 'network-first',
        STALE_WHILE_REVALIDATE: 'stale-while-revalidate',
        NETWORK_ONLY: 'network-only',
        CACHE_ONLY: 'cache-only'
};

// Static assets to cache immediately
const STATIC_ASSETS = [
        '/',
        '/manifest.json',
        '/_next/static/css/',
        '/_next/static/js/',
        '/favicon.ico',
        '/icon-192x192.png',
        '/icon-512x512.png'
];

// API endpoints to cache with strategy
const API_CACHE_PATTERNS = [
        { pattern: /\/api\/matches/, strategy: CACHE_STRATEGIES.NETWORK_FIRST, maxAge: 300000 }, // 5 minutes
        { pattern: /\/api\/leagues/, strategy: CACHE_STRATEGIES.CACHE_FIRST, maxAge: 3600000 }, // 1 hour
        { pattern: /\/api\/news/, strategy: CACHE_STRATEGIES.STALE_WHILE_REVALIDATE, maxAge: 1800000 }, // 30 minutes
        { pattern: /\/api\/live/, strategy: CACHE_STRATEGIES.NETWORK_ONLY }, // Always fresh
        { pattern: /\/api\/odds/, strategy: CACHE_STRATEGIES.NETWORK_FIRST, maxAge: 60000 } // 1 minute
];

// Performance metrics tracking
let performanceMetrics = {
        cacheHits: 0,
        cacheMisses: 0,
        networkRequests: 0,
        averageResponseTime: 0,
        totalRequests: 0
};

// Install event - cache static assets
self.addEventListener('install', event => {
        console.log('[SW] Installing enhanced service worker v1.0.0');

        event.waitUntil(
                Promise.all([
                        caches.open(STATIC_CACHE_NAME).then(cache => {
                                return cache.addAll(STATIC_ASSETS);
                        }),
                        caches.open(DYNAMIC_CACHE_NAME),
                        caches.open(API_CACHE_NAME)
                ]).then(() => {
                        console.log('[SW] All caches initialized');
                        return self.skipWaiting();
                })
        );
});

// Activate event - clean old caches
self.addEventListener('activate', event => {
        console.log('[SW] Activating enhanced service worker');

        event.waitUntil(
                caches.keys().then(cacheNames => {
                        return Promise.all(
                                cacheNames.map(cacheName => {
                                        if (!cacheName.includes('v1.0.0')) {
                                                console.log('[SW] Deleting old cache:', cacheName);
                                                return caches.delete(cacheName);
                                        }
                                })
                        );
                }).then(() => {
                        return self.clients.claim();
                })
        );
});

// Fetch event - intelligent caching strategy
self.addEventListener('fetch', event => {
        const { request } = event;
        const url = new URL(request.url);

        // Skip non-GET requests
        if (request.method !== 'GET') {
                return;
        }

        // Handle different types of requests
        if (url.pathname.startsWith('/api/')) {
                event.respondWith(handleApiRequest(request));
        } else if (url.pathname.startsWith('/_next/static/')) {
                event.respondWith(handleStaticAssets(request));
        } else if (url.pathname.match(/\.(js|css|woff2?|png|jpg|jpeg|gif|svg|ico)$/)) {
                event.respondWith(handleStaticAssets(request));
        } else {
                event.respondWith(handlePageRequest(request));
        }
});

// Handle API requests with intelligent caching
async function handleApiRequest(request) {
        const startTime = Date.now();
        performanceMetrics.totalRequests++;

        const url = new URL(request.url);
        const cacheConfig = API_CACHE_PATTERNS.find(config => config.pattern.test(url.pathname));

        if (!cacheConfig) {
                performanceMetrics.networkRequests++;
                return fetch(request);
        }

        const cache = await caches.open(API_CACHE_NAME);
        const cachedResponse = await cache.match(request);

        switch (cacheConfig.strategy) {
                case CACHE_STRATEGIES.CACHE_FIRST:
                        if (cachedResponse && !isExpired(cachedResponse, cacheConfig.maxAge)) {
                                performanceMetrics.cacheHits++;
                                return cachedResponse;
                        }
                        break;

                case CACHE_STRATEGIES.NETWORK_FIRST:
                        try {
                                const networkResponse = await fetch(request);
                                if (networkResponse.ok) {
                                        performanceMetrics.networkRequests++;
                                        const responseClone = networkResponse.clone();
                                        await cache.put(request, responseClone);
                                        updatePerformanceMetrics(startTime);
                                        return networkResponse;
                                }
                        } catch (error) {
                                console.log('[SW] Network failed, falling back to cache');
                        }

                        if (cachedResponse) {
                                performanceMetrics.cacheHits++;
                                return cachedResponse;
                        }
                        break;

                case CACHE_STRATEGIES.STALE_WHILE_REVALIDATE:
                        if (cachedResponse) {
                                performanceMetrics.cacheHits++;
                                // Return cached version immediately
                                fetch(request).then(networkResponse => {
                                        if (networkResponse.ok) {
                                                cache.put(request, networkResponse.clone());
                                        }
                                }).catch(() => { });
                                return cachedResponse;
                        }
                        break;

                case CACHE_STRATEGIES.NETWORK_ONLY:
                        performanceMetrics.networkRequests++;
                        return fetch(request);
        }

        // Fallback to network
        performanceMetrics.networkRequests++;
        const response = await fetch(request);
        updatePerformanceMetrics(startTime);
        return response;
}

// Handle static assets with cache-first strategy
async function handleStaticAssets(request) {
        const cache = await caches.open(STATIC_CACHE_NAME);
        const cachedResponse = await cache.match(request);

        if (cachedResponse) {
                performanceMetrics.cacheHits++;
                return cachedResponse;
        }

        try {
                performanceMetrics.networkRequests++;
                const networkResponse = await fetch(request);
                if (networkResponse.ok) {
                        await cache.put(request, networkResponse.clone());
                }
                return networkResponse;
        } catch (error) {
                performanceMetrics.cacheMisses++;
                console.log('[SW] Failed to fetch static asset:', request.url);
                throw error;
        }
}

// Handle page requests with network-first strategy
async function handlePageRequest(request) {
        const cache = await caches.open(DYNAMIC_CACHE_NAME);

        try {
                performanceMetrics.networkRequests++;
                const networkResponse = await fetch(request);
                if (networkResponse.ok) {
                        await cache.put(request, networkResponse.clone());
                }
                return networkResponse;
        } catch (error) {
                const cachedResponse = await cache.match(request);
                if (cachedResponse) {
                        performanceMetrics.cacheHits++;
                        return cachedResponse;
                }

                // Return offline page or error
                performanceMetrics.cacheMisses++;
                return new Response('Offline - Please check your connection', {
                        status: 503,
                        statusText: 'Service Unavailable',
                        headers: { 'Content-Type': 'text/plain' }
                });
        }
}

// Check if cached response is expired
function isExpired(response, maxAge) {
        if (!maxAge) return false;

        const cachedTime = response.headers.get('sw-cached-time');
        if (!cachedTime) return true;

        return Date.now() - parseInt(cachedTime) > maxAge;
}

// Update performance metrics
function updatePerformanceMetrics(startTime) {
        const responseTime = Date.now() - startTime;
        performanceMetrics.averageResponseTime =
                (performanceMetrics.averageResponseTime + responseTime) / 2;
}

// Background sync for offline actions
self.addEventListener('sync', event => {
        if (event.tag === 'background-sync-betting') {
                event.waitUntil(syncBettingData());
        } else if (event.tag === 'background-sync-analytics') {
                event.waitUntil(syncAnalyticsData());
        }
});

// Sync betting data when back online
async function syncBettingData() {
        try {
                const pendingBets = await getStoredData('pending-bets');
                if (pendingBets && pendingBets.length > 0) {
                        for (const bet of pendingBets) {
                                await fetch('/api/bets', {
                                        method: 'POST',
                                        body: JSON.stringify(bet),
                                        headers: { 'Content-Type': 'application/json' }
                                });
                        }
                        await clearStoredData('pending-bets');
                        console.log('[SW] Synced pending bets');
                }
        } catch (error) {
                console.error('[SW] Failed to sync betting data:', error);
        }
}

// Sync analytics data when back online
async function syncAnalyticsData() {
        try {
                const pendingAnalytics = await getStoredData('pending-analytics');
                if (pendingAnalytics && pendingAnalytics.length > 0) {
                        await fetch('/api/analytics', {
                                method: 'POST',
                                body: JSON.stringify(pendingAnalytics),
                                headers: { 'Content-Type': 'application/json' }
                        });
                        await clearStoredData('pending-analytics');
                        console.log('[SW] Synced analytics data');
                }
        } catch (error) {
                console.error('[SW] Failed to sync analytics data:', error);
        }
}

// IndexedDB helpers
async function getStoredData(key) {
        return new Promise((resolve, reject) => {
                const request = indexedDB.open('SportsArenaDB', 1);
                request.onsuccess = () => {
                        const db = request.result;
                        const transaction = db.transaction(['cache'], 'readonly');
                        const store = transaction.objectStore('cache');
                        const getRequest = store.get(key);
                        getRequest.onsuccess = () => resolve(getRequest.result?.data);
                        getRequest.onerror = () => reject(getRequest.error);
                };
        });
}

async function clearStoredData(key) {
        return new Promise((resolve, reject) => {
                const request = indexedDB.open('SportsArenaDB', 1);
                request.onsuccess = () => {
                        const db = request.result;
                        const transaction = db.transaction(['cache'], 'readwrite');
                        const store = transaction.objectStore('cache');
                        const deleteRequest = store.delete(key);
                        deleteRequest.onsuccess = () => resolve();
                        deleteRequest.onerror = () => reject(deleteRequest.error);
                };
        });
}

// Push notification handling
self.addEventListener('push', event => {
        if (!event.data) return;

        const data = event.data.json();
        const options = {
                body: data.body,
                icon: '/icon-192x192.png',
                badge: '/icon-192x192.png',
                image: data.image,
                data: data.data,
                tag: data.tag || 'sports-update',
                requireInteraction: data.priority === 'high',
                actions: [
                        {
                                action: 'view',
                                title: 'View Details',
                                icon: '/icon-192x192.png'
                        },
                        {
                                action: 'dismiss',
                                title: 'Dismiss'
                        }
                ]
        };

        event.waitUntil(
                self.registration.showNotification(data.title, options)
        );
});

// Notification click handling
self.addEventListener('notificationclick', event => {
        event.notification.close();

        if (event.action === 'view') {
                const urlToOpen = event.notification.data?.url || '/';
                event.waitUntil(
                        clients.matchAll().then(clientList => {
                                const client = clientList.find(c =>
                                        c.url.includes(urlToOpen) && 'focus' in c
                                );

                                if (client) {
                                        return client.focus();
                                } else {
                                        return clients.openWindow(urlToOpen);
                                }
                        })
                );
        }
});

// Performance monitoring message
self.addEventListener('message', event => {
        if (event.data.type === 'GET_PERFORMANCE_METRICS') {
                event.ports[0].postMessage({
                        type: 'PERFORMANCE_METRICS',
                        data: {
                                ...performanceMetrics,
                                cacheHitRate: performanceMetrics.totalRequests > 0
                                        ? (performanceMetrics.cacheHits / performanceMetrics.totalRequests * 100).toFixed(2)
                                        : 0
                        }
                });
        }
});

console.log('[SW] Enhanced Sports Arena Service Worker v1.0.0 loaded');
