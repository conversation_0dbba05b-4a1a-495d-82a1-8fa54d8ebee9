{"name": "API Sports Game V2 - Enhanced Arena", "short_name": "SportArenaV2", "description": "Next-generation sports betting and live match arena with real-time analytics", "start_url": "/", "display": "standalone", "background_color": "#0f172a", "theme_color": "#3b82f6", "orientation": "portrait-primary", "categories": ["sports", "entertainment", "lifestyle"], "lang": "en", "dir": "ltr", "scope": "/", "icons": [{"src": "/icon-192x192.png", "sizes": "192x192", "type": "image/png", "purpose": "maskable any"}, {"src": "/icon-512x512.png", "sizes": "512x512", "type": "image/png", "purpose": "maskable any"}], "shortcuts": [{"name": "Live Matches", "short_name": "Live", "description": "View live matches and scores", "url": "/?view=live", "icons": [{"src": "/icon-192x192.png", "sizes": "192x192"}]}, {"name": "Upcoming Matches", "short_name": "Upcoming", "description": "View upcoming fixtures", "url": "/?view=upcoming", "icons": [{"src": "/icon-192x192.png", "sizes": "192x192"}]}, {"name": "Arena View", "short_name": "Arena", "description": "3D Arena visualization", "url": "/?view=arena", "icons": [{"src": "/icon-192x192.png", "sizes": "192x192"}]}], "screenshots": [{"src": "/screenshot-desktop.png", "sizes": "1280x720", "type": "image/png", "form_factor": "wide", "label": "Desktop Arena View"}, {"src": "/screenshot-mobile.png", "sizes": "375x667", "type": "image/png", "form_factor": "narrow", "label": "Mobile Arena View"}], "prefer_related_applications": false, "related_applications": [], "edge_side_panel": {"preferred_width": 400}, "handle_links": "preferred", "launch_handler": {"client_mode": "focus-existing"}}